/* [project]/src/Style/Marquee.css [app-client] (css) */
.marquee-container {
  color: #fff;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  justify-content: center;
  align-items: center;
  min-height: 120px;
  padding: 20px 0;
  font-family: Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  position: relative;
  overflow: hidden;
}

.marquee-container:before {
  content: "";
  pointer-events: none;
  z-index: 2;
  background: linear-gradient(90deg, #0000001a 0%, #0000 10% 90%, #0000001a 100%);
  position: absolute;
  inset: 0;
}

.wrapper {
  align-items: center;
  width: 100%;
  height: 80px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.marquee-item {
  cursor: pointer;
  backdrop-filter: blur(10px);
  border: 1px solid #fff3;
  border-radius: 12px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  margin: 0 15px;
  transition: all .3s;
  display: flex;
  position: relative;
  box-shadow: 0 4px 15px #0000001a;
}

.marquee-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #0003;
}

.marquee-content {
  color: #fff;
  text-shadow: 0 2px 4px #0000004d;
  white-space: nowrap;
  letter-spacing: .5px;
  padding: 15px 25px;
  font-size: 18px;
  font-weight: 600;
}

@media (width <= 768px) {
  .marquee-content {
    padding: 12px 20px;
    font-size: 16px;
  }

  .marquee-container {
    min-height: 100px;
  }

  .wrapper {
    height: 60px;
  }
}

@media (width <= 480px) {
  .marquee-content {
    padding: 10px 15px;
    font-size: 14px;
  }

  .marquee-item {
    margin: 0 10px;
  }
}

.MarqueeBox {
  color: #fff;
  text-align: center;
  background: #111;
  justify-content: center;
  align-items: center;
  height: 20vh;
  font-family: system-ui;
  display: flex;
}

.carousel {
  background: #00f;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
}

.box {
  color: #000;
  cursor: pointer;
  background: green;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  font-size: 121px;
  display: flex;
  position: relative;
}

.test {
  padding: 20px;
}

.test-2 {
  padding: 20px 10px;
}


/*# sourceMappingURL=src_Style_Marquee_188ac353.css.map*/