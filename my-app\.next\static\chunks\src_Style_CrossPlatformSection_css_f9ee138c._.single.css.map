{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/CrossPlatformSection.css"], "sourcesContent": ["/* Cross Platform Section */\n.cross-platform-section {\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n}\n\n.cross-platform-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.content-section {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.platform-badges {\n  display: flex;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.platform-badge {\n  background: rgba(255, 255, 255, 0.8);\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n  border: 1px solid rgba(245, 158, 11, 0.2);\n  backdrop-filter: blur(10px);\n}\n\n.section-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  color: #1f2937;\n  line-height: 1.1;\n  margin: 0;\n  font-family: 'Georgia', serif;\n}\n\n.section-description {\n  font-size: 1.125rem;\n  color: #6b7280;\n  line-height: 1.6;\n  margin: 0;\n}\n\n.get-started-btn {\n  background: transparent;\n  color: #1f2937;\n  border: 2px solid #d1d5db;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: fit-content;\n}\n\n.get-started-btn:hover {\n  border-color: #f59e0b;\n  color: #f59e0b;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);\n}\n\n.devices-section {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.device-stack {\n  position: relative;\n  width: 100%;\n  max-width: 500px;\n  height: 600px;\n}\n\n/* Desktop Mockup */\n.desktop-mockup {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 400px;\n  height: 280px;\n  background: #1f2937;\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  z-index: 3;\n}\n\n.desktop-header {\n  background: #374151;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n}\n\n.window-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.control {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.control.red { background: #ef4444; }\n.control.yellow { background: #f59e0b; }\n.control.green { background: #10b981; }\n\n.desktop-content {\n  display: flex;\n  height: calc(100% - 32px);\n}\n\n.sidebar {\n  width: 120px;\n  background: #2d3748;\n  padding: 16px 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.sidebar-item {\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 0.75rem;\n  color: #d1d5db;\n  cursor: pointer;\n  transition: background 0.2s;\n}\n\n.sidebar-item.active {\n  background: #f59e0b;\n  color: white;\n}\n\n.main-area {\n  flex: 1;\n  padding: 16px;\n  background: #1f2937;\n  position: relative;\n}\n\n.document-title {\n  color: #f59e0b;\n  font-size: 0.875rem;\n  font-weight: 600;\n  margin-bottom: 16px;\n}\n\n.document-section h4 {\n  color: #e5e7eb;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n}\n\n.idea-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 4px;\n  font-size: 0.7rem;\n  color: #d1d5db;\n}\n\n.bullet {\n  color: #f59e0b;\n}\n\n.voice-indicator-desktop {\n  position: absolute;\n  bottom: 16px;\n  right: 16px;\n  background: rgba(245, 158, 11, 0.2);\n  border-radius: 20px;\n  padding: 8px 12px;\n}\n\n.voice-waves {\n  display: flex;\n  gap: 2px;\n  align-items: center;\n}\n\n.wave {\n  width: 2px;\n  height: 12px;\n  background: #f59e0b;\n  border-radius: 1px;\n  animation: wave 1s ease-in-out infinite;\n}\n\n.wave:nth-child(1) { animation-delay: 0s; }\n.wave:nth-child(2) { animation-delay: 0.2s; }\n.wave:nth-child(3) { animation-delay: 0.4s; }\n.wave:nth-child(4) { animation-delay: 0.6s; }\n\n/* Mobile Mockup */\n.mobile-mockup {\n  position: absolute;\n  top: 200px;\n  right: 50px;\n  width: 180px;\n  height: 320px;\n  background: #1f2937;\n  border-radius: 20px;\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  z-index: 2;\n}\n\n.mobile-header {\n  background: #374151;\n  padding: 12px 16px 8px;\n}\n\n.status-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.7rem;\n  color: #e5e7eb;\n  margin-bottom: 8px;\n}\n\n.app-title {\n  color: #f59e0b;\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-align: center;\n}\n\n.mobile-content {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24px;\n}\n\n.mobile-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #e5e7eb;\n  font-size: 0.875rem;\n}\n\n.recording-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n}\n\n.recording-circle {\n  width: 60px;\n  height: 60px;\n  border: 2px solid #f59e0b;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.record-button {\n  width: 20px;\n  height: 20px;\n  background: #ef4444;\n  border-radius: 50%;\n}\n\n.recording-waves {\n  display: flex;\n  gap: 3px;\n  align-items: center;\n}\n\n.mobile-wave {\n  width: 3px;\n  height: 20px;\n  background: #3b82f6;\n  border-radius: 2px;\n  animation: wave 1.2s ease-in-out infinite;\n}\n\n.mobile-wave:nth-child(1) { animation-delay: 0s; }\n.mobile-wave:nth-child(2) { animation-delay: 0.15s; }\n.mobile-wave:nth-child(3) { animation-delay: 0.3s; }\n.mobile-wave:nth-child(4) { animation-delay: 0.45s; }\n.mobile-wave:nth-child(5) { animation-delay: 0.6s; }\n\n/* Voice Widget */\n.voice-widget {\n  position: absolute;\n  bottom: 50px;\n  left: 80px;\n  width: 120px;\n  height: 80px;\n  background: #1f2937;\n  border-radius: 16px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\n  padding: 12px;\n  z-index: 1;\n}\n\n.widget-header {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.widget-icon {\n  font-size: 1.2rem;\n}\n\n.widget-waves {\n  display: flex;\n  gap: 2px;\n  align-items: center;\n  justify-content: center;\n}\n\n.widget-wave {\n  width: 2px;\n  height: 16px;\n  background: #10b981;\n  border-radius: 1px;\n  animation: wave 0.8s ease-in-out infinite;\n}\n\n.widget-wave:nth-child(1) { animation-delay: 0s; }\n.widget-wave:nth-child(2) { animation-delay: 0.1s; }\n.widget-wave:nth-child(3) { animation-delay: 0.2s; }\n.widget-wave:nth-child(4) { animation-delay: 0.3s; }\n.widget-wave:nth-child(5) { animation-delay: 0.4s; }\n.widget-wave:nth-child(6) { animation-delay: 0.5s; }\n\n@keyframes wave {\n  0%, 100% { \n    transform: scaleY(0.3);\n    opacity: 0.7;\n  }\n  50% { \n    transform: scaleY(1);\n    opacity: 1;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .cross-platform-container {\n    gap: 60px;\n  }\n\n  .section-title {\n    font-size: 3rem;\n  }\n\n  .desktop-mockup {\n    width: 350px;\n    height: 240px;\n  }\n\n  .mobile-mockup {\n    width: 160px;\n    height: 280px;\n  }\n}\n\n@media (max-width: 768px) {\n  .cross-platform-section {\n    padding: 80px 20px;\n    min-height: auto;\n  }\n\n  .cross-platform-container {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n  }\n\n  .section-description {\n    font-size: 1rem;\n  }\n\n  .device-stack {\n    max-width: 400px;\n    height: 500px;\n    margin: 0 auto;\n  }\n\n  .desktop-mockup {\n    width: 300px;\n    height: 200px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n\n  .mobile-mockup {\n    width: 140px;\n    height: 250px;\n    top: 150px;\n    right: 20px;\n  }\n\n  .voice-widget {\n    bottom: 30px;\n    left: 20px;\n    width: 100px;\n    height: 70px;\n  }\n}\n\n@media (max-width: 480px) {\n  .cross-platform-section {\n    padding: 60px 15px;\n  }\n\n  .section-title {\n    font-size: 2rem;\n  }\n\n  .platform-badges {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .platform-badge {\n    font-size: 0.75rem;\n    padding: 6px 12px;\n  }\n\n  .device-stack {\n    max-width: 320px;\n    height: 400px;\n  }\n\n  .desktop-mockup {\n    width: 280px;\n    height: 180px;\n  }\n\n  .mobile-mockup {\n    width: 120px;\n    height: 220px;\n    top: 120px;\n    right: 10px;\n  }\n\n  .voice-widget {\n    width: 90px;\n    height: 60px;\n    bottom: 20px;\n    left: 10px;\n  }\n\n  .get-started-btn {\n    width: 100%;\n    max-width: 200px;\n    margin: 0 auto;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;AAQA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;;;;;;;;;AAYA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAMF;EACE;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;;EAOA;;;;;;;EAOA;;;;;;;;AAQF;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;;EAOA;;;;;;;EAOA"}}]}