"use client";
import React, { useState, useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/PricingSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const PricingSection = () => {
    const [isYearly, setIsYearly] = useState(false);
    const sectionRef = useRef(null);
    const cardsRef = useRef([]);

    const pricingPlans = {
        monthly: {
            free: {
                title: "Get Basic",
                subtitle: "Free",
                price: "Free",
                features: [
                    "2,000 words per month",
                    "Basic writing assistance",
                    "Grammar and spell check",
                    "Limited AI suggestions",
                    "Email support"
                ],
                buttonText: "Get started",
                buttonStyle: "outline",
                popular: false
            },
            pro: {
                title: "Pro Plan",
                subtitle: "$15/mo",
                price: "$15",
                period: "/mo",
                features: [
                    "Everything in Free Plan",
                    "50,000 words per month",
                    "Advanced AI writing tools",
                    "Custom tone settings",
                    "Priority support",
                    "Export to multiple formats"
                ],
                buttonText: "Get started for free",
                buttonStyle: "primary",
                popular: true
            },
            team: {
                title: "For Teams",
                subtitle: "$12/user/mo",
                price: "$12",
                period: "/user/mo",
                features: [
                    "Everything in Pro Plan",
                    "Unlimited words",
                    "Team collaboration tools",
                    "Admin dashboard",
                    "Custom integrations",
                    "24/7 priority support"
                ],
                buttonText: "Get started",
                buttonStyle: "primary",
                popular: false
            },
            enterprise: {
                title: "For Enterprise",
                subtitle: "Contact us",
                price: "Custom",
                features: [
                    "Everything in Team Plan",
                    "Custom AI model training",
                    "Advanced security features",
                    "Dedicated account manager",
                    "Custom integrations",
                    "SLA guarantee"
                ],
                buttonText: "Contact sales",
                buttonStyle: "outline",
                popular: false
            }
        },
        yearly: {
            free: {
                title: "Get Basic",
                subtitle: "Free",
                price: "Free",
                features: [
                    "2,000 words per month",
                    "Basic writing assistance",
                    "Grammar and spell check",
                    "Limited AI suggestions",
                    "Email support"
                ],
                buttonText: "Get started",
                buttonStyle: "outline",
                popular: false
            },
            pro: {
                title: "Pro Plan",
                subtitle: "$150/year",
                price: "$150",
                period: "/year",
                originalPrice: "$180",
                savings: "Save $30",
                features: [
                    "Everything in Free Plan",
                    "50,000 words per month",
                    "Advanced AI writing tools",
                    "Custom tone settings",
                    "Priority support",
                    "Export to multiple formats"
                ],
                buttonText: "Get started for free",
                buttonStyle: "primary",
                popular: true
            },
            team: {
                title: "For Teams",
                subtitle: "$120/user/year",
                price: "$120",
                period: "/user/year",
                originalPrice: "$144",
                savings: "Save $24",
                features: [
                    "Everything in Pro Plan",
                    "Unlimited words",
                    "Team collaboration tools",
                    "Admin dashboard",
                    "Custom integrations",
                    "24/7 priority support"
                ],
                buttonText: "Get started",
                buttonStyle: "primary",
                popular: false
            },
            enterprise: {
                title: "For Enterprise",
                subtitle: "Contact us",
                price: "Custom",
                features: [
                    "Everything in Team Plan",
                    "Custom AI model training",
                    "Advanced security features",
                    "Dedicated account manager",
                    "Custom integrations",
                    "SLA guarantee"
                ],
                buttonText: "Contact sales",
                buttonStyle: "outline",
                popular: false
            }
        }
    };

    const currentPlans = isYearly ? pricingPlans.yearly : pricingPlans.monthly;

    useEffect(() => {
        const section = sectionRef.current;
        const cards = cardsRef.current;

        if (!section) return;

        // Initial setup
        gsap.set(cards, { opacity: 0, y: 50, scale: 0.9 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        // Animate cards in sequence
        tl.to(cards, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.6,
            ease: "back.out(1.7)",
            stagger: 0.1
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="pricing-section" ref={sectionRef}>
            <div className="pricing-container">
                <div className="pricing-header">
                    <h1 className="pricing-title">Pricing</h1>
                    <p className="pricing-subtitle">
                        Choose the plan to get started for free.<br />
                        No credit card required.
                    </p>
                    
                    <div className="billing-toggle">
                        <span className={`toggle-label ${!isYearly ? 'active' : ''}`}>Monthly</span>
                        <div className="toggle-switch" onClick={() => setIsYearly(!isYearly)}>
                            <div className={`toggle-slider ${isYearly ? 'yearly' : 'monthly'}`}></div>
                        </div>
                        <span className={`toggle-label ${isYearly ? 'active' : ''}`}>
                            Yearly
                            <span className="discount-badge">Save 20% discount</span>
                        </span>
                    </div>
                </div>

                <div className="pricing-cards">
                    {Object.entries(currentPlans).map(([key, plan], index) => (
                        <div 
                            key={key}
                            className={`pricing-card ${plan.popular ? 'popular' : ''}`}
                            ref={el => cardsRef.current[index] = el}
                        >
                            {plan.popular && <div className="popular-badge">Most Popular</div>}
                            
                            <div className="card-header">
                                <h3 className="plan-title">{plan.title}</h3>
                                <div className="price-container">
                                    <div className="price-main">
                                        <span className="price">{plan.price}</span>
                                        {plan.period && <span className="period">{plan.period}</span>}
                                    </div>
                                    {plan.originalPrice && (
                                        <div className="price-details">
                                            <span className="original-price">{plan.originalPrice}</span>
                                            <span className="savings">{plan.savings}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="card-features">
                                <ul className="features-list">
                                    {plan.features.map((feature, idx) => (
                                        <li key={idx} className="feature-item">
                                            <span className="check-icon">✓</span>
                                            {feature}
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div className="card-footer">
                                <button className={`cta-button ${plan.buttonStyle}`}>
                                    {plan.buttonText}
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="pricing-footer">
                    <div className="money-back-guarantee">
                        <span className="guarantee-icon">🛡️</span>
                        <div className="guarantee-text">
                            <strong>30-day money-back guarantee</strong>
                            <p>We offer a full refund within 30 days of purchase if you're not satisfied.</p>
                        </div>
                    </div>
                    <button className="contact-sales-btn">Contact Sales</button>
                </div>
            </div>
        </section>
    );
};

export default PricingSection;
