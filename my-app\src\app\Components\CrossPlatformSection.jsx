"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/CrossPlatformSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const CrossPlatformSection = () => {
    const sectionRef = useRef(null);
    const contentRef = useRef(null);
    const devicesRef = useRef(null);

    useEffect(() => {
        const section = sectionRef.current;
        const content = contentRef.current;
        const devices = devicesRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([content, devices], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(content, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(devices, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4");

        // Floating animation for devices
        gsap.to(devices, {
            y: -8,
            duration: 3,
            ease: "power2.inOut",
            yoyo: true,
            repeat: -1
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="cross-platform-section" ref={sectionRef}>
            <div className="cross-platform-container">
                <div className="content-section" ref={contentRef}>
                    <div className="platform-badges">
                        <span className="platform-badge">📱 iOS</span>
                        <span className="platform-badge">🖥️ Mac</span>
                        <span className="platform-badge">🪟 Windows</span>
                    </div>
                    
                    <h2 className="section-title text-black" style={{color: 'black'}}>
                        On-the-go or at<br />
                        your desk
                    </h2>
                    
                    <p className="section-description">
                        With apps for Desktop and iPhone, flow<br />
                        freely from wherever you are. Your personal<br />
                        dictionary and notes sync seamlessly<br />
                        between all devices.
                    </p>
                    
                    <button className="get-started-btn">
                        Get started
                    </button>
                </div>

                <div className="devices-section" ref={devicesRef}>
                    <div className="device-stack">
                        {/* Desktop App */}
                        <div className="desktop-mockup">
                            <div className="desktop-header">
                                <div className="window-controls">
                                    <span className="control red"></span>
                                    <span className="control yellow"></span>
                                    <span className="control green"></span>
                                </div>
                            </div>
                            <div className="desktop-content">
                                <div className="sidebar">
                                    <div className="sidebar-item">📝 Documents</div>
                                    <div className="sidebar-item active">💡 Crazy product ideas</div>
                                    <div className="sidebar-item">🎯 Goals</div>
                                    <div className="sidebar-item">📊 Notes</div>
                                </div>
                                <div className="main-area">
                                    <div className="document-title">💡 Crazy product ideas</div>
                                    <div className="document-section">
                                        <h4>Physical Products</h4>
                                        <div className="idea-item">
                                            <span className="bullet">•</span>
                                            <span>Self-Adjusting Plant Stand</span>
                                        </div>
                                        <div className="idea-item">
                                            <span className="bullet">•</span>
                                            <span>Speakers with built-in planters and a tiny water reservoir</span>
                                        </div>
                                        <div className="idea-item">
                                            <span className="bullet">•</span>
                                            <span>Modular furniture system</span>
                                        </div>
                                    </div>
                                    <div className="voice-indicator-desktop">
                                        <div className="voice-waves">
                                            <div className="wave"></div>
                                            <div className="wave"></div>
                                            <div className="wave"></div>
                                            <div className="wave"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Mobile App */}
                        <div className="mobile-mockup">
                            <div className="mobile-header">
                                <div className="status-bar">
                                    <span className="time">9:41</span>
                                    <div className="status-icons">
                                        <span>📶</span>
                                        <span>🔋</span>
                                    </div>
                                </div>
                                <div className="app-title">Flow</div>
                            </div>
                            <div className="mobile-content">
                                <div className="mobile-item">
                                    <span className="item-icon">🎤</span>
                                    <span className="item-text">Voice Note</span>
                                </div>
                                <div className="recording-area">
                                    <div className="recording-circle">
                                        <div className="record-button"></div>
                                    </div>
                                    <div className="recording-waves">
                                        <div className="mobile-wave"></div>
                                        <div className="mobile-wave"></div>
                                        <div className="mobile-wave"></div>
                                        <div className="mobile-wave"></div>
                                        <div className="mobile-wave"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Voice Recorder Widget */}
                        <div className="voice-widget">
                            <div className="widget-header">
                                <span className="widget-icon">🎤</span>
                            </div>
                            <div className="widget-waves">
                                <div className="widget-wave"></div>
                                <div className="widget-wave"></div>
                                <div className="widget-wave"></div>
                                <div className="widget-wave"></div>
                                <div className="widget-wave"></div>
                                <div className="widget-wave"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default CrossPlatformSection;
