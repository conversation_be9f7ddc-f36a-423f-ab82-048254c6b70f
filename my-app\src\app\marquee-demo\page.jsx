"use client";
import React from "react";
import Marquee from "../Components/Marquee";

const MarqueeDemo = () => {
    // Different sets of items for different marquees
    const techItems = ["React", "Next.js", "GSAP", "JavaScript", "TypeScript", "Node.js", "MongoDB", "Express"];
    const designItems = ["Creative", "Design", "Innovation", "Excellence", "Quality", "Performance", "Success", "Modern"];
    const colorItems = ["🎨", "🚀", "⭐", "💎", "🔥", "✨", "🌟", "💫"];

    // Different color schemes
    const techColors = ["#61dafb", "#000000", "#88ce02", "#f7df1e", "#3178c6", "#339933", "#47a248", "#000000"];
    const designColors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff", "#5f27cd"];
    const vibrantColors = ["#e74c3c", "#f39c12", "#f1c40f", "#2ecc71", "#3498db", "#9b59b6", "#e67e22", "#1abc9c"];

    return (
        <div style={{ 
            minHeight: "100vh", 
            background: "linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)",
            padding: "40px 0"
        }}>
            <div style={{ 
                textAlign: "center", 
                marginBottom: "40px",
                color: "white"
            }}>
                <h1 style={{ 
                    fontSize: "3rem", 
                    marginBottom: "10px",
                    textShadow: "0 4px 8px rgba(0,0,0,0.3)"
                }}>
                    GSAP Marquee Showcase
                </h1>
                <p style={{ 
                    fontSize: "1.2rem", 
                    opacity: 0.9,
                    maxWidth: "600px",
                    margin: "0 auto"
                }}>
                    Interactive marquee animations with hover effects, customizable speed, and direction
                </p>
            </div>

            {/* Tech Stack Marquee */}
            <div style={{ marginBottom: "40px" }}>
                <h2 style={{ 
                    textAlign: "center", 
                    color: "white", 
                    marginBottom: "20px",
                    fontSize: "1.5rem"
                }}>
                    Tech Stack (Left to Right)
                </h2>
                <Marquee 
                    items={techItems}
                    colors={techColors}
                    speed={1}
                    direction="left"
                    pauseOnHover={true}
                />
            </div>

            {/* Design Concepts Marquee */}
            <div style={{ marginBottom: "40px" }}>
                <h2 style={{ 
                    textAlign: "center", 
                    color: "white", 
                    marginBottom: "20px",
                    fontSize: "1.5rem"
                }}>
                    Design Concepts (Right to Left, Fast)
                </h2>
                <Marquee 
                    items={designItems}
                    colors={designColors}
                    speed={1.5}
                    direction="right"
                    pauseOnHover={true}
                />
            </div>

            {/* Emoji Marquee */}
            <div style={{ marginBottom: "40px" }}>
                <h2 style={{ 
                    textAlign: "center", 
                    color: "white", 
                    marginBottom: "20px",
                    fontSize: "1.5rem"
                }}>
                    Fun Icons (Slow, No Pause on Hover)
                </h2>
                <Marquee 
                    items={colorItems}
                    colors={vibrantColors}
                    speed={0.5}
                    direction="left"
                    pauseOnHover={false}
                />
            </div>

            {/* Instructions */}
            <div style={{ 
                textAlign: "center", 
                color: "white", 
                marginTop: "60px",
                padding: "0 20px"
            }}>
                <h3 style={{ marginBottom: "20px", fontSize: "1.3rem" }}>
                    Features
                </h3>
                <div style={{ 
                    display: "grid", 
                    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                    gap: "20px",
                    maxWidth: "800px",
                    margin: "0 auto"
                }}>
                    <div style={{ 
                        background: "rgba(255,255,255,0.1)", 
                        padding: "20px", 
                        borderRadius: "10px",
                        backdropFilter: "blur(10px)"
                    }}>
                        <h4>🎯 Hover Effects</h4>
                        <p>Hover over items to see scale and rotation animations</p>
                    </div>
                    <div style={{ 
                        background: "rgba(255,255,255,0.1)", 
                        padding: "20px", 
                        borderRadius: "10px",
                        backdropFilter: "blur(10px)"
                    }}>
                        <h4>⚡ Customizable Speed</h4>
                        <p>Control animation speed and direction</p>
                    </div>
                    <div style={{ 
                        background: "rgba(255,255,255,0.1)", 
                        padding: "20px", 
                        borderRadius: "10px",
                        backdropFilter: "blur(10px)"
                    }}>
                        <h4>🎨 Dynamic Colors</h4>
                        <p>Each item gets a unique color from the palette</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MarqueeDemo;
