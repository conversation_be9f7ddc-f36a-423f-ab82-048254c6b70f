{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/Marquee.css"], "sourcesContent": [".MarqueeBox {\r\n  font-family: system-ui;\r\n  background: #111;\r\n  color: white;\r\n  text-align: center;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 20vh;\r\n}\r\n\r\n.wrapper {\r\n/*   height: 20%; */\r\n  width: 100%;\r\n  background: #555;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  overflow: hidden;\r\n}\r\n\r\n.carousel {\r\n  background: blue;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: green;\r\n/*   height: 80%; */\r\n/*   width: 20%; */\r\n  margin: 0;\r\n  padding: 0;\r\n  position: relative;\r\n/*   flex-shrink: 0; */\r\n  color: black;\r\n  font-size: 121px;\r\n  cursor: pointer;\r\n/*   padding:20px 50px; */\r\n}\r\n.test{\r\n  padding:20px\r\n}\r\n.test-2{\r\n  padding:20px 10px\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAgBA;;;;AAGA", "debugId": null}}]}