/* Marquee Container */
.marquee-container {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 100%;
  min-height: 100px;
  padding: 20px 0;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.marquee-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(0,0,0,0.15) 0%,
    transparent 15%,
    transparent 85%,
    rgba(0,0,0,0.15) 100%);
  pointer-events: none;
  z-index: 2;
}

.marquee-wrapper {
  display: flex;
  align-items: center;
  white-space: nowrap;
  will-change: transform;
}

/* Marquee Items */
.marquee-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: fit-content;
}

.marquee-content {
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  letter-spacing: 0.5px;
  user-select: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .marquee-content {
    font-size: 15px;
    padding: 11px 18px;
  }

  .marquee-item {
    margin-right: 18px;
  }
}

@media (max-width: 768px) {
  .marquee-container {
    min-height: 80px;
    padding: 15px 0;
  }

  .marquee-content {
    font-size: 14px;
    padding: 10px 16px;
  }

  .marquee-item {
    margin-right: 15px;
  }
}

@media (max-width: 480px) {
  .marquee-container {
    min-height: 70px;
    padding: 12px 0;
  }

  .marquee-content {
    font-size: 13px;
    padding: 8px 14px;
    letter-spacing: 0.3px;
  }

  .marquee-item {
    margin-right: 12px;
  }
}

@media (max-width: 320px) {
  .marquee-container {
    min-height: 60px;
    padding: 10px 0;
  }

  .marquee-content {
    font-size: 12px;
    padding: 6px 12px;
  }

  .marquee-item {
    margin-right: 10px;
  }
}

/* Legacy styles for backward compatibility */
.MarqueeBox {
  font-family: system-ui;
  background: #111;
  color: white;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20vh;
}

.carousel {
  background: blue;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.box {
  display: flex;
  align-items: center;
  justify-content: center;
  background: green;
  margin: 0;
  padding: 0;
  position: relative;
  color: black;
  font-size: 121px;
  cursor: pointer;
}

.test {
  padding: 20px;
}

.test-2 {
  padding: 20px 10px;
}