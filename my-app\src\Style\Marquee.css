.MarqueeBox {
  font-family: system-ui;
  background: #111;
  color: white;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20vh;
}

.wrapper {
/*   height: 20%; */
  width: 100%;
  background: #555;
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.carousel {
  background: blue;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.box {
  display: flex;
  align-items: center;
  justify-content: center;
  background: green;
/*   height: 80%; */
/*   width: 20%; */
  margin: 0;
  padding: 0;
  position: relative;
/*   flex-shrink: 0; */
  color: black;
  font-size: 121px;
  cursor: pointer;
/*   padding:20px 50px; */
}
.test{
  padding:20px
}
.test-2{
  padding:20px 10px
}