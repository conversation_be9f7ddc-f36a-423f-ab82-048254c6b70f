/* Marquee Container */
.marquee-container {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
  padding: 20px 0;
  position: relative;
  overflow: hidden;
}

.marquee-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(0,0,0,0.1) 0%,
    transparent 10%,
    transparent 90%,
    rgba(0,0,0,0.1) 100%);
  pointer-events: none;
  z-index: 2;
}

.wrapper {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  height: 80px;
}

/* Marquee Items */
.marquee-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 15px;
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.marquee-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.marquee-content {
  padding: 15px 25px;
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .marquee-content {
    font-size: 16px;
    padding: 12px 20px;
  }

  .marquee-container {
    min-height: 100px;
  }

  .wrapper {
    height: 60px;
  }
}

@media (max-width: 480px) {
  .marquee-content {
    font-size: 14px;
    padding: 10px 15px;
  }

  .marquee-item {
    margin: 0 10px;
  }
}

/* Legacy styles for backward compatibility */
.MarqueeBox {
  font-family: system-ui;
  background: #111;
  color: white;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20vh;
}

.carousel {
  background: blue;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.box {
  display: flex;
  align-items: center;
  justify-content: center;
  background: green;
  margin: 0;
  padding: 0;
  position: relative;
  color: black;
  font-size: 121px;
  cursor: pointer;
}

.test {
  padding: 20px;
}

.test-2 {
  padding: 20px 10px;
}