/* [project]/src/Style/CrossPlatformSection.css [app-client] (css) */
.cross-platform-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
}

.cross-platform-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
}

.content-section {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.platform-badges {
  gap: 12px;
  margin-bottom: 8px;
  display: flex;
}

.platform-badge {
  color: #374151;
  backdrop-filter: blur(10px);
  background: #fffc;
  border: 1px solid #f59e0b33;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: .875rem;
  font-weight: 500;
}

.section-title {
  color: #1f2937;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.section-description {
  color: #6b7280;
  margin: 0;
  font-size: 1.125rem;
  line-height: 1.6;
}

.get-started-btn {
  color: #1f2937;
  cursor: pointer;
  background: none;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  width: fit-content;
  padding: 16px 32px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.get-started-btn:hover {
  color: #f59e0b;
  border-color: #f59e0b;
  box-shadow: 0 4px 15px #f59e0b33;
}

.devices-section {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.device-stack {
  width: 100%;
  max-width: 500px;
  height: 600px;
  position: relative;
}

.desktop-mockup {
  z-index: 3;
  background: #1f2937;
  border-radius: 12px;
  width: 400px;
  height: 280px;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  box-shadow: 0 20px 40px #0000004d;
}

.desktop-header {
  background: #374151;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  display: flex;
}

.window-controls {
  gap: 8px;
  display: flex;
}

.control {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.control.red {
  background: #ef4444;
}

.control.yellow {
  background: #f59e0b;
}

.control.green {
  background: #10b981;
}

.desktop-content {
  height: calc(100% - 32px);
  display: flex;
}

.sidebar {
  background: #2d3748;
  flex-direction: column;
  gap: 8px;
  width: 120px;
  padding: 16px 8px;
  display: flex;
}

.sidebar-item {
  color: #d1d5db;
  cursor: pointer;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: .75rem;
  transition: background .2s;
}

.sidebar-item.active {
  color: #fff;
  background: #f59e0b;
}

.main-area {
  background: #1f2937;
  flex: 1;
  padding: 16px;
  position: relative;
}

.document-title {
  color: #f59e0b;
  margin-bottom: 16px;
  font-size: .875rem;
  font-weight: 600;
}

.document-section h4 {
  color: #e5e7eb;
  margin: 0 0 8px;
  font-size: .75rem;
  font-weight: 600;
}

.idea-item {
  color: #d1d5db;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: .7rem;
  display: flex;
}

.bullet {
  color: #f59e0b;
}

.voice-indicator-desktop {
  background: #f59e0b33;
  border-radius: 20px;
  padding: 8px 12px;
  position: absolute;
  bottom: 16px;
  right: 16px;
}

.voice-waves {
  align-items: center;
  gap: 2px;
  display: flex;
}

.wave {
  background: #f59e0b;
  border-radius: 1px;
  width: 2px;
  height: 12px;
  animation: 1s ease-in-out infinite wave;
}

.wave:first-child {
  animation-delay: 0s;
}

.wave:nth-child(2) {
  animation-delay: .2s;
}

.wave:nth-child(3) {
  animation-delay: .4s;
}

.wave:nth-child(4) {
  animation-delay: .6s;
}

.mobile-mockup {
  z-index: 2;
  background: #1f2937;
  border-radius: 20px;
  width: 180px;
  height: 320px;
  position: absolute;
  top: 200px;
  right: 50px;
  overflow: hidden;
  box-shadow: 0 15px 30px #0000004d;
}

.mobile-header {
  background: #374151;
  padding: 12px 16px 8px;
}

.status-bar {
  color: #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: .7rem;
  display: flex;
}

.app-title {
  color: #f59e0b;
  text-align: center;
  font-size: .875rem;
  font-weight: 600;
}

.mobile-content {
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 20px;
  display: flex;
}

.mobile-item {
  color: #e5e7eb;
  align-items: center;
  gap: 8px;
  font-size: .875rem;
  display: flex;
}

.recording-area {
  flex-direction: column;
  align-items: center;
  gap: 16px;
  display: flex;
}

.recording-circle {
  border: 2px solid #f59e0b;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  display: flex;
}

.record-button {
  background: #ef4444;
  border-radius: 50%;
  width: 20px;
  height: 20px;
}

.recording-waves {
  align-items: center;
  gap: 3px;
  display: flex;
}

.mobile-wave {
  background: #3b82f6;
  border-radius: 2px;
  width: 3px;
  height: 20px;
  animation: 1.2s ease-in-out infinite wave;
}

.mobile-wave:first-child {
  animation-delay: 0s;
}

.mobile-wave:nth-child(2) {
  animation-delay: .15s;
}

.mobile-wave:nth-child(3) {
  animation-delay: .3s;
}

.mobile-wave:nth-child(4) {
  animation-delay: .45s;
}

.mobile-wave:nth-child(5) {
  animation-delay: .6s;
}

.voice-widget {
  z-index: 1;
  background: #1f2937;
  border-radius: 16px;
  width: 120px;
  height: 80px;
  padding: 12px;
  position: absolute;
  bottom: 50px;
  left: 80px;
  box-shadow: 0 10px 25px #0000004d;
}

.widget-header {
  justify-content: center;
  margin-bottom: 8px;
  display: flex;
}

.widget-icon {
  font-size: 1.2rem;
}

.widget-waves {
  justify-content: center;
  align-items: center;
  gap: 2px;
  display: flex;
}

.widget-wave {
  background: #10b981;
  border-radius: 1px;
  width: 2px;
  height: 16px;
  animation: .8s ease-in-out infinite wave;
}

.widget-wave:first-child {
  animation-delay: 0s;
}

.widget-wave:nth-child(2) {
  animation-delay: .1s;
}

.widget-wave:nth-child(3) {
  animation-delay: .2s;
}

.widget-wave:nth-child(4) {
  animation-delay: .3s;
}

.widget-wave:nth-child(5) {
  animation-delay: .4s;
}

.widget-wave:nth-child(6) {
  animation-delay: .5s;
}

@keyframes wave {
  0%, 100% {
    opacity: .7;
    transform: scaleY(.3);
  }

  50% {
    opacity: 1;
    transform: scaleY(1);
  }
}

@media (width <= 1024px) {
  .cross-platform-container {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .desktop-mockup {
    width: 350px;
    height: 240px;
  }

  .mobile-mockup {
    width: 160px;
    height: 280px;
  }
}

@media (width <= 768px) {
  .cross-platform-section {
    min-height: auto;
    padding: 80px 20px;
  }

  .cross-platform-container {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .device-stack {
    max-width: 400px;
    height: 500px;
    margin: 0 auto;
  }

  .desktop-mockup {
    width: 300px;
    height: 200px;
    left: 50%;
    transform: translateX(-50%);
  }

  .mobile-mockup {
    width: 140px;
    height: 250px;
    top: 150px;
    right: 20px;
  }

  .voice-widget {
    width: 100px;
    height: 70px;
    bottom: 30px;
    left: 20px;
  }
}

@media (width <= 480px) {
  .cross-platform-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .platform-badges {
    flex-wrap: wrap;
    justify-content: center;
  }

  .platform-badge {
    padding: 6px 12px;
    font-size: .75rem;
  }

  .device-stack {
    max-width: 320px;
    height: 400px;
  }

  .desktop-mockup {
    width: 280px;
    height: 180px;
  }

  .mobile-mockup {
    width: 120px;
    height: 220px;
    top: 120px;
    right: 10px;
  }

  .voice-widget {
    width: 90px;
    height: 60px;
    bottom: 20px;
    left: 10px;
  }

  .get-started-btn {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }
}

/*# sourceMappingURL=src_Style_CrossPlatformSection_css_f9ee138c._.single.css.map*/