/* Flow Section Styles */
.flow-section {
  background: #2a2a2a;
  color: white;
  padding: 100px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.flow-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 20px;
}

.flow-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 100%;
}

.flow-left {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.flow-title {
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
  margin: 0;
  font-family: 'Georgia', serif;
  max-width: 100%;
  word-wrap: break-word;
}

.profession-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  max-width: 100%;
  width: 100%;
}

.profession-tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.profession-tag:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.flow-right {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: flex-end;
}

.flow-accessibility {
  max-width: 400px;
  text-align: left;
}

.accessibility-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: white;
}

.accessibility-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 24px;
}

.get-started-btn {
  background: #6366f1;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.get-started-btn:hover {
  background: #5855eb;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

/* Character Illustration */
.flow-illustration {
  position: relative;
  width: 300px;
  height: 300px;
}

.character-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.character {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.character-head {
  width: 80px;
  height: 80px;
  background: #ff9ff3;
  border-radius: 50%;
  position: relative;
  margin: 0 auto 10px;
}

.character-face {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.eye {
  width: 8px;
  height: 8px;
  background: #2a2a2a;
  border-radius: 50%;
  position: absolute;
}

.left-eye {
  left: -12px;
  top: -2px;
}

.right-eye {
  right: -12px;
  top: -2px;
}

.mouth {
  width: 12px;
  height: 6px;
  border: 2px solid #2a2a2a;
  border-top: none;
  border-radius: 0 0 12px 12px;
  position: absolute;
  left: -6px;
  top: 8px;
}

.character-ears {
  position: absolute;
  top: 10px;
  width: 100%;
}

.ear {
  width: 20px;
  height: 25px;
  background: #ff9ff3;
  border-radius: 50%;
  position: absolute;
}

.left-ear {
  left: -10px;
  transform: rotate(-20deg);
}

.right-ear {
  right: -10px;
  transform: rotate(20deg);
}

.character-body {
  width: 60px;
  height: 80px;
  background: #ff9ff3;
  border-radius: 30px;
  margin: 0 auto;
  position: relative;
}

.character-arms {
  position: absolute;
  top: 20px;
  width: 100%;
}

.arm {
  width: 30px;
  height: 8px;
  background: #ff9ff3;
  border-radius: 4px;
  position: absolute;
}

.left-arm {
  left: -25px;
  transform: rotate(-30deg);
}

.right-arm {
  right: -25px;
  transform: rotate(30deg);
}

.speech-bubble {
  position: absolute;
  top: -20px;
  right: -80px;
  width: 120px;
  height: 80px;
  background: white;
  border-radius: 15px;
  padding: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.speech-bubble::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 30px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 10px 10px 0;
  border-color: transparent white transparent transparent;
}

.bubble-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.text-lines {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.text-line {
  height: 3px;
  background: #ff9500;
  border-radius: 2px;
}

.text-line.short {
  width: 60%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .flow-content {
    gap: 60px;
  }
  
  .flow-title {
    font-size: 3.5rem;
  }
  
  .flow-illustration {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .flow-section {
    padding: 80px 20px;
  }
  
  .flow-content {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
  
  .flow-title {
    font-size: 3rem;
  }
  
  .flow-right {
    align-items: center;
  }
  
  .flow-accessibility {
    text-align: center;
  }
  
  .profession-tags {
    justify-content: center;
  }
  
  .flow-illustration {
    width: 200px;
    height: 200px;
  }
  
  .speech-bubble {
    right: -60px;
    width: 100px;
    height: 60px;
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .flow-section {
    padding: 60px 15px;
  }
  
  .flow-title {
    font-size: 2.5rem;
  }
  
  .accessibility-title {
    font-size: 1.5rem;
  }
  
  .accessibility-description {
    font-size: 1rem;
  }
  
  .profession-tag {
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .flow-illustration {
    width: 180px;
    height: 180px;
  }
  
  .character-head {
    width: 60px;
    height: 60px;
  }
  
  .character-body {
    width: 45px;
    height: 60px;
  }
}
