/* Content Image Section Styles */
.content-image-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 120px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.content-image-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.content-side {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.content-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  width: fit-content;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.content-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  color: #1a202c;
  margin: 0;
}

.highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.content-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #4a5568;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.feature-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  flex-shrink: 0;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.feature-description {
  font-size: 1rem;
  line-height: 1.5;
  color: #4a5568;
  margin: 0;
}

.content-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.secondary-btn {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 12px 26px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Image Side Styles */
.image-side {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.main-device {
  background: #1a202c;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}

.device-screen {
  background: #2d3748;
  border-radius: 12px;
  overflow: hidden;
}

.screen-header {
  background: #4a5568;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.screen-dots {
  display: flex;
  gap: 6px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.red { background: #f56565; }
.dot.yellow { background: #ed8936; }
.dot.green { background: #48bb78; }

.screen-title {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.screen-content {
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
}

.voice-wave {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.wave-bar {
  width: 4px;
  background: #667eea;
  border-radius: 2px;
  animation: wave 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(1) { height: 20px; animation-delay: 0s; }
.wave-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }
.wave-bar:nth-child(3) { height: 25px; animation-delay: 0.2s; }
.wave-bar:nth-child(4) { height: 40px; animation-delay: 0.3s; }
.wave-bar:nth-child(5) { height: 30px; animation-delay: 0.4s; }
.wave-bar:nth-child(6) { height: 35px; animation-delay: 0.5s; }
.wave-bar:nth-child(7) { height: 20px; animation-delay: 0.6s; }

@keyframes wave {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(0.3); }
}

.text-output {
  background: #4a5568;
  border-radius: 8px;
  padding: 16px;
  width: 100%;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.typing-text {
  color: #e2e8f0;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.cursor {
  animation: blink 1s infinite;
  color: #667eea;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-card {
  position: absolute;
  background: white;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  animation: float 3s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: -10%;
  animation-delay: 0s;
}

.card-2 {
  top: 60%;
  right: -15%;
  animation-delay: 1s;
}

.card-3 {
  bottom: 20%;
  left: -5%;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.card-icon {
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-image-container {
    gap: 60px;
  }
  
  .content-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .content-image-section {
    padding: 80px 20px;
  }
  
  .content-image-container {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .content-title {
    font-size: 2.5rem;
    text-align: center;
  }
  
  .content-header {
    text-align: center;
  }
  
  .content-badge {
    align-self: center;
  }
  
  .content-actions {
    justify-content: center;
  }
  
  .floating-card {
    display: none;
  }
}

@media (max-width: 480px) {
  .content-image-section {
    padding: 60px 15px;
  }
  
  .content-title {
    font-size: 2rem;
  }
  
  .content-description {
    font-size: 1rem;
  }
  
  .feature-item {
    padding: 16px;
  }
  
  .feature-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }
  
  .main-device {
    padding: 15px;
  }
  
  .screen-content {
    padding: 20px 15px;
  }
}
