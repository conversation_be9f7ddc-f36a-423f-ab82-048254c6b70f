{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/VoiceHeroSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport \"@/Style/VoiceHeroSection.css\";\n\nconst VoiceHeroSection = () => {\n    const sectionRef = useRef(null);\n    const curvedTextRef = useRef(null);\n    const voiceIndicatorRef = useRef(null);\n    const mainTextRef = useRef(null);\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const curvedText = curvedTextRef.current;\n        const voiceIndicator = voiceIndicatorRef.current;\n        const mainText = mainTextRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([curvedText, voiceIndicator, mainText], { opacity: 0, y: 30 });\n\n        // Create entrance timeline\n        const tl = gsap.timeline({ delay: 0.5 });\n\n        tl.to(curvedText, {\n            opacity: 1,\n            y: 0,\n            duration: 1,\n            ease: \"power3.out\"\n        })\n        .to(voiceIndicator, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.5\")\n        .to(mainText, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Animate curved text continuously\n        gsap.to(curvedText, {\n            rotation: 360,\n            duration: 20,\n            ease: \"none\",\n            repeat: -1\n        });\n\n        // Voice indicator animation\n        const waves = voiceIndicator?.querySelectorAll('.voice-wave');\n        if (waves) {\n            waves.forEach((wave, index) => {\n                gsap.to(wave, {\n                    scaleY: Math.random() * 0.5 + 0.5,\n                    duration: 0.5 + Math.random() * 0.5,\n                    ease: \"power2.inOut\",\n                    yoyo: true,\n                    repeat: -1,\n                    delay: index * 0.1\n                });\n            });\n        }\n\n        return () => {\n            gsap.killTweensOf([curvedText, voiceIndicator, mainText]);\n        };\n    }, []);\n\n    return (\n        <section className=\"voice-hero-section\" ref={sectionRef}>\n            <div className=\"voice-hero-container\">\n                <div className=\"curved-text-container\">\n                    <div className=\"curved-text\" ref={curvedTextRef}>\n                        <svg viewBox=\"0 0 200 200\" className=\"curved-svg\">\n                            <defs>\n                                <path\n                                    id=\"circle\"\n                                    d=\"M 100, 100 m -75, 0 a 75,75 0 1,1 150,0 a 75,75 0 1,1 -150,0\"\n                                />\n                            </defs>\n                            <text className=\"curved-text-path\">\n                                <textPath href=\"#circle\" startOffset=\"0%\">\n                                    their meeting were sent out, or mentioned it but didn't capture it •\n                                </textPath>\n                            </text>\n                        </svg>\n                    </div>\n                </div>\n\n                <div className=\"voice-indicator-container\" ref={voiceIndicatorRef}>\n                    <div className=\"voice-indicator\">\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                    </div>\n                    <div className=\"voice-label\">meeting were sent out, or</div>\n                </div>\n\n                <div className=\"main-content\" ref={mainTextRef}>\n                    <h1 className=\"hero-title\">\n                        Don't type, <span className=\"highlight\">just speak</span>\n                    </h1>\n                    \n                    <p className=\"hero-subtitle\">\n                        Effortless voice dictation in every application:<br />\n                        4x faster than typing, AI commands and auto-edits.\n                    </p>\n\n                    <div className=\"cta-buttons\">\n                        <button className=\"primary-btn\">\n                            🎤 Try Flow\n                        </button>\n                        <button className=\"secondary-btn\">\n                            Download\n                        </button>\n                    </div>\n\n                    <p className=\"availability\">\n                        Available on Mac, Windows and iPhone\n                    </p>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default VoiceHeroSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;;AAKA,MAAM,mBAAmB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,aAAa,cAAc,OAAO;QACxC,MAAM,iBAAiB,kBAAkB,OAAO;QAChD,MAAM,WAAW,YAAY,OAAO;QAEpC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAY;YAAgB;SAAS,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAErE,2BAA2B;QAC3B,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YAAE,OAAO;QAAI;QAEtC,GAAG,EAAE,CAAC,YAAY;YACd,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,gBAAgB;YAChB,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,UAAU;YACV,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,mCAAmC;QACnC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,YAAY;YAChB,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ,CAAC;QACb;QAEA,4BAA4B;QAC5B,MAAM,QAAQ,gBAAgB,iBAAiB;QAC/C,IAAI,OAAO;YACP,MAAM,OAAO,CAAC,CAAC,MAAM;gBACjB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACV,QAAQ,KAAK,MAAM,KAAK,MAAM;oBAC9B,UAAU,MAAM,KAAK,MAAM,KAAK;oBAChC,MAAM;oBACN,MAAM;oBACN,QAAQ,CAAC;oBACT,OAAO,QAAQ;gBACnB;YACJ;QACJ;QAEA,OAAO;YACH,6IAAA,CAAA,OAAI,CAAC,YAAY,CAAC;gBAAC;gBAAY;gBAAgB;aAAS;QAC5D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAqB,KAAK;kBACzC,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAI,WAAU;wBAAc,KAAK;kCAC9B,cAAA,8OAAC;4BAAI,SAAQ;4BAAc,WAAU;;8CACjC,8OAAC;8CACG,cAAA,8OAAC;wCACG,IAAG;wCACH,GAAE;;;;;;;;;;;8CAGV,8OAAC;oCAAK,WAAU;8CACZ,cAAA,8OAAC;wCAAS,MAAK;wCAAU,aAAY;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1D,8OAAC;oBAAI,WAAU;oBAA4B,KAAK;;sCAC5C,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEnB,8OAAC;4BAAI,WAAU;sCAAc;;;;;;;;;;;;8BAGjC,8OAAC;oBAAI,WAAU;oBAAe,KAAK;;sCAC/B,8OAAC;4BAAG,WAAU;;gCAAa;8CACX,8OAAC;oCAAK,WAAU;8CAAY;;;;;;;;;;;;sCAG5C,8OAAC;4BAAE,WAAU;;gCAAgB;8CACuB,8OAAC;;;;;gCAAK;;;;;;;sCAI1D,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;sCAKtC,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Marquee.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport \"@/Style/Marquee.css\";\r\n\r\nconst Marquee = ({\r\n    items = [\"Creative\", \"Design\", \"Development\", \"Innovation\", \"Excellence\", \"Quality\", \"Performance\", \"Success\"],\r\n    speed = 1, // speed multiplier\r\n    colors = [\"#ff6b6b\", \"#4ecdc4\", \"#45b7d1\", \"#96ceb4\", \"#feca57\", \"#ff9ff3\", \"#54a0ff\", \"#5f27cd\"]\r\n}) => {\r\n    const wrapperRef = useRef(null);\r\n    const animationRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const wrapper = wrapperRef.current;\r\n        if (!wrapper) return;\r\n\r\n        const marqueeItems = wrapper.querySelectorAll(\".marquee-item\");\r\n\r\n        // Apply background colors\r\n        gsap.set(marqueeItems, {\r\n            backgroundColor: gsap.utils.wrap(colors),\r\n        });\r\n\r\n        // Create infinite scroll animation\r\n        const createAnimation = () => {\r\n            if (marqueeItems.length === 0) return;\r\n\r\n            // Calculate the total width of one set of items\r\n            let totalWidth = 0;\r\n            const itemCount = items.length; // Original items count\r\n\r\n            for (let i = 0; i < itemCount; i++) {\r\n                if (marqueeItems[i]) {\r\n                    totalWidth += marqueeItems[i].offsetWidth + 20; // 20px margin\r\n                }\r\n            }\r\n\r\n            // Animate the wrapper to move left infinitely\r\n            animationRef.current = gsap.to(wrapper, {\r\n                x: -totalWidth,\r\n                duration: totalWidth / (50 * speed), // Adjust duration based on speed\r\n                ease: \"none\",\r\n                repeat: -1,\r\n            });\r\n        };\r\n\r\n        // Wait for layout to be ready\r\n        const timer = setTimeout(createAnimation, 200);\r\n\r\n        return () => {\r\n            clearTimeout(timer);\r\n            if (animationRef.current) {\r\n                animationRef.current.kill();\r\n            }\r\n        };\r\n    }, [items, speed, colors]);\r\n    return (\r\n        <div className=\"marquee-container\">\r\n            <div className=\"marquee-wrapper\" ref={wrapperRef}>\r\n                {/* Duplicate items for seamless infinite loop */}\r\n                {[...items, ...items].map((item, index) => (\r\n                    <div key={index} className=\"marquee-item\">\r\n                        <div className=\"marquee-content\">\r\n                            {item}\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Marquee;"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;;AAKA,MAAM,UAAU,CAAC,EACb,QAAQ;IAAC;IAAY;IAAU;IAAe;IAAc;IAAc;IAAW;IAAe;CAAU,EAC9G,QAAQ,CAAC,EACT,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU,EACpG;IACG,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,IAAI,CAAC,SAAS;QAEd,MAAM,eAAe,QAAQ,gBAAgB,CAAC;QAE9C,0BAA0B;QAC1B,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,cAAc;YACnB,iBAAiB,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACrC;QAEA,mCAAmC;QACnC,MAAM,kBAAkB;YACpB,IAAI,aAAa,MAAM,KAAK,GAAG;YAE/B,gDAAgD;YAChD,IAAI,aAAa;YACjB,MAAM,YAAY,MAAM,MAAM,EAAE,uBAAuB;YAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAChC,IAAI,YAAY,CAAC,EAAE,EAAE;oBACjB,cAAc,YAAY,CAAC,EAAE,CAAC,WAAW,GAAG,IAAI,cAAc;gBAClE;YACJ;YAEA,8CAA8C;YAC9C,aAAa,OAAO,GAAG,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;gBACpC,GAAG,CAAC;gBACJ,UAAU,aAAa,CAAC,KAAK,KAAK;gBAClC,MAAM;gBACN,QAAQ,CAAC;YACb;QACJ;QAEA,8BAA8B;QAC9B,MAAM,QAAQ,WAAW,iBAAiB;QAE1C,OAAO;YACH,aAAa;YACb,IAAI,aAAa,OAAO,EAAE;gBACtB,aAAa,OAAO,CAAC,IAAI;YAC7B;QACJ;IACJ,GAAG;QAAC;QAAO;QAAO;KAAO;IACzB,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;YAAkB,KAAK;sBAEjC;mBAAI;mBAAU;aAAM,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;oBAAgB,WAAU;8BACvB,cAAA,8OAAC;wBAAI,WAAU;kCACV;;;;;;mBAFC;;;;;;;;;;;;;;;AAS9B;uCAEe", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/FeaturesShowcase.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/FeaturesShowcase.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst FeaturesShowcase = () => {\n    const containerRef = useRef(null);\n    const cardsRef = useRef([]);\n\n    useEffect(() => {\n        const cards = cardsRef.current;\n        \n        // Initial animation for cards\n        gsap.fromTo(cards, \n            {\n                opacity: 0,\n                y: 50,\n                scale: 0.9\n            },\n            {\n                opacity: 1,\n                y: 0,\n                scale: 1,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: containerRef.current,\n                    start: \"top 80%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            }\n        );\n\n        // Hover animations\n        cards.forEach((card, index) => {\n            if (card) {\n                card.addEventListener('mouseenter', () => {\n                    gsap.to(card, {\n                        scale: 1.05,\n                        y: -10,\n                        duration: 0.3,\n                        ease: \"power2.out\"\n                    });\n                });\n\n                card.addEventListener('mouseleave', () => {\n                    gsap.to(card, {\n                        scale: 1,\n                        y: 0,\n                        duration: 0.3,\n                        ease: \"power2.out\"\n                    });\n                });\n            }\n        });\n\n        return () => {\n            cards.forEach(card => {\n                if (card) {\n                    card.removeEventListener('mouseenter', () => {});\n                    card.removeEventListener('mouseleave', () => {});\n                }\n            });\n        };\n    }, []);\n\n    const features = [\n        {\n            id: 1,\n            title: \"AI Auto Edits\",\n            description: \"Capture naturally and have interactive voice assistants that work seamlessly. Enhance your content with AI-powered editing tools.\",\n            image: \"/api/placeholder/300/400\",\n            bgColor: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 2,\n            title: \"Personal dictionary\",\n            description: \"Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 3,\n            title: \"Different tones for each app\",\n            description: \"Adapt your communication style with different tones optimized for each application and context.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 4,\n            title: \"100+ languages\",\n            description: \"Communicate globally with support for over 100 languages and seamless translation capabilities.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            textColor: \"#ffffff\"\n        }\n    ];\n\n    return (\n        <section className=\"features-showcase\" ref={containerRef}>\n            <div className=\"features-container\">\n                <div className=\"features-header\">\n                    <h2 className=\"features-title\">Powerful Features</h2>\n                    <p className=\"features-subtitle\">\n                        Discover the advanced capabilities that make our platform unique\n                    </p>\n                </div>\n                \n                <div className=\"features-grid\">\n                    {features.map((feature, index) => (\n                        <div\n                            key={feature.id}\n                            className={`feature-card ${index === 0 ? 'feature-large' : ''}`}\n                            ref={el => cardsRef.current[index] = el}\n                            style={{ \n                                background: feature.bgColor,\n                                color: feature.textColor \n                            }}\n                        >\n                            <div className=\"feature-content\">\n                                <div className=\"feature-image\">\n                                    <div className=\"phone-mockup\">\n                                        {index === 0 && (\n                                            <div className=\"ai-interface\">\n                                                <div className=\"ai-suggestions\">\n                                                    <div className=\"suggestion-item\">✨ Enhance clarity</div>\n                                                    <div className=\"suggestion-item\">🎯 Improve tone</div>\n                                                    <div className=\"suggestion-item\">📝 Fix grammar</div>\n                                                </div>\n                                                <div className=\"ai-controls\">\n                                                    <button className=\"ai-btn\">Apply</button>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 1 && (\n                                            <div className=\"dictionary-interface\">\n                                                <div className=\"dict-header\">Your Dictionary</div>\n                                                <div className=\"dict-items\">\n                                                    <div className=\"dict-item\">Technical terms</div>\n                                                    <div className=\"dict-item\">Brand names</div>\n                                                    <div className=\"dict-item\">Custom phrases</div>\n                                                    <div className=\"dict-item\">Abbreviations</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 2 && (\n                                            <div className=\"tones-interface\">\n                                                <div className=\"app-selector\">\n                                                    <div className=\"app-icon\">📧</div>\n                                                    <div className=\"app-icon active\">💬</div>\n                                                    <div className=\"app-icon\">📱</div>\n                                                </div>\n                                                <div className=\"tone-options\">\n                                                    <div className=\"tone-btn\">Professional</div>\n                                                    <div className=\"tone-btn active\">Casual</div>\n                                                    <div className=\"tone-btn\">Friendly</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 3 && (\n                                            <div className=\"languages-interface\">\n                                                <div className=\"lang-wheel\">\n                                                    <div className=\"lang-item\">EN</div>\n                                                    <div className=\"lang-item\">ES</div>\n                                                    <div className=\"lang-item\">FR</div>\n                                                    <div className=\"lang-item\">DE</div>\n                                                    <div className=\"lang-item\">ZH</div>\n                                                    <div className=\"lang-item\">JA</div>\n                                                    <div className=\"lang-center\">100+</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                    </div>\n                                </div>\n                                \n                                <div className=\"feature-text\">\n                                    <h3 className=\"feature-title\">{feature.title}</h3>\n                                    <p className=\"feature-description\">{feature.description}</p>\n                                </div>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default FeaturesShowcase;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,mBAAmB;IACrB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,SAAS,OAAO;QAE9B,8BAA8B;QAC9B,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACR;YACI,SAAS;YACT,GAAG;YACH,OAAO;QACX,GACA;YACI,SAAS;YACT,GAAG;YACH,OAAO;YACP,UAAU;YACV,SAAS;YACT,MAAM;YACN,eAAe;gBACX,SAAS,aAAa,OAAO;gBAC7B,OAAO;gBACP,eAAe;YACnB;QACJ;QAGJ,mBAAmB;QACnB,MAAM,OAAO,CAAC,CAAC,MAAM;YACjB,IAAI,MAAM;gBACN,KAAK,gBAAgB,CAAC,cAAc;oBAChC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;wBACV,OAAO;wBACP,GAAG,CAAC;wBACJ,UAAU;wBACV,MAAM;oBACV;gBACJ;gBAEA,KAAK,gBAAgB,CAAC,cAAc;oBAChC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;wBACV,OAAO;wBACP,GAAG;wBACH,UAAU;wBACV,MAAM;oBACV;gBACJ;YACJ;QACJ;QAEA,OAAO;YACH,MAAM,OAAO,CAAC,CAAA;gBACV,IAAI,MAAM;oBACN,KAAK,mBAAmB,CAAC,cAAc,KAAO;oBAC9C,KAAK,mBAAmB,CAAC,cAAc,KAAO;gBAClD;YACJ;QACJ;IACJ,GAAG,EAAE;IAEL,MAAM,WAAW;QACb;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;KACH;IAED,qBACI,8OAAC;QAAQ,WAAU;QAAoB,KAAK;kBACxC,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAAiB;;;;;;sCAC/B,8OAAC;4BAAE,WAAU;sCAAoB;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAU;8BACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,8OAAC;4BAEG,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,kBAAkB,IAAI;4BAC/D,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;4BACrC,OAAO;gCACH,YAAY,QAAQ,OAAO;gCAC3B,OAAO,QAAQ,SAAS;4BAC5B;sCAEA,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;kDACX,cAAA,8OAAC;4CAAI,WAAU;;gDACV,UAAU,mBACP,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,8OAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,8OAAC;oEAAI,WAAU;8EAAkB;;;;;;;;;;;;sEAErC,8OAAC;4DAAI,WAAU;sEACX,cAAA,8OAAC;gEAAO,WAAU;0EAAS;;;;;;;;;;;;;;;;;gDAItC,UAAU,mBACP,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,8OAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,8OAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,8OAAC;oEAAI,WAAU;8EAAY;;;;;;;;;;;;;;;;;;gDAItC,UAAU,mBACP,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;8EAAW;;;;;;8EAC1B,8OAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,8OAAC;oEAAI,WAAU;8EAAW;;;;;;;;;;;;sEAE9B,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;8EAAW;;;;;;8EAC1B,8OAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,8OAAC;oEAAI,WAAU;8EAAW;;;;;;;;;;;;;;;;;;gDAIrC,UAAU,mBACP,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,8OAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,8OAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,8OAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,8OAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,8OAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,8OAAC;gEAAI,WAAU;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOjD,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;0DAAiB,QAAQ,KAAK;;;;;;0DAC5C,8OAAC;gDAAE,WAAU;0DAAuB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2BAlE1D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AA2E3C;uCAEe", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/FlowSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/FlowSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst FlowSection = () => {\n    const sectionRef = useRef(null);\n    const titleRef = useRef(null);\n    const tagsRef = useRef([]);\n    const contentRef = useRef(null);\n    const illustrationRef = useRef(null);\n\n    const professionTags = [\n        \"Accessibility\", \"Consultants\", \"Creators\", \"Customer Support\",\n        \"Designers\", \"HR\", \"Managers\", \"Publishers\", \"Ergonomics\",\n        \"Freelancers\", \"Government\", \"Healthcare\", \"Individuals\",\n        \"Journalists\", \"Lawyers\", \"Multilingual\", \"Product\", \"Sales\",\n        \"Slower Typists\", \"Students\", \"Teams\", \"Writers\"\n    ];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const title = titleRef.current;\n        const tags = tagsRef.current;\n        const content = contentRef.current;\n        const illustration = illustrationRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([title, content, illustration], { opacity: 0, y: 50 });\n        gsap.set(tags, { opacity: 0, scale: 0.8 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 80%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(title, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(tags, {\n            opacity: 1,\n            scale: 1,\n            duration: 0.6,\n            stagger: 0.05,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.4\")\n        .to([content, illustration], {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            stagger: 0.2,\n            ease: \"power3.out\"\n        }, \"-=0.3\");\n\n        // Floating animation for illustration\n        gsap.to(illustration, {\n            y: -10,\n            duration: 2,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"flow-section\" ref={sectionRef}>\n            <div className=\"flow-container\">\n                <div className=\"flow-content\">\n                    <div className=\"flow-left\">\n                        <h1 className=\"flow-title\" ref={titleRef}>\n                            Flow is made<br />for you\n                        </h1>\n                        \n                        <div className=\"profession-tags\">\n                            {professionTags.map((tag, index) => (\n                                <span \n                                    key={index}\n                                    className=\"profession-tag\"\n                                    ref={el => tagsRef.current[index] = el}\n                                >\n                                    {tag}\n                                </span>\n                            ))}\n                        </div>\n                    </div>\n\n                    <div className=\"flow-right\">\n                        <div className=\"flow-accessibility\" ref={contentRef}>\n                            <h2 className=\"accessibility-title\">Flow for Accessibility</h2>\n                            <p className=\"accessibility-description\">\n                                Your voice deserves a shortcut. Flow supports anyone who \n                                feels slowed down by a keyboard by turning speech into \n                                structured, polished text—quickly, reliably, naturally.\n                            </p>\n                            <button className=\"get-started-btn\">Get started</button>\n                        </div>\n\n                        <div className=\"flow-illustration\" ref={illustrationRef}>\n                            <div className=\"character-container\">\n                                <div className=\"character\">\n                                    <div className=\"character-head\">\n                                        <div className=\"character-face\">\n                                            <div className=\"eye left-eye\"></div>\n                                            <div className=\"eye right-eye\"></div>\n                                            <div className=\"mouth\"></div>\n                                        </div>\n                                        <div className=\"character-ears\">\n                                            <div className=\"ear left-ear\"></div>\n                                            <div className=\"ear right-ear\"></div>\n                                        </div>\n                                    </div>\n                                    <div className=\"character-body\">\n                                        <div className=\"character-arms\">\n                                            <div className=\"arm left-arm\"></div>\n                                            <div className=\"arm right-arm\"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                                <div className=\"speech-bubble\">\n                                    <div className=\"bubble-content\">\n                                        <div className=\"text-lines\">\n                                            <div className=\"text-line\"></div>\n                                            <div className=\"text-line\"></div>\n                                            <div className=\"text-line short\"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default FlowSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,cAAc;IAChB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,iBAAiB;QACnB;QAAiB;QAAe;QAAY;QAC5C;QAAa;QAAM;QAAY;QAAc;QAC7C;QAAe;QAAc;QAAc;QAC3C;QAAe;QAAW;QAAgB;QAAW;QACrD;QAAkB;QAAY;QAAS;KAC1C;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,OAAO,QAAQ,OAAO;QAC5B,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,eAAe,gBAAgB,OAAO;QAE5C,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAO;YAAS;SAAa,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7D,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAExC,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,OAAO;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;YACV,SAAS;YACT,MAAM;QACV,GAAG,SACF,EAAE,CAAC;YAAC;YAAS;SAAa,EAAE;YACzB,SAAS;YACT,GAAG;YACH,UAAU;YACV,SAAS;YACT,MAAM;QACV,GAAG;QAEH,sCAAsC;QACtC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,cAAc;YAClB,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;QACb;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAe,KAAK;kBACnC,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAG,WAAU;gCAAa,KAAK;;oCAAU;kDAC1B,8OAAC;;;;;oCAAK;;;;;;;0CAGtB,8OAAC;gCAAI,WAAU;0CACV,eAAe,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;wCAEG,WAAU;wCACV,KAAK,CAAA,KAAM,QAAQ,OAAO,CAAC,MAAM,GAAG;kDAEnC;uCAJI;;;;;;;;;;;;;;;;kCAUrB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;gCAAqB,KAAK;;kDACrC,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDAKzC,8OAAC;wCAAO,WAAU;kDAAkB;;;;;;;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;gCAAoB,KAAK;0CACpC,cAAA,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAGvB,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI3B,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;gDAAI,WAAU;0DACX,cAAA,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D;uCAEe", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/ContentImageSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/ContentImageSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst ContentImageSection = () => {\n    const sectionRef = useRef(null);\n    const contentRef = useRef(null);\n    const imageRef = useRef(null);\n    const featuresRef = useRef([]);\n\n    const features = [\n        {\n            icon: \"🎯\",\n            title: \"Precision & Accuracy\",\n            description: \"Advanced AI algorithms ensure your voice is converted to text with remarkable precision and context awareness.\"\n        },\n        {\n            icon: \"⚡\",\n            title: \"Lightning Fast\",\n            description: \"Real-time processing means your thoughts become text instantly, keeping up with your natural speaking pace.\"\n        },\n        {\n            icon: \"🌐\",\n            title: \"Multi-Language Support\",\n            description: \"Communicate in over 100 languages with seamless translation and localization capabilities.\"\n        },\n        {\n            icon: \"🔒\",\n            title: \"Privacy First\",\n            description: \"Your data stays secure with end-to-end encryption and local processing options for sensitive content.\"\n        }\n    ];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const content = contentRef.current;\n        const image = imageRef.current;\n        const featureElements = featuresRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([content, image], { opacity: 0, y: 50 });\n        gsap.set(featureElements, { opacity: 0, x: -30 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(image, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(featureElements, {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            stagger: 0.1,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Parallax effect for image\n        gsap.to(image, {\n            y: -20,\n            scrollTrigger: {\n                trigger: section,\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: 1\n            }\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"content-image-section\" ref={sectionRef}>\n            <div className=\"content-image-container\">\n                <div className=\"content-side\" ref={contentRef}>\n                    <div className=\"content-header\">\n                        <span className=\"content-badge\">Why Choose Flow</span>\n                        <h2 className=\"content-title\">\n                            Transform Your Voice Into \n                            <span className=\"highlight\"> Powerful Content</span>\n                        </h2>\n                        <p className=\"content-description\">\n                            Experience the future of content creation with our advanced voice-to-text \n                            technology. Whether you're writing emails, creating documents, or brainstorming \n                            ideas, Flow makes it effortless and natural.\n                        </p>\n                    </div>\n\n                    <div className=\"features-grid\">\n                        {features.map((feature, index) => (\n                            <div \n                                key={index}\n                                className=\"feature-item\"\n                                ref={el => featuresRef.current[index] = el}\n                            >\n                                <div className=\"feature-icon\">{feature.icon}</div>\n                                <div className=\"feature-content\">\n                                    <h3 className=\"feature-title\">{feature.title}</h3>\n                                    <p className=\"feature-description\">{feature.description}</p>\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n\n                    <div className=\"content-actions\">\n                        <button className=\"primary-btn\">Start Free Trial</button>\n                        <button className=\"secondary-btn\">Watch Demo</button>\n                    </div>\n                </div>\n\n                <div className=\"image-side\" ref={imageRef}>\n                    <div className=\"image-container\">\n                        <div className=\"main-device\">\n                            <div className=\"device-screen\">\n                                <div className=\"screen-header\">\n                                    <div className=\"screen-dots\">\n                                        <span className=\"dot red\"></span>\n                                        <span className=\"dot yellow\"></span>\n                                        <span className=\"dot green\"></span>\n                                    </div>\n                                    <div className=\"screen-title\">Flow - Voice to Text</div>\n                                </div>\n                                <div className=\"screen-content\">\n                                    <div className=\"voice-wave\">\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                    </div>\n                                    <div className=\"text-output\">\n                                        <div className=\"typing-text\">\n                                            <span>Hello, this is a demonstration of Flow's</span>\n                                            <span className=\"cursor\">|</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                        \n                        <div className=\"floating-elements\">\n                            <div className=\"floating-card card-1\">\n                                <div className=\"card-icon\">🎤</div>\n                                <div className=\"card-text\">Voice Input</div>\n                            </div>\n                            <div className=\"floating-card card-2\">\n                                <div className=\"card-icon\">📝</div>\n                                <div className=\"card-text\">Text Output</div>\n                            </div>\n                            <div className=\"floating-card card-3\">\n                                <div className=\"card-icon\">⚡</div>\n                                <div className=\"card-text\">Real-time</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default ContentImageSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,sBAAsB;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE7B,MAAM,WAAW;QACb;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;KACH;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,kBAAkB,YAAY,OAAO;QAE3C,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAS;SAAM,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/C,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,iBAAiB;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAE/C,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,SAAS;YACX,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,OAAO;YACP,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,iBAAiB;YACjB,SAAS;YACT,GAAG;YACH,UAAU;YACV,SAAS;YACT,MAAM;QACV,GAAG;QAEH,4BAA4B;QAC5B,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;YACX,GAAG,CAAC;YACJ,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,OAAO;YACX;QACJ;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAwB,KAAK;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;oBAAe,KAAK;;sCAC/B,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAG,WAAU;;wCAAgB;sDAE1B,8OAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;8CAEhC,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;sCACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,8OAAC;oCAEG,WAAU;oCACV,KAAK,CAAA,KAAM,YAAY,OAAO,CAAC,MAAM,GAAG;;sDAExC,8OAAC;4CAAI,WAAU;sDAAgB,QAAQ,IAAI;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAG,WAAU;8DAAiB,QAAQ,KAAK;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAuB,QAAQ,WAAW;;;;;;;;;;;;;mCAPtD;;;;;;;;;;sCAajB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAO,WAAU;8CAAc;;;;;;8CAChC,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAI1C,8OAAC;oBAAI,WAAU;oBAAa,KAAK;8BAC7B,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;;;;;;;;;;;;8DAEpB,8OAAC;oDAAI,WAAU;8DAAe;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;uCAEe", "debugId": null}}, {"offset": {"line": 1827, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/AIAutoEditsSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/AIAutoEditsSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst AIAutoEditsSection = () => {\n    const sectionRef = useRef(null);\n    const phoneRef = useRef(null);\n    const contentRef = useRef(null);\n    const [currentSuggestion, setCurrentSuggestion] = useState(0);\n\n    const suggestions = [\n        \"✨ Enhance clarity and flow\",\n        \"🎯 Improve tone and style\", \n        \"📝 Fix grammar and spelling\",\n        \"🔄 Restructure for impact\"\n    ];\n\n    const originalText = \"Let's brainstorm a few ideas for our next campaign. We need something that will resonate with our target audience and drive engagement.\";\n    const improvedText = \"Let's collaborate on innovative campaign concepts that will deeply resonate with our target demographic and significantly boost engagement metrics.\";\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const phone = phoneRef.current;\n        const content = contentRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([phone, content], { opacity: 0, y: 50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(phone, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animation for phone\n        gsap.to(phone, {\n            y: -8,\n            duration: 3,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1\n        });\n\n        // Cycle through suggestions\n        const suggestionInterval = setInterval(() => {\n            setCurrentSuggestion(prev => (prev + 1) % suggestions.length);\n        }, 3000);\n\n        return () => {\n            clearInterval(suggestionInterval);\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"ai-auto-edits-section\" ref={sectionRef}>\n            <div className=\"ai-auto-edits-container\">\n                <div className=\"phone-mockup-container\" ref={phoneRef}>\n                    <div className=\"phone-device\">\n                        <div className=\"phone-screen\">\n                            <div className=\"screen-header\">\n                                <div className=\"status-bar\">\n                                    <span className=\"time\">9:41</span>\n                                    <div className=\"status-icons\">\n                                        <span className=\"signal\">📶</span>\n                                        <span className=\"wifi\">📶</span>\n                                        <span className=\"battery\">🔋</span>\n                                    </div>\n                                </div>\n                                <div className=\"app-header\">\n                                    <h3>Flow - AI Writing Assistant</h3>\n                                </div>\n                            </div>\n                            \n                            <div className=\"screen-content\">\n                                <div className=\"text-editor\">\n                                    <div className=\"original-text\">\n                                        <p>{originalText}</p>\n                                    </div>\n                                    \n                                    <div className=\"ai-suggestions\">\n                                        <div className=\"suggestion-header\">\n                                            <span className=\"ai-icon\">🤖</span>\n                                            <span>AI Suggestions</span>\n                                        </div>\n                                        \n                                        <div className=\"suggestion-items\">\n                                            {suggestions.map((suggestion, index) => (\n                                                <div \n                                                    key={index}\n                                                    className={`suggestion-item ${index === currentSuggestion ? 'active' : ''}`}\n                                                >\n                                                    {suggestion}\n                                                </div>\n                                            ))}\n                                        </div>\n                                        \n                                        <div className=\"action-buttons\">\n                                            <button className=\"apply-btn\">Apply All</button>\n                                            <button className=\"preview-btn\">Preview</button>\n                                        </div>\n                                    </div>\n                                    \n                                    <div className=\"improved-text\">\n                                        <div className=\"improved-header\">\n                                            <span className=\"check-icon\">✅</span>\n                                            <span>Improved Version</span>\n                                        </div>\n                                        <p className=\"typing-text\">{improvedText}</p>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"content-section\" ref={contentRef}>\n                    <div className=\"content-badge\">\n                        <span className=\"sparkle\">✨</span>\n                        AI-Powered\n                    </div>\n                    \n                    <h2 className=\"section-title\">AI Auto Edits</h2>\n                    \n                    <p className=\"section-description\">\n                        Capture naturally and have interactive voice assistants \n                        that work seamlessly. Enhance thoughts flowing into \n                        structured, polished text—quickly, reliably, naturally.\n                    </p>\n                    \n                    <div className=\"features-list\">\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">🎯</span>\n                            <span>Smart tone adjustment</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">📝</span>\n                            <span>Grammar & style fixes</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">🔄</span>\n                            <span>Content restructuring</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">✨</span>\n                            <span>Clarity enhancement</span>\n                        </div>\n                    </div>\n                    \n                    <button className=\"cta-button\">\n                        Try AI Auto Edits\n                        <span className=\"arrow\">→</span>\n                    </button>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default AIAutoEditsSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,qBAAqB;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,cAAc;QAChB;QACA;QACA;QACA;KACH;IAED,MAAM,eAAe;IACrB,MAAM,eAAe;IAErB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,UAAU,WAAW,OAAO;QAElC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAO;SAAQ,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAE/C,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,OAAO;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,SAAS;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,+BAA+B;QAC/B,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;YACX,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;QACb;QAEA,4BAA4B;QAC5B,MAAM,qBAAqB,YAAY;YACnC,qBAAqB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,MAAM;QAChE,GAAG;QAEH,OAAO;YACH,cAAc;YACd,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAwB,KAAK;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;oBAAyB,KAAK;8BACzC,cAAA,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAK,WAAU;sEAAS;;;;;;sEACzB,8OAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;0DAAG;;;;;;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DACX,cAAA,8OAAC;8DAAG;;;;;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;0EAAK;;;;;;;;;;;;kEAGV,8OAAC;wDAAI,WAAU;kEACV,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC1B,8OAAC;gEAEG,WAAW,CAAC,gBAAgB,EAAE,UAAU,oBAAoB,WAAW,IAAI;0EAE1E;+DAHI;;;;;;;;;;kEAQjB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAO,WAAU;0EAAY;;;;;;0EAC9B,8OAAC;gEAAO,WAAU;0EAAc;;;;;;;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAK,WAAU;0EAAa;;;;;;0EAC7B,8OAAC;0EAAK;;;;;;;;;;;;kEAEV,8OAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpD,8OAAC;oBAAI,WAAU;oBAAkB,KAAK;;sCAClC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAU;;;;;;gCAAQ;;;;;;;sCAItC,8OAAC;4BAAG,WAAU;sCAAgB;;;;;;sCAE9B,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;sCAMnC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAO,WAAU;;gCAAa;8CAE3B,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD;uCAEe", "debugId": null}}, {"offset": {"line": 2339, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/MultiDeviceSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/MultiDeviceSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst MultiDeviceSection = () => {\n    const sectionRef = useRef(null);\n    const devicesRef = useRef([]);\n    const contentRef = useRef([]);\n\n    const languages = [\"EN\", \"ES\", \"FR\", \"DE\", \"ZH\", \"JA\", \"KO\", \"AR\", \"HI\", \"PT\", \"RU\", \"IT\"];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const devices = devicesRef.current;\n        const contents = contentRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set(devices, { opacity: 0, y: 100, rotation: 5 });\n        gsap.set(contents, { opacity: 0, x: -50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        // Animate devices in sequence\n        tl.to(devices[0], { // Dictionary device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        })\n        .to(contents[0], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[1], { // Tones device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[1], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[2], { // Languages device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[2], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[3], { // Desktop device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[3], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animations for devices\n        devices.forEach((device, index) => {\n            if (device) {\n                gsap.to(device, {\n                    y: -10,\n                    duration: 2 + index * 0.5,\n                    ease: \"power2.inOut\",\n                    yoyo: true,\n                    repeat: -1,\n                    delay: index * 0.3\n                });\n            }\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"multi-device-section\" ref={sectionRef}>\n            <div className=\"multi-device-container\">\n                \n                {/* Dictionary Section */}\n                <div className=\"device-row\">\n                    <div className=\"device-mockup dictionary-device\" ref={el => devicesRef.current[0] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"screen-header\">\n                                <h3>Your Dictionary</h3>\n                                <span className=\"close-btn\">×</span>\n                            </div>\n                            <div className=\"dictionary-content\">\n                                <div className=\"dict-item\">Technical terms</div>\n                                <div className=\"dict-item\">Brand names</div>\n                                <div className=\"dict-item\">Custom phrases</div>\n                                <div className=\"dict-item\">Abbreviations</div>\n                                <div className=\"dict-item\">Industry jargon</div>\n                                <div className=\"dict-item\">Personal vocabulary</div>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"device-content\" ref={el => contentRef.current[0] = el}>\n                        <h3>Personal Dictionary</h3>\n                        <p>Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage across all your content.</p>\n                    </div>\n                </div>\n\n                {/* Tones Section */}\n                <div className=\"device-row reverse\">\n                    <div className=\"device-content\" ref={el => contentRef.current[1] = el}>\n                        <h3>Different tones for each app</h3>\n                        <p>Adapt your communication style with different tones optimized for each application and context, ensuring perfect messaging every time.</p>\n                    </div>\n                    <div className=\"device-mockup tones-device\" ref={el => devicesRef.current[1] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"app-selector\">\n                                <div className=\"app-icon email\">📧</div>\n                                <div className=\"app-icon chat active\">💬</div>\n                                <div className=\"app-icon social\">📱</div>\n                            </div>\n                            <div className=\"tones-content\">\n                                <div className=\"tone-option\">Professional</div>\n                                <div className=\"tone-option active\">Casual</div>\n                                <div className=\"tone-option\">Friendly</div>\n                                <div className=\"tone-option\">Formal</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Languages Section */}\n                <div className=\"device-row\">\n                    <div className=\"device-mockup languages-device\" ref={el => devicesRef.current[2] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"language-wheel\">\n                                {languages.map((lang, index) => (\n                                    <div \n                                        key={lang}\n                                        className=\"language-item\"\n                                        style={{\n                                            transform: `rotate(${index * 30}deg) translateY(-80px) rotate(-${index * 30}deg)`\n                                        }}\n                                    >\n                                        {lang}\n                                    </div>\n                                ))}\n                                <div className=\"wheel-center\">\n                                    <span className=\"lang-count\">100+</span>\n                                    <span className=\"lang-text\">Languages</span>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"device-content\" ref={el => contentRef.current[2] = el}>\n                        <h3>100+ languages</h3>\n                        <p>Communicate globally with support for over 100 languages and seamless translation capabilities, breaking down language barriers effortlessly.</p>\n                    </div>\n                </div>\n\n                {/* Desktop Section */}\n                <div className=\"device-row reverse\">\n                    <div className=\"device-content\" ref={el => contentRef.current[3] = el}>\n                        <h3>On-the-go or at your desk</h3>\n                        <p>Work from anywhere with seamless synchronization across all your devices. Whether mobile or desktop, your workflow stays uninterrupted and efficient.</p>\n                        <button className=\"cta-button\">Get started</button>\n                    </div>\n                    <div className=\"device-mockup desktop-device\" ref={el => devicesRef.current[3] = el}>\n                        <div className=\"desktop-screen\">\n                            <div className=\"desktop-header\">\n                                <div className=\"window-controls\">\n                                    <span className=\"control red\"></span>\n                                    <span className=\"control yellow\"></span>\n                                    <span className=\"control green\"></span>\n                                </div>\n                                <div className=\"window-title\">Flow Desktop</div>\n                            </div>\n                            <div className=\"desktop-content\">\n                                <div className=\"sidebar\">\n                                    <div className=\"sidebar-item active\">📝 Documents</div>\n                                    <div className=\"sidebar-item\">🎤 Voice Notes</div>\n                                    <div className=\"sidebar-item\">📊 Analytics</div>\n                                    <div className=\"sidebar-item\">⚙️ Settings</div>\n                                </div>\n                                <div className=\"main-content\">\n                                    <div className=\"document-header\">\n                                        <h4>📋 Crazy product ideas</h4>\n                                        <span className=\"doc-status\">Synced</span>\n                                    </div>\n                                    <div className=\"document-body\">\n                                        <div className=\"text-line\"></div>\n                                        <div className=\"text-line short\"></div>\n                                        <div className=\"text-line\"></div>\n                                        <div className=\"text-line medium\"></div>\n                                    </div>\n                                </div>\n                            </div>\n                            <div className=\"mobile-preview\">\n                                <div className=\"mobile-screen\">\n                                    <div className=\"mobile-header\">Flow Mobile</div>\n                                    <div className=\"voice-indicator\">\n                                        <div className=\"voice-wave\"></div>\n                                        <div className=\"voice-wave\"></div>\n                                        <div className=\"voice-wave\"></div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n            </div>\n        </section>\n    );\n};\n\nexport default MultiDeviceSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,qBAAqB;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC5B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE5B,MAAM,YAAY;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE1F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,WAAW,WAAW,OAAO;QAEnC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS;YAAE,SAAS;YAAG,GAAG;YAAK,UAAU;QAAE;QACpD,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAExC,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,8BAA8B;QAC9B,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACd,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,kCAAkC;QAClC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACrB,IAAI,QAAQ;gBACR,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,QAAQ;oBACZ,GAAG,CAAC;oBACJ,UAAU,IAAI,QAAQ;oBACtB,MAAM;oBACN,MAAM;oBACN,QAAQ,CAAC;oBACT,OAAO,QAAQ;gBACnB;YACJ;QACJ;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAuB,KAAK;kBAC3C,cAAA,8OAAC;YAAI,WAAU;;8BAGX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAkC,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAChF,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAKX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;sCAEP,8OAAC;4BAAI,WAAU;4BAA6B,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC3E,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAiB;;;;;;0DAChC,8OAAC;gDAAI,WAAU;0DAAuB;;;;;;0DACtC,8OAAC;gDAAI,WAAU;0DAAkB;;;;;;;;;;;;kDAErC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAiC,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC/E,cAAA,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAI,WAAU;;wCACV,UAAU,GAAG,CAAC,CAAC,MAAM,sBAClB,8OAAC;gDAEG,WAAU;gDACV,OAAO;oDACH,WAAW,CAAC,OAAO,EAAE,QAAQ,GAAG,+BAA+B,EAAE,QAAQ,GAAG,IAAI,CAAC;gDACrF;0DAEC;+CANI;;;;;sDASb,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAK,WAAU;8DAAa;;;;;;8DAC7B,8OAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAKX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAO,WAAU;8CAAa;;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;4BAA+B,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC7E,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;;;;;;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DAAe;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,8OAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,8OAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,8OAAC;wDAAI,WAAU;kEAAe;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;gEAAK,WAAU;0EAAa;;;;;;;;;;;;kEAEjC,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAI3B,8OAAC;wCAAI,WAAU;kDACX,cAAA,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD;uCAEe", "debugId": null}}]}