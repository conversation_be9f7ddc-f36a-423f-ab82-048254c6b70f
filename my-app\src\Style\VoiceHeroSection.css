/* Voice Hero Section */
.voice-hero-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 30%, #fed7aa 70%, #fdba74 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 100px rgba(245, 158, 11, 0.1);
}

.voice-hero-container {
  max-width: 1200px;
  width: 100%;
  text-align: center;
  position: relative;
}

.curved-text-container {
  position: absolute;
  top: -100px;
  right: 10%;
  width: 200px;
  height: 200px;
  z-index: 1;
}

.curved-svg {
  width: 100%;
  height: 100%;
}

.curved-text-path {
  font-size: 12px;
  font-weight: 400;
  fill: #a3a3a3;
  font-family: 'Arial', sans-serif;
}

.voice-indicator-container {
  position: absolute;
  top: 20%;
  left: 15%;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 2;
}

.voice-indicator {
  background: #1f2937;
  border-radius: 20px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.voice-wave {
  width: 3px;
  height: 20px;
  background: #3b82f6;
  border-radius: 2px;
  animation: wave 1s ease-in-out infinite;
}

.voice-wave:nth-child(1) { animation-delay: 0s; }
.voice-wave:nth-child(2) { animation-delay: 0.1s; }
.voice-wave:nth-child(3) { animation-delay: 0.2s; }
.voice-wave:nth-child(4) { animation-delay: 0.3s; }
.voice-wave:nth-child(5) { animation-delay: 0.4s; }
.voice-wave:nth-child(6) { animation-delay: 0.5s; }
.voice-wave:nth-child(7) { animation-delay: 0.6s; }
.voice-wave:nth-child(8) { animation-delay: 0.7s; }

@keyframes wave {
  0%, 100% { 
    transform: scaleY(0.3);
    opacity: 0.7;
  }
  50% { 
    transform: scaleY(1);
    opacity: 1;
  }
}

.voice-label {
  background: white;
  color: #1f2937;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

.voice-label::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid white;
}

.main-content {
  z-index: 3;
  position: relative;
  margin-top: 60px;
}

.hero-title {
  font-size: 4.5rem;
  font-weight: 400;
  color: #1f2937;
  margin: 0 0 24px 0;
  line-height: 1.1;
  font-family: 'Georgia', serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.highlight {
  color: #1f2937;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
  font-weight: 600;
  position: relative;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 4px;
  z-index: -1;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #374151;
  margin: 0 0 40px 0;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.primary-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.secondary-btn {
  background: transparent;
  color: #1f2937;
  border: 2px solid #d1d5db;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-2px);
}

.availability {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .curved-text-container {
    top: -80px;
    right: 5%;
    width: 160px;
    height: 160px;
  }
  
  .voice-indicator-container {
    top: 25%;
    left: 10%;
  }
  
  .hero-title {
    font-size: 3.5rem;
  }
}

@media (max-width: 768px) {
  .voice-hero-section {
    padding: 60px 20px;
  }
  
  .curved-text-container {
    display: none;
  }
  
  .voice-indicator-container {
    position: static;
    justify-content: center;
    margin-bottom: 40px;
  }
  
  .main-content {
    margin-top: 0;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .primary-btn,
  .secondary-btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .voice-hero-section {
    padding: 40px 15px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .voice-indicator {
    padding: 10px 14px;
  }
  
  .voice-wave {
    width: 2px;
    height: 16px;
  }
  
  .voice-label {
    font-size: 12px;
    padding: 6px 10px;
  }
}
