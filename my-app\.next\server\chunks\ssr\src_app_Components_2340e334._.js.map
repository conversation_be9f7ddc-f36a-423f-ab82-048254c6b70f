{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/StartFlowingSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/StartFlowingSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst StartFlowingSection = () => {\n    const sectionRef = useRef(null);\n    const contentRef = useRef(null);\n    const dottedLineRef = useRef(null);\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const content = contentRef.current;\n        const dottedLine = dottedLineRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([content], { opacity: 0, y: 50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 1,\n            ease: \"power3.out\"\n        });\n\n        // Animate the dotted line\n        if (dottedLine) {\n            gsap.set(dottedLine, { \n                strokeDasharray: \"10,10\",\n                strokeDashoffset: 0\n            });\n\n            gsap.to(dottedLine, {\n                strokeDashoffset: -20,\n                duration: 2,\n                ease: \"none\",\n                repeat: -1\n            });\n        }\n\n        // Floating animation for the entire content\n        gsap.to(content, {\n            y: -10,\n            duration: 4,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1,\n            delay: 1\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"start-flowing-section\" ref={sectionRef}>\n            <div className=\"background-overlay\"></div>\n            <div className=\"background-blur\"></div>\n            \n            <svg className=\"dotted-line-svg\" viewBox=\"0 0 1200 800\" preserveAspectRatio=\"xMidYMid slice\">\n                <path\n                    ref={dottedLineRef}\n                    className=\"dotted-line\"\n                    d=\"M 800 100 Q 1000 200 900 400 Q 800 600 1100 700\"\n                    fill=\"none\"\n                    stroke=\"rgba(255, 255, 255, 0.6)\"\n                    strokeWidth=\"3\"\n                    strokeLinecap=\"round\"\n                />\n            </svg>\n\n            <div className=\"start-flowing-container\">\n                <div className=\"content-wrapper\" ref={contentRef}>\n                    <h1 className=\"main-title\">\n                        Start flowing\n                        <span className=\"title-dots\">.....</span>\n                    </h1>\n                    \n                    <p className=\"subtitle\">\n                        Effortless voice dictation in every application. 4x<br />\n                        faster than typing, AI commands and auto-edits.\n                    </p>\n                    \n                    <div className=\"cta-buttons\">\n                        <button className=\"primary-cta\">\n                            🎤 Try Flow\n                        </button>\n                        <button className=\"secondary-cta\">\n                            Download\n                        </button>\n                    </div>\n                    \n                    <p className=\"availability-text\">\n                        Available on Mac, Windows and iPhone\n                    </p>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default StartFlowingSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,sBAAsB;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,aAAa,cAAc,OAAO;QAExC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;SAAQ,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAExC,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,SAAS;YACX,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV;QAEA,0BAA0B;QAC1B,IAAI,YAAY;YACZ,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,YAAY;gBACjB,iBAAiB;gBACjB,kBAAkB;YACtB;YAEA,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,YAAY;gBAChB,kBAAkB,CAAC;gBACnB,UAAU;gBACV,MAAM;gBACN,QAAQ,CAAC;YACb;QACJ;QAEA,4CAA4C;QAC5C,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;YACb,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;YACT,OAAO;QACX;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAwB,KAAK;;0BAC5C,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;gBAAkB,SAAQ;gBAAe,qBAAoB;0BACxE,cAAA,8OAAC;oBACG,KAAK;oBACL,WAAU;oBACV,GAAE;oBACF,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAI,WAAU;oBAAkB,KAAK;;sCAClC,8OAAC;4BAAG,WAAU;;gCAAa;8CAEvB,8OAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;;sCAGjC,8OAAC;4BAAE,WAAU;;gCAAW;8CAC+B,8OAAC;;;;;gCAAK;;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;sCAKtC,8OAAC;4BAAE,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/VoiceHeroSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport \"@/Style/VoiceHeroSection.css\";\n\nconst VoiceHeroSection = () => {\n    const sectionRef = useRef(null);\n    const curvedTextRef = useRef(null);\n    const voiceIndicatorRef = useRef(null);\n    const mainTextRef = useRef(null);\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const curvedText = curvedTextRef.current;\n        const voiceIndicator = voiceIndicatorRef.current;\n        const mainText = mainTextRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([curvedText, voiceIndicator, mainText], { opacity: 0, y: 30 });\n\n        // Create entrance timeline\n        const tl = gsap.timeline({ delay: 0.5 });\n\n        tl.to(curvedText, {\n            opacity: 1,\n            y: 0,\n            duration: 1,\n            ease: \"power3.out\"\n        })\n        .to(voiceIndicator, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.5\")\n        .to(mainText, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Animate curved text continuously\n        gsap.to(curvedText, {\n            rotation: 360,\n            duration: 20,\n            ease: \"none\",\n            repeat: -1\n        });\n\n        // Voice indicator animation\n        const waves = voiceIndicator?.querySelectorAll('.voice-wave');\n        if (waves) {\n            waves.forEach((wave, index) => {\n                gsap.to(wave, {\n                    scaleY: Math.random() * 0.5 + 0.5,\n                    duration: 0.5 + Math.random() * 0.5,\n                    ease: \"power2.inOut\",\n                    yoyo: true,\n                    repeat: -1,\n                    delay: index * 0.1\n                });\n            });\n        }\n\n        return () => {\n            gsap.killTweensOf([curvedText, voiceIndicator, mainText]);\n        };\n    }, []);\n\n    return (\n        <section className=\"voice-hero-section\" ref={sectionRef}>\n            <div className=\"voice-hero-container\">\n                <div className=\"curved-text-container\">\n                    <div className=\"curved-text\" ref={curvedTextRef}>\n                        <svg viewBox=\"0 0 200 200\" className=\"curved-svg\">\n                            <defs>\n                                <path\n                                    id=\"circle\"\n                                    d=\"M 100, 100 m -75, 0 a 75,75 0 1,1 150,0 a 75,75 0 1,1 -150,0\"\n                                />\n                            </defs>\n                            <text className=\"curved-text-path\">\n                                <textPath href=\"#circle\" startOffset=\"0%\">\n                                    their meeting were sent out, or mentioned it but didn't capture it •\n                                </textPath>\n                            </text>\n                        </svg>\n                    </div>\n                </div>\n\n                <div className=\"voice-indicator-container\" ref={voiceIndicatorRef}>\n                    <div className=\"voice-indicator\">\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                    </div>\n                    <div className=\"voice-label\">meeting were sent out, or</div>\n                </div>\n\n                <div className=\"main-content\" ref={mainTextRef}>\n                    <h1 className=\"hero-title\">\n                        Don't type, <span className=\"highlight\">just speak</span>\n                    </h1>\n                    \n                    <p className=\"hero-subtitle\">\n                        Effortless voice dictation in every application:<br />\n                        4x faster than typing, AI commands and auto-edits.\n                    </p>\n\n                    <div className=\"cta-buttons\">\n                        <button className=\"primary-btn\">\n                            🎤 Try Flow\n                        </button>\n                        <button className=\"secondary-btn\">\n                            Download\n                        </button>\n                    </div>\n\n                    <p className=\"availability\">\n                        Available on Mac, Windows and iPhone\n                    </p>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default VoiceHeroSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;;AAKA,MAAM,mBAAmB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,aAAa,cAAc,OAAO;QACxC,MAAM,iBAAiB,kBAAkB,OAAO;QAChD,MAAM,WAAW,YAAY,OAAO;QAEpC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAY;YAAgB;SAAS,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAErE,2BAA2B;QAC3B,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YAAE,OAAO;QAAI;QAEtC,GAAG,EAAE,CAAC,YAAY;YACd,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,gBAAgB;YAChB,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,UAAU;YACV,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,mCAAmC;QACnC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,YAAY;YAChB,UAAU;YACV,UAAU;YACV,MAAM;YACN,QAAQ,CAAC;QACb;QAEA,4BAA4B;QAC5B,MAAM,QAAQ,gBAAgB,iBAAiB;QAC/C,IAAI,OAAO;YACP,MAAM,OAAO,CAAC,CAAC,MAAM;gBACjB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACV,QAAQ,KAAK,MAAM,KAAK,MAAM;oBAC9B,UAAU,MAAM,KAAK,MAAM,KAAK;oBAChC,MAAM;oBACN,MAAM;oBACN,QAAQ,CAAC;oBACT,OAAO,QAAQ;gBACnB;YACJ;QACJ;QAEA,OAAO;YACH,6IAAA,CAAA,OAAI,CAAC,YAAY,CAAC;gBAAC;gBAAY;gBAAgB;aAAS;QAC5D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAqB,KAAK;kBACzC,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAI,WAAU;wBAAc,KAAK;kCAC9B,cAAA,8OAAC;4BAAI,SAAQ;4BAAc,WAAU;;8CACjC,8OAAC;8CACG,cAAA,8OAAC;wCACG,IAAG;wCACH,GAAE;;;;;;;;;;;8CAGV,8OAAC;oCAAK,WAAU;8CACZ,cAAA,8OAAC;wCAAS,MAAK;wCAAU,aAAY;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1D,8OAAC;oBAAI,WAAU;oBAA4B,KAAK;;sCAC5C,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEnB,8OAAC;4BAAI,WAAU;sCAAc;;;;;;;;;;;;8BAGjC,8OAAC;oBAAI,WAAU;oBAAe,KAAK;;sCAC/B,8OAAC;4BAAG,WAAU;;gCAAa;8CACX,8OAAC;oCAAK,WAAU;8CAAY;;;;;;;;;;;;sCAG5C,8OAAC;4BAAE,WAAU;;gCAAgB;8CACuB,8OAAC;;;;;gCAAK;;;;;;;sCAI1D,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;sCAKtC,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe", "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Home.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\r\nimport \"@/Style/HomeSection.css\";\r\n\r\n// Register ScrollTrigger plugin\r\nif (typeof window !== \"undefined\") {\r\n    gsap.registerPlugin(ScrollTrigger);\r\n}\r\n\r\nconst HomeSection = () => {\r\n    const sectionRef = useRef(null);\r\n    const contentRef = useRef(null);\r\n    const statsRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const section = sectionRef.current;\r\n        const content = contentRef.current;\r\n        const stats = statsRef.current;\r\n\r\n        if (!section) return;\r\n\r\n        // Initial setup\r\n        gsap.set([content, stats], { opacity: 0, y: 50 });\r\n\r\n        // Create timeline for entrance animations\r\n        const tl = gsap.timeline({\r\n            scrollTrigger: {\r\n                trigger: section,\r\n                start: \"top 70%\",\r\n                toggleActions: \"play none none reverse\"\r\n            }\r\n        });\r\n\r\n        tl.to(content, {\r\n            opacity: 1,\r\n            y: 0,\r\n            duration: 0.8,\r\n            ease: \"power3.out\"\r\n        })\r\n        .to(stats, {\r\n            opacity: 1,\r\n            y: 0,\r\n            duration: 0.8,\r\n            ease: \"power3.out\"\r\n        }, \"-=0.4\");\r\n\r\n        return () => {\r\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\r\n        };\r\n    }, []);\r\n\r\n    return (\r\n        <section className=\"home-section\" ref={sectionRef}>\r\n            <div className=\"home-container\">\r\n                <div className=\"content-grid\">\r\n                    <div className=\"text-content\" ref={contentRef}>\r\n                        <div className=\"section-badge\">\r\n                            ✨ Powered by AI\r\n                        </div>\r\n                        <h2 className=\"section-title\">\r\n                            Transform your voice into<br />\r\n                            <span className=\"gradient-text\">perfect text</span>\r\n                        </h2>\r\n                        <p className=\"section-description\">\r\n                            Experience the future of productivity with Flow's advanced AI voice dictation.\r\n                            Speak naturally and watch your thoughts transform into polished, professional text\r\n                            across any application.\r\n                        </p>\r\n                        <div className=\"feature-list\">\r\n                            <div className=\"feature-item\">\r\n                                <span className=\"feature-icon\">🎯</span>\r\n                                <span>99.9% accuracy with AI enhancement</span>\r\n                            </div>\r\n                            <div className=\"feature-item\">\r\n                                <span className=\"feature-icon\">⚡</span>\r\n                                <span>4x faster than traditional typing</span>\r\n                            </div>\r\n                            <div className=\"feature-item\">\r\n                                <span className=\"feature-icon\">🔄</span>\r\n                                <span>Real-time auto-corrections and formatting</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"visual-content\" ref={statsRef}>\r\n                        <div className=\"stats-grid\">\r\n                            <div className=\"stat-card\">\r\n                                <div className=\"stat-number\">4x</div>\r\n                                <div className=\"stat-label\">Faster than typing</div>\r\n                            </div>\r\n                            <div className=\"stat-card\">\r\n                                <div className=\"stat-number\">99.9%</div>\r\n                                <div className=\"stat-label\">Accuracy rate</div>\r\n                            </div>\r\n                            <div className=\"stat-card\">\r\n                                <div className=\"stat-number\">50+</div>\r\n                                <div className=\"stat-label\">Languages supported</div>\r\n                            </div>\r\n                            <div className=\"stat-card\">\r\n                                <div className=\"stat-number\">1M+</div>\r\n                                <div className=\"stat-label\">Happy users</div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"demo-card\">\r\n                            <div className=\"demo-header\">\r\n                                <div className=\"demo-title\">Live Demo</div>\r\n                                <div className=\"demo-status\">\r\n                                    <span className=\"status-dot\"></span>\r\n                                    Recording\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"demo-content\">\r\n                                <div className=\"waveform\">\r\n                                    <div className=\"wave-bar\"></div>\r\n                                    <div className=\"wave-bar\"></div>\r\n                                    <div className=\"wave-bar\"></div>\r\n                                    <div className=\"wave-bar\"></div>\r\n                                    <div className=\"wave-bar\"></div>\r\n                                    <div className=\"wave-bar\"></div>\r\n                                    <div className=\"wave-bar\"></div>\r\n                                    <div className=\"wave-bar\"></div>\r\n                                </div>\r\n                                <div className=\"demo-text\">\r\n                                    \"Transform your voice into perfect text with Flow's AI-powered dictation...\"\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    );\r\n};\r\n\r\nexport default HomeSection;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,cAAc;IAChB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAE9B,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAS;SAAM,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAE/C,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,SAAS;YACX,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,OAAO;YACP,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAe,KAAK;kBACnC,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;wBAAe,KAAK;;0CAC/B,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAG/B,8OAAC;gCAAG,WAAU;;oCAAgB;kDACD,8OAAC;;;;;kDAC1B,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAEpC,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;0CAKnC,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;0DAAK;;;;;;;;;;;;kDAEV,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;0DAAK;;;;;;;;;;;;kDAEV,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;wBAAI,WAAU;wBAAiB,KAAK;;0CACjC,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAa;;;;;;;;;;;;;;;;;;0CAIpC,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAa;;;;;;0DAC5B,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAK,WAAU;;;;;;oDAAoB;;;;;;;;;;;;;kDAI5C,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D;uCAEe", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Marquee.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport \"@/Style/Marquee.css\";\r\n\r\nconst Marquee = ({\r\n    items = [\r\n        \"🎤 Voice-First Productivity\",\r\n        \"⚡ 4x Faster Than Typing\",\r\n        \"🤖 AI-Powered Accuracy\",\r\n        \"🌍 50+ Languages Supported\",\r\n        \"📱 Cross-Platform Ready\",\r\n        \"🔄 Real-Time Corrections\",\r\n        \"✨ Smart Auto-Formatting\",\r\n        \"🎯 99.9% Accuracy Rate\"\r\n    ],\r\n    speed = 1, // speed multiplier\r\n    colors = [\"#f59e0b\", \"#10b981\", \"#3b82f6\", \"#8b5cf6\", \"#ef4444\", \"#06b6d4\", \"#84cc16\", \"#f97316\"]\r\n}) => {\r\n    const wrapperRef = useRef(null);\r\n    const animationRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const wrapper = wrapperRef.current;\r\n        if (!wrapper) return;\r\n\r\n        const marqueeItems = wrapper.querySelectorAll(\".marquee-item\");\r\n\r\n        // Apply background colors\r\n        gsap.set(marqueeItems, {\r\n            backgroundColor: gsap.utils.wrap(colors),\r\n        });\r\n\r\n        // Create infinite scroll animation\r\n        const createAnimation = () => {\r\n            if (marqueeItems.length === 0) return;\r\n\r\n            // Calculate the total width of one set of items\r\n            let totalWidth = 0;\r\n            const itemCount = items.length; // Original items count\r\n\r\n            for (let i = 0; i < itemCount; i++) {\r\n                if (marqueeItems[i]) {\r\n                    totalWidth += marqueeItems[i].offsetWidth + 20; // 20px margin\r\n                }\r\n            }\r\n\r\n            // Animate the wrapper to move left infinitely\r\n            animationRef.current = gsap.to(wrapper, {\r\n                x: -totalWidth,\r\n                duration: totalWidth / (50 * speed), // Adjust duration based on speed\r\n                ease: \"none\",\r\n                repeat: -1,\r\n            });\r\n        };\r\n\r\n        // Wait for layout to be ready\r\n        const timer = setTimeout(createAnimation, 200);\r\n\r\n        return () => {\r\n            clearTimeout(timer);\r\n            if (animationRef.current) {\r\n                animationRef.current.kill();\r\n            }\r\n        };\r\n    }, [items, speed, colors]);\r\n    return (\r\n        <div className=\"marquee-container\">\r\n            <div className=\"marquee-wrapper\" ref={wrapperRef}>\r\n                {/* Duplicate items for seamless infinite loop */}\r\n                {[...items, ...items].map((item, index) => (\r\n                    <div key={index} className=\"marquee-item\">\r\n                        <div className=\"marquee-content\">\r\n                            {item}\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Marquee;"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;;AAKA,MAAM,UAAU,CAAC,EACb,QAAQ;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH,EACD,QAAQ,CAAC,EACT,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU,EACpG;IACG,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,IAAI,CAAC,SAAS;QAEd,MAAM,eAAe,QAAQ,gBAAgB,CAAC;QAE9C,0BAA0B;QAC1B,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,cAAc;YACnB,iBAAiB,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACrC;QAEA,mCAAmC;QACnC,MAAM,kBAAkB;YACpB,IAAI,aAAa,MAAM,KAAK,GAAG;YAE/B,gDAAgD;YAChD,IAAI,aAAa;YACjB,MAAM,YAAY,MAAM,MAAM,EAAE,uBAAuB;YAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAChC,IAAI,YAAY,CAAC,EAAE,EAAE;oBACjB,cAAc,YAAY,CAAC,EAAE,CAAC,WAAW,GAAG,IAAI,cAAc;gBAClE;YACJ;YAEA,8CAA8C;YAC9C,aAAa,OAAO,GAAG,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;gBACpC,GAAG,CAAC;gBACJ,UAAU,aAAa,CAAC,KAAK,KAAK;gBAClC,MAAM;gBACN,QAAQ,CAAC;YACb;QACJ;QAEA,8BAA8B;QAC9B,MAAM,QAAQ,WAAW,iBAAiB;QAE1C,OAAO;YACH,aAAa;YACb,IAAI,aAAa,OAAO,EAAE;gBACtB,aAAa,OAAO,CAAC,IAAI;YAC7B;QACJ;IACJ,GAAG;QAAC;QAAO;QAAO;KAAO;IACzB,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;YAAkB,KAAK;sBAEjC;mBAAI;mBAAU;aAAM,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;oBAAgB,WAAU;8BACvB,cAAA,8OAAC;wBAAI,WAAU;kCACV;;;;;;mBAFC;;;;;;;;;;;;;;;AAS9B;uCAEe", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/FlowSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/FlowSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst FlowSection = () => {\n    const sectionRef = useRef(null);\n    const titleRef = useRef(null);\n    const tagsRef = useRef([]);\n    const contentRef = useRef(null);\n    const illustrationRef = useRef(null);\n\n    const professionTags = [\n        \"Accessibility\", \"Consultants\", \"Creators\", \"Customer Support\",\n        \"Designers\", \"HR\", \"Managers\", \"Publishers\", \"Ergonomics\",\n        \"Freelancers\", \"Government\", \"Healthcare\", \"Individuals\",\n        \"Journalists\", \"Lawyers\", \"Multilingual\", \"Product\", \"Sales\",\n        \"Slower Typists\", \"Students\", \"Teams\", \"Writers\"\n    ];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const title = titleRef.current;\n        const tags = tagsRef.current;\n        const content = contentRef.current;\n        const illustration = illustrationRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([title, content, illustration], { opacity: 0, y: 50 });\n        gsap.set(tags, { opacity: 0, scale: 0.8 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 80%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(title, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(tags, {\n            opacity: 1,\n            scale: 1,\n            duration: 0.6,\n            stagger: 0.05,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.4\")\n        .to([content, illustration], {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            stagger: 0.2,\n            ease: \"power3.out\"\n        }, \"-=0.3\");\n\n        // Floating animation for illustration\n        gsap.to(illustration, {\n            y: -10,\n            duration: 2,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"flow-section\" ref={sectionRef}>\n            <div className=\"flow-container\">\n                <div className=\"flow-content\">\n                    <div className=\"flow-left\">\n                        <h1 className=\"flow-title\" ref={titleRef}>\n                            Flow is made<br />for you\n                        </h1>\n                        \n                        <div className=\"profession-tags\">\n                            {professionTags.map((tag, index) => (\n                                <span \n                                    key={index}\n                                    className=\"profession-tag\"\n                                    ref={el => tagsRef.current[index] = el}\n                                >\n                                    {tag}\n                                </span>\n                            ))}\n                        </div>\n                    </div>\n\n                    <div className=\"flow-right\">\n                        <div className=\"flow-accessibility\" ref={contentRef}>\n                            <h2 className=\"accessibility-title\">Flow for Accessibility</h2>\n                            <p className=\"accessibility-description\">\n                                Your voice deserves a shortcut. Flow supports anyone who \n                                feels slowed down by a keyboard by turning speech into \n                                structured, polished text—quickly, reliably, naturally.\n                            </p>\n                            <button className=\"get-started-btn\">Get started</button>\n                        </div>\n\n                        <div className=\"flow-illustration\" ref={illustrationRef}>\n                            <div className=\"character-container\">\n                                <div className=\"character\">\n                                    <div className=\"character-head\">\n                                        <div className=\"character-face\">\n                                            <div className=\"eye left-eye\"></div>\n                                            <div className=\"eye right-eye\"></div>\n                                            <div className=\"mouth\"></div>\n                                        </div>\n                                        <div className=\"character-ears\">\n                                            <div className=\"ear left-ear\"></div>\n                                            <div className=\"ear right-ear\"></div>\n                                        </div>\n                                    </div>\n                                    <div className=\"character-body\">\n                                        <div className=\"character-arms\">\n                                            <div className=\"arm left-arm\"></div>\n                                            <div className=\"arm right-arm\"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                                <div className=\"speech-bubble\">\n                                    <div className=\"bubble-content\">\n                                        <div className=\"text-lines\">\n                                            <div className=\"text-line\"></div>\n                                            <div className=\"text-line\"></div>\n                                            <div className=\"text-line short\"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default FlowSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,cAAc;IAChB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,iBAAiB;QACnB;QAAiB;QAAe;QAAY;QAC5C;QAAa;QAAM;QAAY;QAAc;QAC7C;QAAe;QAAc;QAAc;QAC3C;QAAe;QAAW;QAAgB;QAAW;QACrD;QAAkB;QAAY;QAAS;KAC1C;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,OAAO,QAAQ,OAAO;QAC5B,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,eAAe,gBAAgB,OAAO;QAE5C,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAO;YAAS;SAAa,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7D,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAExC,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,OAAO;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;YACV,SAAS;YACT,MAAM;QACV,GAAG,SACF,EAAE,CAAC;YAAC;YAAS;SAAa,EAAE;YACzB,SAAS;YACT,GAAG;YACH,UAAU;YACV,SAAS;YACT,MAAM;QACV,GAAG;QAEH,sCAAsC;QACtC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,cAAc;YAClB,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;QACb;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAe,KAAK;kBACnC,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAG,WAAU;gCAAa,KAAK;;oCAAU;kDAC1B,8OAAC;;;;;oCAAK;;;;;;;0CAGtB,8OAAC;gCAAI,WAAU;0CACV,eAAe,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;wCAEG,WAAU;wCACV,KAAK,CAAA,KAAM,QAAQ,OAAO,CAAC,MAAM,GAAG;kDAEnC;uCAJI;;;;;;;;;;;;;;;;kCAUrB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;gCAAqB,KAAK;;kDACrC,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDAKzC,8OAAC;wCAAO,WAAU;kDAAkB;;;;;;;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;gCAAoB,KAAK;0CACpC,cAAA,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAGvB,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI3B,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;gDAAI,WAAU;0DACX,cAAA,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D;uCAEe", "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/ContentImageSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/ContentImageSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst ContentImageSection = () => {\n    const sectionRef = useRef(null);\n    const contentRef = useRef(null);\n    const imageRef = useRef(null);\n    const featuresRef = useRef([]);\n\n    const features = [\n        {\n            icon: \"🎯\",\n            title: \"Precision & Accuracy\",\n            description: \"Advanced AI algorithms ensure your voice is converted to text with remarkable precision and context awareness.\"\n        },\n        {\n            icon: \"⚡\",\n            title: \"Lightning Fast\",\n            description: \"Real-time processing means your thoughts become text instantly, keeping up with your natural speaking pace.\"\n        },\n        {\n            icon: \"🌐\",\n            title: \"Multi-Language Support\",\n            description: \"Communicate in over 100 languages with seamless translation and localization capabilities.\"\n        },\n        {\n            icon: \"🔒\",\n            title: \"Privacy First\",\n            description: \"Your data stays secure with end-to-end encryption and local processing options for sensitive content.\"\n        }\n    ];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const content = contentRef.current;\n        const image = imageRef.current;\n        const featureElements = featuresRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([content, image], { opacity: 0, y: 50 });\n        gsap.set(featureElements, { opacity: 0, x: -30 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(image, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(featureElements, {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            stagger: 0.1,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Parallax effect for image\n        gsap.to(image, {\n            y: -20,\n            scrollTrigger: {\n                trigger: section,\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: 1\n            }\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"content-image-section\" ref={sectionRef}>\n            <div className=\"content-image-container\">\n                <div className=\"content-side\" ref={contentRef}>\n                    <div className=\"content-header\">\n                        <span className=\"content-badge\">Why Choose Flow</span>\n                        <h2 className=\"content-title\">\n                            Transform Your Voice Into \n                            <span className=\"highlight\"> Powerful Content</span>\n                        </h2>\n                        <p className=\"content-description\">\n                            Experience the future of content creation with our advanced voice-to-text \n                            technology. Whether you're writing emails, creating documents, or brainstorming \n                            ideas, Flow makes it effortless and natural.\n                        </p>\n                    </div>\n\n                    <div className=\"features-grid\">\n                        {features.map((feature, index) => (\n                            <div \n                                key={index}\n                                className=\"feature-item\"\n                                ref={el => featuresRef.current[index] = el}\n                            >\n                                <div className=\"feature-icon\">{feature.icon}</div>\n                                <div className=\"feature-content\">\n                                    <h3 className=\"feature-title\">{feature.title}</h3>\n                                    <p className=\"feature-description\">{feature.description}</p>\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n\n                    <div className=\"content-actions\">\n                        <button className=\"primary-btn\">Start Free Trial</button>\n                        <button className=\"secondary-btn\">Watch Demo</button>\n                    </div>\n                </div>\n\n                <div className=\"image-side\" ref={imageRef}>\n                    <div className=\"image-container\">\n                        <div className=\"main-device\">\n                            <div className=\"device-screen\">\n                                <div className=\"screen-header\">\n                                    <div className=\"screen-dots\">\n                                        <span className=\"dot red\"></span>\n                                        <span className=\"dot yellow\"></span>\n                                        <span className=\"dot green\"></span>\n                                    </div>\n                                    <div className=\"screen-title\">Flow - Voice to Text</div>\n                                </div>\n                                <div className=\"screen-content\">\n                                    <div className=\"voice-wave\">\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                    </div>\n                                    <div className=\"text-output\">\n                                        <div className=\"typing-text\">\n                                            <span>Hello, this is a demonstration of Flow's</span>\n                                            <span className=\"cursor\">|</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                        \n                        <div className=\"floating-elements\">\n                            <div className=\"floating-card card-1\">\n                                <div className=\"card-icon\">🎤</div>\n                                <div className=\"card-text\">Voice Input</div>\n                            </div>\n                            <div className=\"floating-card card-2\">\n                                <div className=\"card-icon\">📝</div>\n                                <div className=\"card-text\">Text Output</div>\n                            </div>\n                            <div className=\"floating-card card-3\">\n                                <div className=\"card-icon\">⚡</div>\n                                <div className=\"card-text\">Real-time</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default ContentImageSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,sBAAsB;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE7B,MAAM,WAAW;QACb;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;KACH;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,kBAAkB,YAAY,OAAO;QAE3C,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAS;SAAM,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/C,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,iBAAiB;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAE/C,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,SAAS;YACX,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,OAAO;YACP,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,iBAAiB;YACjB,SAAS;YACT,GAAG;YACH,UAAU;YACV,SAAS;YACT,MAAM;QACV,GAAG;QAEH,4BAA4B;QAC5B,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;YACX,GAAG,CAAC;YACJ,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,OAAO;YACX;QACJ;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAwB,KAAK;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;oBAAe,KAAK;;sCAC/B,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAG,WAAU;;wCAAgB;sDAE1B,8OAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;8CAEhC,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;sCACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,8OAAC;oCAEG,WAAU;oCACV,KAAK,CAAA,KAAM,YAAY,OAAO,CAAC,MAAM,GAAG;;sDAExC,8OAAC;4CAAI,WAAU;sDAAgB,QAAQ,IAAI;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAG,WAAU;8DAAiB,QAAQ,KAAK;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAAuB,QAAQ,WAAW;;;;;;;;;;;;;mCAPtD;;;;;;;;;;sCAajB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAO,WAAU;8CAAc;;;;;;8CAChC,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAI1C,8OAAC;oBAAI,WAAU;oBAAa,KAAK;8BAC7B,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;;;;;;;;;;;;8DAEpB,8OAAC;oDAAI,WAAU;8DAAe;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;uCAEe", "debugId": null}}, {"offset": {"line": 2017, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/AIAutoEditsSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/AIAutoEditsSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst AIAutoEditsSection = () => {\n    const sectionRef = useRef(null);\n    const phoneRef = useRef(null);\n    const contentRef = useRef(null);\n    const [currentSuggestion, setCurrentSuggestion] = useState(0);\n\n    const suggestions = [\n        \"✨ Enhance clarity and flow\",\n        \"🎯 Improve tone and style\", \n        \"📝 Fix grammar and spelling\",\n        \"🔄 Restructure for impact\"\n    ];\n\n    const originalText = \"Let's brainstorm a few ideas for our next campaign. We need something that will resonate with our target audience and drive engagement.\";\n    const improvedText = \"Let's collaborate on innovative campaign concepts that will deeply resonate with our target demographic and significantly boost engagement metrics.\";\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const phone = phoneRef.current;\n        const content = contentRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([phone, content], { opacity: 0, y: 50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(phone, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animation for phone\n        gsap.to(phone, {\n            y: -8,\n            duration: 3,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1\n        });\n\n        // Cycle through suggestions\n        const suggestionInterval = setInterval(() => {\n            setCurrentSuggestion(prev => (prev + 1) % suggestions.length);\n        }, 3000);\n\n        return () => {\n            clearInterval(suggestionInterval);\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"ai-auto-edits-section\" ref={sectionRef}>\n            <div className=\"ai-auto-edits-container\">\n                <div className=\"phone-mockup-container\" ref={phoneRef}>\n                    <div className=\"phone-device\">\n                        <div className=\"phone-screen\">\n                            <div className=\"screen-header\">\n                                <div className=\"status-bar\">\n                                    <span className=\"time\">9:41</span>\n                                    <div className=\"status-icons\">\n                                        <span className=\"signal\">📶</span>\n                                        <span className=\"wifi\">📶</span>\n                                        <span className=\"battery\">🔋</span>\n                                    </div>\n                                </div>\n                                <div className=\"app-header\">\n                                    <h3>Flow - AI Writing Assistant</h3>\n                                </div>\n                            </div>\n                            \n                            <div className=\"screen-content\">\n                                <div className=\"text-editor\">\n                                    <div className=\"original-text\">\n                                        <p>{originalText}</p>\n                                    </div>\n                                    \n                                    <div className=\"ai-suggestions\">\n                                        <div className=\"suggestion-header\">\n                                            <span className=\"ai-icon\">🤖</span>\n                                            <span>AI Suggestions</span>\n                                        </div>\n                                        \n                                        <div className=\"suggestion-items\">\n                                            {suggestions.map((suggestion, index) => (\n                                                <div \n                                                    key={index}\n                                                    className={`suggestion-item ${index === currentSuggestion ? 'active' : ''}`}\n                                                >\n                                                    {suggestion}\n                                                </div>\n                                            ))}\n                                        </div>\n                                        \n                                        <div className=\"action-buttons\">\n                                            <button className=\"apply-btn\">Apply All</button>\n                                            <button className=\"preview-btn\">Preview</button>\n                                        </div>\n                                    </div>\n                                    \n                                    <div className=\"improved-text\">\n                                        <div className=\"improved-header\">\n                                            <span className=\"check-icon\">✅</span>\n                                            <span>Improved Version</span>\n                                        </div>\n                                        <p className=\"typing-text\">{improvedText}</p>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"content-section\" ref={contentRef}>\n                    <div className=\"content-badge\">\n                        <span className=\"sparkle\">✨</span>\n                        AI-Powered\n                    </div>\n                    \n                    <h2 className=\"section-title\">AI Auto Edits</h2>\n                    \n                    <p className=\"section-description\">\n                        Capture naturally and have interactive voice assistants \n                        that work seamlessly. Enhance thoughts flowing into \n                        structured, polished text—quickly, reliably, naturally.\n                    </p>\n                    \n                    <div className=\"features-list\">\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">🎯</span>\n                            <span>Smart tone adjustment</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">📝</span>\n                            <span>Grammar & style fixes</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">🔄</span>\n                            <span>Content restructuring</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">✨</span>\n                            <span>Clarity enhancement</span>\n                        </div>\n                    </div>\n                    \n                    <button className=\"cta-button\">\n                        Try AI Auto Edits\n                        <span className=\"arrow\">→</span>\n                    </button>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default AIAutoEditsSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,qBAAqB;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,cAAc;QAChB;QACA;QACA;QACA;KACH;IAED,MAAM,eAAe;IACrB,MAAM,eAAe;IAErB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,UAAU,WAAW,OAAO;QAElC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAO;SAAQ,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAE/C,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,OAAO;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,SAAS;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,+BAA+B;QAC/B,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;YACX,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;QACb;QAEA,4BAA4B;QAC5B,MAAM,qBAAqB,YAAY;YACnC,qBAAqB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,MAAM;QAChE,GAAG;QAEH,OAAO;YACH,cAAc;YACd,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAwB,KAAK;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;oBAAyB,KAAK;8BACzC,cAAA,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAK,WAAU;sEAAS;;;;;;sEACzB,8OAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;0DAAG;;;;;;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DACX,cAAA,8OAAC;8DAAG;;;;;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;0EAAK;;;;;;;;;;;;kEAGV,8OAAC;wDAAI,WAAU;kEACV,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC1B,8OAAC;gEAEG,WAAW,CAAC,gBAAgB,EAAE,UAAU,oBAAoB,WAAW,IAAI;0EAE1E;+DAHI;;;;;;;;;;kEAQjB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAO,WAAU;0EAAY;;;;;;0EAC9B,8OAAC;gEAAO,WAAU;0EAAc;;;;;;;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAK,WAAU;0EAAa;;;;;;0EAC7B,8OAAC;0EAAK;;;;;;;;;;;;kEAEV,8OAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpD,8OAAC;oBAAI,WAAU;oBAAkB,KAAK;;sCAClC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAU;;;;;;gCAAQ;;;;;;;sCAItC,8OAAC;4BAAG,WAAU;sCAAgB;;;;;;sCAE9B,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;sCAMnC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAO,WAAU;;gCAAa;8CAE3B,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD;uCAEe", "debugId": null}}, {"offset": {"line": 2529, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/MultiDeviceSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/MultiDeviceSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst MultiDeviceSection = () => {\n    const sectionRef = useRef(null);\n    const devicesRef = useRef([]);\n    const contentRef = useRef([]);\n\n    const languages = [\"EN\", \"ES\", \"FR\", \"DE\", \"ZH\", \"JA\", \"KO\", \"AR\", \"HI\", \"PT\", \"RU\", \"IT\"];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const devices = devicesRef.current;\n        const contents = contentRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set(devices, { opacity: 0, y: 100, rotation: 5 });\n        gsap.set(contents, { opacity: 0, x: -50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        // Animate devices in sequence\n        tl.to(devices[0], { // Dictionary device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        })\n        .to(contents[0], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[1], { // Tones device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[1], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[2], { // Languages device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[2], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[3], { // Desktop device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[3], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animations for devices\n        devices.forEach((device, index) => {\n            if (device) {\n                gsap.to(device, {\n                    y: -10,\n                    duration: 2 + index * 0.5,\n                    ease: \"power2.inOut\",\n                    yoyo: true,\n                    repeat: -1,\n                    delay: index * 0.3\n                });\n            }\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"multi-device-section\" ref={sectionRef}>\n            <div className=\"multi-device-container\">\n                \n                {/* Dictionary Section */}\n                <div className=\"device-row\">\n                    <div className=\"device-mockup dictionary-device\" ref={el => devicesRef.current[0] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"screen-header\">\n                                <h3>Your Dictionary</h3>\n                                <span className=\"close-btn\">×</span>\n                            </div>\n                            <div className=\"dictionary-content\">\n                                <div className=\"dict-item\">Technical terms</div>\n                                <div className=\"dict-item\">Brand names</div>\n                                <div className=\"dict-item\">Custom phrases</div>\n                                <div className=\"dict-item\">Abbreviations</div>\n                                <div className=\"dict-item\">Industry jargon</div>\n                                <div className=\"dict-item\">Personal vocabulary</div>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"device-content\" ref={el => contentRef.current[0] = el}>\n                        <h3>Personal Dictionary</h3>\n                        <p>Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage across all your content.</p>\n                    </div>\n                </div>\n\n                {/* Tones Section */}\n                <div className=\"device-row reverse\">\n                    <div className=\"device-content\" ref={el => contentRef.current[1] = el}>\n                        <h3>Different tones for each app</h3>\n                        <p>Adapt your communication style with different tones optimized for each application and context, ensuring perfect messaging every time.</p>\n                    </div>\n                    <div className=\"device-mockup tones-device\" ref={el => devicesRef.current[1] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"app-selector\">\n                                <div className=\"app-icon email\">📧</div>\n                                <div className=\"app-icon chat active\">💬</div>\n                                <div className=\"app-icon social\">📱</div>\n                            </div>\n                            <div className=\"tones-content\">\n                                <div className=\"tone-option\">Professional</div>\n                                <div className=\"tone-option active\">Casual</div>\n                                <div className=\"tone-option\">Friendly</div>\n                                <div className=\"tone-option\">Formal</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Languages Section */}\n                <div className=\"device-row\">\n                    <div className=\"device-mockup languages-device\" ref={el => devicesRef.current[2] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"language-wheel\">\n                                {languages.map((lang, index) => (\n                                    <div \n                                        key={lang}\n                                        className=\"language-item\"\n                                        style={{\n                                            transform: `rotate(${index * 30}deg) translateY(-80px) rotate(-${index * 30}deg)`\n                                        }}\n                                    >\n                                        {lang}\n                                    </div>\n                                ))}\n                                <div className=\"wheel-center\">\n                                    <span className=\"lang-count\">100+</span>\n                                    <span className=\"lang-text\">Languages</span>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"device-content\" ref={el => contentRef.current[2] = el}>\n                        <h3>100+ languages</h3>\n                        <p>Communicate globally with support for over 100 languages and seamless translation capabilities, breaking down language barriers effortlessly.</p>\n                    </div>\n                </div>\n\n                {/* Desktop Section */}\n                <div className=\"device-row reverse\">\n                    <div className=\"device-content\" ref={el => contentRef.current[3] = el}>\n                        <h3>On-the-go or at your desk</h3>\n                        <p>Work from anywhere with seamless synchronization across all your devices. Whether mobile or desktop, your workflow stays uninterrupted and efficient.</p>\n                        <button className=\"cta-button\">Get started</button>\n                    </div>\n                    <div className=\"device-mockup desktop-device\" ref={el => devicesRef.current[3] = el}>\n                        <div className=\"desktop-screen\">\n                            <div className=\"desktop-header\">\n                                <div className=\"window-controls\">\n                                    <span className=\"control red\"></span>\n                                    <span className=\"control yellow\"></span>\n                                    <span className=\"control green\"></span>\n                                </div>\n                                <div className=\"window-title\">Flow Desktop</div>\n                            </div>\n                            <div className=\"desktop-content\">\n                                <div className=\"sidebar\">\n                                    <div className=\"sidebar-item active\">📝 Documents</div>\n                                    <div className=\"sidebar-item\">🎤 Voice Notes</div>\n                                    <div className=\"sidebar-item\">📊 Analytics</div>\n                                    <div className=\"sidebar-item\">⚙️ Settings</div>\n                                </div>\n                                <div className=\"main-content\">\n                                    <div className=\"document-header\">\n                                        <h4>📋 Crazy product ideas</h4>\n                                        <span className=\"doc-status\">Synced</span>\n                                    </div>\n                                    <div className=\"document-body\">\n                                        <div className=\"text-line\"></div>\n                                        <div className=\"text-line short\"></div>\n                                        <div className=\"text-line\"></div>\n                                        <div className=\"text-line medium\"></div>\n                                    </div>\n                                </div>\n                            </div>\n                            <div className=\"mobile-preview\">\n                                <div className=\"mobile-screen\">\n                                    <div className=\"mobile-header\">Flow Mobile</div>\n                                    <div className=\"voice-indicator\">\n                                        <div className=\"voice-wave\"></div>\n                                        <div className=\"voice-wave\"></div>\n                                        <div className=\"voice-wave\"></div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n            </div>\n        </section>\n    );\n};\n\nexport default MultiDeviceSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,qBAAqB;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC5B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE5B,MAAM,YAAY;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE1F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,WAAW,WAAW,OAAO;QAEnC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS;YAAE,SAAS;YAAG,GAAG;YAAK,UAAU;QAAE;QACpD,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAExC,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,8BAA8B;QAC9B,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACd,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,UAAU;YACV,MAAM;QACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;YACb,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,kCAAkC;QAClC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACrB,IAAI,QAAQ;gBACR,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,QAAQ;oBACZ,GAAG,CAAC;oBACJ,UAAU,IAAI,QAAQ;oBACtB,MAAM;oBACN,MAAM;oBACN,QAAQ,CAAC;oBACT,OAAO,QAAQ;gBACnB;YACJ;QACJ;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAuB,KAAK;kBAC3C,cAAA,8OAAC;YAAI,WAAU;;8BAGX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAkC,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAChF,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAKX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;sCAEP,8OAAC;4BAAI,WAAU;4BAA6B,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC3E,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAiB;;;;;;0DAChC,8OAAC;gDAAI,WAAU;0DAAuB;;;;;;0DACtC,8OAAC;gDAAI,WAAU;0DAAkB;;;;;;;;;;;;kDAErC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAiC,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC/E,cAAA,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAI,WAAU;;wCACV,UAAU,GAAG,CAAC,CAAC,MAAM,sBAClB,8OAAC;gDAEG,WAAU;gDACV,OAAO;oDACH,WAAW,CAAC,OAAO,EAAE,QAAQ,GAAG,+BAA+B,EAAE,QAAQ,GAAG,IAAI,CAAC;gDACrF;0DAEC;+CANI;;;;;sDASb,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAK,WAAU;8DAAa;;;;;;8DAC7B,8OAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAKX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAO,WAAU;8CAAa;;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;4BAA+B,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC7E,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;;;;;;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DAAe;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,8OAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,8OAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,8OAAC;wDAAI,WAAU;kEAAe;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;gEAAK,WAAU;0EAAa;;;;;;;;;;;;kEAEjC,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAI3B,8OAAC;wCAAI,WAAU;kDACX,cAAA,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD;uCAEe", "debugId": null}}, {"offset": {"line": 3302, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/CrossPlatformSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/CrossPlatformSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst CrossPlatformSection = () => {\n    const sectionRef = useRef(null);\n    const contentRef = useRef(null);\n    const devicesRef = useRef(null);\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const content = contentRef.current;\n        const devices = devicesRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([content, devices], { opacity: 0, y: 50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(devices, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animation for devices\n        gsap.to(devices, {\n            y: -8,\n            duration: 3,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"cross-platform-section\" ref={sectionRef}>\n            <div className=\"cross-platform-container\">\n                <div className=\"content-section\" ref={contentRef}>\n                    <div className=\"platform-badges\">\n                        <span className=\"platform-badge\">📱 iOS</span>\n                        <span className=\"platform-badge\">🖥️ Mac</span>\n                        <span className=\"platform-badge\">🪟 Windows</span>\n                    </div>\n                    \n                    <h2 className=\"section-title text-black\">\n                        On-the-go or at<br />\n                        your desk\n                    </h2>\n                    \n                    <p className=\"section-description\">\n                        With apps for Desktop and iPhone, flow<br />\n                        freely from wherever you are. Your personal<br />\n                        dictionary and notes sync seamlessly<br />\n                        between all devices.\n                    </p>\n                    \n                    <button className=\"get-started-btn\">\n                        Get started\n                    </button>\n                </div>\n\n                <div className=\"devices-section\" ref={devicesRef}>\n                    <div className=\"device-stack\">\n                        {/* Desktop App */}\n                        <div className=\"desktop-mockup\">\n                            <div className=\"desktop-header\">\n                                <div className=\"window-controls\">\n                                    <span className=\"control red\"></span>\n                                    <span className=\"control yellow\"></span>\n                                    <span className=\"control green\"></span>\n                                </div>\n                            </div>\n                            <div className=\"desktop-content\">\n                                <div className=\"sidebar\">\n                                    <div className=\"sidebar-item\">📝 Documents</div>\n                                    <div className=\"sidebar-item active\">💡 Crazy product ideas</div>\n                                    <div className=\"sidebar-item\">🎯 Goals</div>\n                                    <div className=\"sidebar-item\">📊 Notes</div>\n                                </div>\n                                <div className=\"main-area\">\n                                    <div className=\"document-title\">💡 Crazy product ideas</div>\n                                    <div className=\"document-section\">\n                                        <h4>Physical Products</h4>\n                                        <div className=\"idea-item\">\n                                            <span className=\"bullet\">•</span>\n                                            <span>Self-Adjusting Plant Stand</span>\n                                        </div>\n                                        <div className=\"idea-item\">\n                                            <span className=\"bullet\">•</span>\n                                            <span>Speakers with built-in planters and a tiny water reservoir</span>\n                                        </div>\n                                        <div className=\"idea-item\">\n                                            <span className=\"bullet\">•</span>\n                                            <span>Modular furniture system</span>\n                                        </div>\n                                    </div>\n                                    <div className=\"voice-indicator-desktop\">\n                                        <div className=\"voice-waves\">\n                                            <div className=\"wave\"></div>\n                                            <div className=\"wave\"></div>\n                                            <div className=\"wave\"></div>\n                                            <div className=\"wave\"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Mobile App */}\n                        <div className=\"mobile-mockup\">\n                            <div className=\"mobile-header\">\n                                <div className=\"status-bar\">\n                                    <span className=\"time\">9:41</span>\n                                    <div className=\"status-icons\">\n                                        <span>📶</span>\n                                        <span>🔋</span>\n                                    </div>\n                                </div>\n                                <div className=\"app-title\">Flow</div>\n                            </div>\n                            <div className=\"mobile-content\">\n                                <div className=\"mobile-item\">\n                                    <span className=\"item-icon\">🎤</span>\n                                    <span className=\"item-text\">Voice Note</span>\n                                </div>\n                                <div className=\"recording-area\">\n                                    <div className=\"recording-circle\">\n                                        <div className=\"record-button\"></div>\n                                    </div>\n                                    <div className=\"recording-waves\">\n                                        <div className=\"mobile-wave\"></div>\n                                        <div className=\"mobile-wave\"></div>\n                                        <div className=\"mobile-wave\"></div>\n                                        <div className=\"mobile-wave\"></div>\n                                        <div className=\"mobile-wave\"></div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Voice Recorder Widget */}\n                        <div className=\"voice-widget\">\n                            <div className=\"widget-header\">\n                                <span className=\"widget-icon\">🎤</span>\n                            </div>\n                            <div className=\"widget-waves\">\n                                <div className=\"widget-wave\"></div>\n                                <div className=\"widget-wave\"></div>\n                                <div className=\"widget-wave\"></div>\n                                <div className=\"widget-wave\"></div>\n                                <div className=\"widget-wave\"></div>\n                                <div className=\"widget-wave\"></div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default CrossPlatformSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,uBAAuB;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,UAAU,WAAW,OAAO;QAElC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAS;SAAQ,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAEjD,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,SAAS;YACX,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,SAAS;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,iCAAiC;QACjC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;YACb,GAAG,CAAC;YACJ,UAAU;YACV,MAAM;YACN,MAAM;YACN,QAAQ,CAAC;QACb;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAyB,KAAK;kBAC7C,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;oBAAkB,KAAK;;sCAClC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;8CACjC,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;8CACjC,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;sCAGrC,8OAAC;4BAAG,WAAU;;gCAA2B;8CACtB,8OAAC;;;;;gCAAK;;;;;;;sCAIzB,8OAAC;4BAAE,WAAU;;gCAAsB;8CACO,8OAAC;;;;;gCAAK;8CACD,8OAAC;;;;;gCAAK;8CACb,8OAAC;;;;;gCAAK;;;;;;;sCAI9C,8OAAC;4BAAO,WAAU;sCAAkB;;;;;;;;;;;;8BAKxC,8OAAC;oBAAI,WAAU;oBAAkB,KAAK;8BAClC,cAAA,8OAAC;wBAAI,WAAU;;0CAEX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;kDACX,cAAA,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,8OAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,8OAAC;wDAAI,WAAU;kEAAe;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;kEAAiB;;;;;;kEAChC,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;gEAAI,WAAU;;kFACX,8OAAC;wEAAK,WAAU;kFAAS;;;;;;kFACzB,8OAAC;kFAAK;;;;;;;;;;;;0EAEV,8OAAC;gEAAI,WAAU;;kFACX,8OAAC;wEAAK,WAAU;kFAAS;;;;;;kFACzB,8OAAC;kFAAK;;;;;;;;;;;;0EAEV,8OAAC;gEAAI,WAAU;;kFACX,8OAAC;wEAAK,WAAU;kFAAS;;;;;;kFACzB,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;kEACX,cAAA,8OAAC;4DAAI,WAAU;;8EACX,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQnC,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAO;;;;;;kEACvB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAY;;;;;;kEAC5B,8OAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;;0DAEhC,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;kEACX,cAAA,8OAAC;4DAAI,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/B,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;kDACX,cAAA,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;uCAEe", "debugId": null}}, {"offset": {"line": 3991, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/TestimonialsSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/TestimonialsSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst TestimonialsSection = () => {\n    const sectionRef = useRef(null);\n    const titleRef = useRef(null);\n    const cardsRef = useRef(null);\n\n    const testimonials = [\n        {\n            id: 1,\n            avatar: \"👨‍💼\",\n            name: \"<PERSON>\",\n            role: \"Product Manager\",\n            text: \"I have Parkinson's and this app has just made my life so much easier using my Mac. I can't type anymore but I can still dictate and <PERSON> has provided for me.\",\n            rating: 5\n        },\n        {\n            id: 2,\n            avatar: \"👩‍💻\",\n            name: \"<PERSON>\",\n            role: \"Software Developer\",\n            text: \"Hey! <PERSON> is currently blowing my mind with how fast and intuitive it is.\",\n            rating: 5\n        },\n        {\n            id: 3,\n            avatar: \"👨‍🎨\",\n            name: \"<PERSON>\",\n            role: \"Creative Director\",\n            text: \"I was able to dictate ~70% of our latest project with <PERSON><PERSON>. It was massive time saver!\",\n            rating: 5\n        },\n        {\n            id: 4,\n            avatar: \"👩‍🏫\",\n            name: \"<PERSON> <PERSON>\",\n            role: \"Teacher\",\n            text: \"Flow is that single little part of your brain that helps you remember all the sentences and have a pretty response.\",\n            rating: 5\n        },\n        {\n            id: 5,\n            avatar: \"👨‍⚕️\",\n            name: \"Dr. Robert Smith\",\n            role: \"Medical Professional\",\n            text: \"Stuttering a bit left works really well there's really a back, Flow works really well.\",\n            rating: 5\n        },\n        {\n            id: 6,\n            avatar: \"👩‍💼\",\n            name: \"Lisa Anderson\",\n            role: \"Business Analyst\",\n            text: \"I'm afraid I'm getting addicted to this great platform. It allows me to get into a flow state and not worry about the typing or dictation stop and interrupt me.\",\n            rating: 5\n        },\n        {\n            id: 7,\n            avatar: \"👨‍🔬\",\n            name: \"David Wilson\",\n            role: \"Researcher\",\n            text: \"You're making texting actually fun again and I feel like I'm becoming a can't live without it tool. I'm now faster than ever because Flow is not on there.\",\n            rating: 5\n        }\n    ];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const title = titleRef.current;\n        const cards = cardsRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([title, cards], { opacity: 0, y: 50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(title, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(cards, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Stagger animation for individual cards\n        const testimonialCards = cards?.querySelectorAll('.testimonial-card');\n        if (testimonialCards) {\n            gsap.fromTo(testimonialCards, \n                { opacity: 0, y: 30 },\n                {\n                    opacity: 1,\n                    y: 0,\n                    duration: 0.6,\n                    stagger: 0.1,\n                    ease: \"power3.out\",\n                    scrollTrigger: {\n                        trigger: cards,\n                        start: \"top 80%\",\n                        toggleActions: \"play none none reverse\"\n                    }\n                }\n            );\n        }\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    const renderStars = (rating) => {\n        return Array.from({ length: 5 }, (_, index) => (\n            <span key={index} className={`star ${index < rating ? 'filled' : ''}`}>\n                ⭐\n            </span>\n        ));\n    };\n\n    return (\n        <section className=\"testimonials-section\" ref={sectionRef}>\n            <div className=\"testimonials-container\">\n                <div className=\"section-header\" ref={titleRef}>\n                    <h2 className=\"section-title\">\n                        Love letters<br />\n                        to Flow\n                    </h2>\n                    <div className=\"decorative-lines\">\n                        <div className=\"line line-1\"></div>\n                        <div className=\"line line-2\"></div>\n                        <div className=\"line line-3\"></div>\n                        <div className=\"line line-4\"></div>\n                        <div className=\"line line-5\"></div>\n                        <div className=\"line line-6\"></div>\n                    </div>\n                </div>\n\n                <div className=\"testimonials-grid\" ref={cardsRef}>\n                    {testimonials.map((testimonial, index) => (\n                        <div \n                            key={testimonial.id} \n                            className={`testimonial-card card-${index + 1}`}\n                        >\n                            <div className=\"card-header\">\n                                <div className=\"avatar\">\n                                    {testimonial.avatar}\n                                </div>\n                                <div className=\"user-info\">\n                                    <h4 className=\"user-name\">{testimonial.name}</h4>\n                                    <p className=\"user-role\">{testimonial.role}</p>\n                                </div>\n                            </div>\n                            \n                            <div className=\"testimonial-content\">\n                                <p className=\"testimonial-text\">\n                                    \"{testimonial.text}\"\n                                </p>\n                            </div>\n                            \n                            <div className=\"rating\">\n                                {renderStars(testimonial.rating)}\n                            </div>\n                        </div>\n                    ))}\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default TestimonialsSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,sBAAsB;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,MAAM,eAAe;QACjB;YACI,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;QACZ;KACH;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,QAAQ,SAAS,OAAO;QAE9B,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAO;SAAM,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAE7C,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,OAAO;YACT,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,OAAO;YACP,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,yCAAyC;QACzC,MAAM,mBAAmB,OAAO,iBAAiB;QACjD,IAAI,kBAAkB;YAClB,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,kBACR;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBACI,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACX,SAAS;oBACT,OAAO;oBACP,eAAe;gBACnB;YACJ;QAER;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACjB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACjC,8OAAC;gBAAiB,WAAW,CAAC,KAAK,EAAE,QAAQ,SAAS,WAAW,IAAI;0BAAE;eAA5D;;;;;IAInB;IAEA,qBACI,8OAAC;QAAQ,WAAU;QAAuB,KAAK;kBAC3C,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;oBAAiB,KAAK;;sCACjC,8OAAC;4BAAG,WAAU;;gCAAgB;8CACd,8OAAC;;;;;gCAAK;;;;;;;sCAGtB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAIvB,8OAAC;oBAAI,WAAU;oBAAoB,KAAK;8BACnC,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC5B,8OAAC;4BAEG,WAAW,CAAC,sBAAsB,EAAE,QAAQ,GAAG;;8CAE/C,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;sDACV,YAAY,MAAM;;;;;;sDAEvB,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAG,WAAU;8DAAa,YAAY,IAAI;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DAAa,YAAY,IAAI;;;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC;wCAAE,WAAU;;4CAAmB;4CAC1B,YAAY,IAAI;4CAAC;;;;;;;;;;;;8CAI3B,8OAAC;oCAAI,WAAU;8CACV,YAAY,YAAY,MAAM;;;;;;;2BApB9B,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;AA4B/C;uCAEe", "debugId": null}}, {"offset": {"line": 4325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Footer.jsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport \"@/Style/Footer.css\";\n\nconst Footer = () => {\n    return (\n        <footer className=\"footer-section\">\n            <div className=\"footer-container\">\n                <div className=\"footer-content\">\n                    <div className=\"footer-main\">\n                        <div className=\"footer-brand\">\n                            <div className=\"brand-logo\">\n                                <h2 className=\"brand-name\">Flow</h2>\n                                <p className=\"brand-tagline\">Effortless voice dictation</p>\n                            </div>\n                            <p className=\"brand-description\">\n                                Transform your productivity with AI-powered voice dictation. \n                                4x faster than typing, available across all your devices.\n                            </p>\n                            <div className=\"social-links\">\n                                <a href=\"#\" className=\"social-link\" aria-label=\"Twitter\">\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                                        <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                                    </svg>\n                                </a>\n                                <a href=\"#\" className=\"social-link\" aria-label=\"LinkedIn\">\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                                        <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                                    </svg>\n                                </a>\n                                <a href=\"#\" className=\"social-link\" aria-label=\"GitHub\">\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                                        <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                                    </svg>\n                                </a>\n                            </div>\n                        </div>\n\n                        <div className=\"footer-links\">\n                            <div className=\"link-group\">\n                                <h3 className=\"link-title\">Product</h3>\n                                <ul className=\"link-list\">\n                                    <li><a href=\"#\" className=\"footer-link\">Features</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Pricing</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Download</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Integrations</a></li>\n                                </ul>\n                            </div>\n\n                            <div className=\"link-group\">\n                                <h3 className=\"link-title\">Company</h3>\n                                <ul className=\"link-list\">\n                                    <li><a href=\"#\" className=\"footer-link\">About</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Blog</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Careers</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Contact</a></li>\n                                </ul>\n                            </div>\n\n                            <div className=\"link-group\">\n                                <h3 className=\"link-title\">Support</h3>\n                                <ul className=\"link-list\">\n                                    <li><a href=\"#\" className=\"footer-link\">Help Center</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Documentation</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Community</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Status</a></li>\n                                </ul>\n                            </div>\n\n                            <div className=\"link-group\">\n                                <h3 className=\"link-title\">Legal</h3>\n                                <ul className=\"link-list\">\n                                    <li><a href=\"#\" className=\"footer-link\">Privacy Policy</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Terms of Service</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">Cookie Policy</a></li>\n                                    <li><a href=\"#\" className=\"footer-link\">GDPR</a></li>\n                                </ul>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"footer-bottom\">\n                        <div className=\"footer-divider\"></div>\n                        <div className=\"footer-bottom-content\">\n                            <p className=\"copyright\">\n                                © 2024 Flow. All rights reserved.\n                            </p>\n                            <div className=\"footer-bottom-links\">\n                                <a href=\"#\" className=\"footer-bottom-link\">Privacy</a>\n                                <a href=\"#\" className=\"footer-bottom-link\">Terms</a>\n                                <a href=\"#\" className=\"footer-bottom-link\">Cookies</a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </footer>\n    );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;;AAIA,MAAM,SAAS;IACX,qBACI,8OAAC;QAAO,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;0DAAa;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAE,WAAU;kDAAoB;;;;;;kDAIjC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAE,MAAK;gDAAI,WAAU;gDAAc,cAAW;0DAC3C,cAAA,8OAAC;oDAAI,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;8DACjD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGhB,8OAAC;gDAAE,MAAK;gDAAI,WAAU;gDAAc,cAAW;0DAC3C,cAAA,8OAAC;oDAAI,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;8DACjD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGhB,8OAAC;gDAAE,MAAK;gDAAI,WAAU;gDAAc,cAAW;0DAC3C,cAAA,8OAAC;oDAAI,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;8DACjD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMxB,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;0DAAa;;;;;;0DAC3B,8OAAC;gDAAG,WAAU;;kEACV,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;kDAIhD,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;0DAAa;;;;;;0DAC3B,8OAAC;gDAAG,WAAU;;kEACV,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;kDAIhD,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;0DAAa;;;;;;0DAC3B,8OAAC;gDAAG,WAAU;;kEACV,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;kDAIhD,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;0DAAa;;;;;;0DAC3B,8OAAC;gDAAG,WAAU;;kEACV,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;kEACxC,8OAAC;kEAAG,cAAA,8OAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAE,WAAU;kDAAY;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqB;;;;;;0DAC3C,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqB;;;;;;0DAC3C,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E;uCAEe", "debugId": null}}]}