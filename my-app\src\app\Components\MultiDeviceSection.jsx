"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/MultiDeviceSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const MultiDeviceSection = () => {
    const sectionRef = useRef(null);
    const devicesRef = useRef([]);
    const contentRef = useRef([]);

    const languages = ["EN", "ES", "FR", "DE", "ZH", "JA", "KO", "AR", "HI", "PT", "RU", "IT"];

    useEffect(() => {
        const section = sectionRef.current;
        const devices = devicesRef.current;
        const contents = contentRef.current;

        if (!section) return;

        // Initial setup
        gsap.set(devices, { opacity: 0, y: 100, rotation: 5 });
        gsap.set(contents, { opacity: 0, x: -50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        // Animate devices in sequence
        tl.to(devices[0], { // Dictionary device
            opacity: 1,
            y: 0,
            rotation: 0,
            duration: 0.8,
            ease: "back.out(1.7)"
        })
        .to(contents[0], {
            opacity: 1,
            x: 0,
            duration: 0.6,
            ease: "power3.out"
        }, "-=0.4")
        .to(devices[1], { // Tones device
            opacity: 1,
            y: 0,
            rotation: 0,
            duration: 0.8,
            ease: "back.out(1.7)"
        }, "-=0.2")
        .to(contents[1], {
            opacity: 1,
            x: 0,
            duration: 0.6,
            ease: "power3.out"
        }, "-=0.4")
        .to(devices[2], { // Languages device
            opacity: 1,
            y: 0,
            rotation: 0,
            duration: 0.8,
            ease: "back.out(1.7)"
        }, "-=0.2")
        .to(contents[2], {
            opacity: 1,
            x: 0,
            duration: 0.6,
            ease: "power3.out"
        }, "-=0.4")
        .to(devices[3], { // Desktop device
            opacity: 1,
            y: 0,
            rotation: 0,
            duration: 0.8,
            ease: "back.out(1.7)"
        }, "-=0.2")
        .to(contents[3], {
            opacity: 1,
            x: 0,
            duration: 0.6,
            ease: "power3.out"
        }, "-=0.4");

        // Floating animations for devices
        devices.forEach((device, index) => {
            if (device) {
                gsap.to(device, {
                    y: -10,
                    duration: 2 + index * 0.5,
                    ease: "power2.inOut",
                    yoyo: true,
                    repeat: -1,
                    delay: index * 0.3
                });
            }
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="multi-device-section" ref={sectionRef}>
            <div className="multi-device-container">
                
                {/* Dictionary Section */}
                <div className="device-row">
                    <div className="device-mockup dictionary-device" ref={el => devicesRef.current[0] = el}>
                        <div className="device-screen">
                            <div className="screen-header">
                                <h3>Your Dictionary</h3>
                                <span className="close-btn">×</span>
                            </div>
                            <div className="dictionary-content">
                                <div className="dict-item">Technical terms</div>
                                <div className="dict-item">Brand names</div>
                                <div className="dict-item">Custom phrases</div>
                                <div className="dict-item">Abbreviations</div>
                                <div className="dict-item">Industry jargon</div>
                                <div className="dict-item">Personal vocabulary</div>
                            </div>
                        </div>
                    </div>
                    <div className="device-content" ref={el => contentRef.current[0] = el}>
                        <h3>Personal Dictionary</h3>
                        <p>Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage across all your content.</p>
                    </div>
                </div>

                {/* Tones Section */}
                <div className="device-row reverse">
                    <div className="device-content" ref={el => contentRef.current[1] = el}>
                        <h3>Different tones for each app</h3>
                        <p>Adapt your communication style with different tones optimized for each application and context, ensuring perfect messaging every time.</p>
                    </div>
                    <div className="device-mockup tones-device" ref={el => devicesRef.current[1] = el}>
                        <div className="device-screen">
                            <div className="app-selector">
                                <div className="app-icon email">📧</div>
                                <div className="app-icon chat active">💬</div>
                                <div className="app-icon social">📱</div>
                            </div>
                            <div className="tones-content">
                                <div className="tone-option">Professional</div>
                                <div className="tone-option active">Casual</div>
                                <div className="tone-option">Friendly</div>
                                <div className="tone-option">Formal</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Languages Section */}
                <div className="device-row">
                    <div className="device-mockup languages-device" ref={el => devicesRef.current[2] = el}>
                        <div className="device-screen">
                            <div className="language-wheel">
                                {languages.map((lang, index) => (
                                    <div 
                                        key={lang}
                                        className="language-item"
                                        style={{
                                            transform: `rotate(${index * 30}deg) translateY(-80px) rotate(-${index * 30}deg)`
                                        }}
                                    >
                                        {lang}
                                    </div>
                                ))}
                                <div className="wheel-center">
                                    <span className="lang-count">100+</span>
                                    <span className="lang-text">Languages</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="device-content" ref={el => contentRef.current[2] = el}>
                        <h3>100+ languages</h3>
                        <p>Communicate globally with support for over 100 languages and seamless translation capabilities, breaking down language barriers effortlessly.</p>
                    </div>
                </div>

                {/* Desktop Section */}
                <div className="device-row reverse">
                    <div className="device-content" ref={el => contentRef.current[3] = el}>
                        <h3>On-the-go or at your desk</h3>
                        <p>Work from anywhere with seamless synchronization across all your devices. Whether mobile or desktop, your workflow stays uninterrupted and efficient.</p>
                        <button className="cta-button">Get started</button>
                    </div>
                    <div className="device-mockup desktop-device" ref={el => devicesRef.current[3] = el}>
                        <div className="desktop-screen">
                            <div className="desktop-header">
                                <div className="window-controls">
                                    <span className="control red"></span>
                                    <span className="control yellow"></span>
                                    <span className="control green"></span>
                                </div>
                                <div className="window-title">Flow Desktop</div>
                            </div>
                            <div className="desktop-content">
                                <div className="sidebar">
                                    <div className="sidebar-item active">📝 Documents</div>
                                    <div className="sidebar-item">🎤 Voice Notes</div>
                                    <div className="sidebar-item">📊 Analytics</div>
                                    <div className="sidebar-item">⚙️ Settings</div>
                                </div>
                                <div className="main-content">
                                    <div className="document-header">
                                        <h4>📋 Crazy product ideas</h4>
                                        <span className="doc-status">Synced</span>
                                    </div>
                                    <div className="document-body">
                                        <div className="text-line"></div>
                                        <div className="text-line short"></div>
                                        <div className="text-line"></div>
                                        <div className="text-line medium"></div>
                                    </div>
                                </div>
                            </div>
                            <div className="mobile-preview">
                                <div className="mobile-screen">
                                    <div className="mobile-header">Flow Mobile</div>
                                    <div className="voice-indicator">
                                        <div className="voice-wave"></div>
                                        <div className="voice-wave"></div>
                                        <div className="voice-wave"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </section>
    );
};

export default MultiDeviceSection;
