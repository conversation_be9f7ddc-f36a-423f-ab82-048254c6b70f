/* Cross Platform Section */
.cross-platform-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  padding: 120px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.cross-platform-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  padding: 0 40px;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.platform-badges {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.platform-badge {
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  border: 1px solid rgba(245, 158, 11, 0.2);
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 3.5rem;
  font-weight: 400;
  color: #1f2937;
  line-height: 1.1;
  margin: 0;
  font-family: 'Georgia', serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.02em;
}

.section-description {
  font-size: 1.125rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 400;
}

.get-started-btn {
  background: transparent;
  color: #1f2937;
  border: 2px solid #d1d5db;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
}

.get-started-btn:hover {
  border-color: #f59e0b;
  color: #f59e0b;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
}

.devices-section {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.device-stack {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 600px;
}

/* Desktop Mockup */
.desktop-mockup {
  position: absolute;
  top: 0;
  left: 0;
  width: 400px;
  height: 280px;
  background: #1f2937;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  z-index: 3;
}

.desktop-header {
  background: #374151;
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.window-controls {
  display: flex;
  gap: 8px;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.desktop-content {
  display: flex;
  height: calc(100% - 32px);
}

.sidebar {
  width: 120px;
  background: #2d3748;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sidebar-item {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  color: #d1d5db;
  cursor: pointer;
  transition: background 0.2s;
}

.sidebar-item.active {
  background: #f59e0b;
  color: white;
}

.main-area {
  flex: 1;
  padding: 16px;
  background: #1f2937;
  position: relative;
}

.document-title {
  color: #f59e0b;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.document-section h4 {
  color: #e5e7eb;
  font-size: 0.75rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.idea-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 0.7rem;
  color: #d1d5db;
}

.bullet {
  color: #f59e0b;
}

.voice-indicator-desktop {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background: rgba(245, 158, 11, 0.2);
  border-radius: 20px;
  padding: 8px 12px;
}

.voice-waves {
  display: flex;
  gap: 2px;
  align-items: center;
}

.wave {
  width: 2px;
  height: 12px;
  background: #f59e0b;
  border-radius: 1px;
  animation: wave 1s ease-in-out infinite;
}

.wave:nth-child(1) { animation-delay: 0s; }
.wave:nth-child(2) { animation-delay: 0.2s; }
.wave:nth-child(3) { animation-delay: 0.4s; }
.wave:nth-child(4) { animation-delay: 0.6s; }

/* Mobile Mockup */
.mobile-mockup {
  position: absolute;
  top: 200px;
  right: 50px;
  width: 180px;
  height: 320px;
  background: #1f2937;
  border-radius: 20px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  z-index: 2;
}

.mobile-header {
  background: #374151;
  padding: 12px 16px 8px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.7rem;
  color: #e5e7eb;
  margin-bottom: 8px;
}

.app-title {
  color: #f59e0b;
  font-size: 0.875rem;
  font-weight: 600;
  text-align: center;
}

.mobile-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.mobile-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e5e7eb;
  font-size: 0.875rem;
}

.recording-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.recording-circle {
  width: 60px;
  height: 60px;
  border: 2px solid #f59e0b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-button {
  width: 20px;
  height: 20px;
  background: #ef4444;
  border-radius: 50%;
}

.recording-waves {
  display: flex;
  gap: 3px;
  align-items: center;
}

.mobile-wave {
  width: 3px;
  height: 20px;
  background: #3b82f6;
  border-radius: 2px;
  animation: wave 1.2s ease-in-out infinite;
}

.mobile-wave:nth-child(1) { animation-delay: 0s; }
.mobile-wave:nth-child(2) { animation-delay: 0.15s; }
.mobile-wave:nth-child(3) { animation-delay: 0.3s; }
.mobile-wave:nth-child(4) { animation-delay: 0.45s; }
.mobile-wave:nth-child(5) { animation-delay: 0.6s; }

/* Voice Widget */
.voice-widget {
  position: absolute;
  bottom: 50px;
  left: 80px;
  width: 120px;
  height: 80px;
  background: #1f2937;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  padding: 12px;
  z-index: 1;
}

.widget-header {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

.widget-icon {
  font-size: 1.2rem;
}

.widget-waves {
  display: flex;
  gap: 2px;
  align-items: center;
  justify-content: center;
}

.widget-wave {
  width: 2px;
  height: 16px;
  background: #10b981;
  border-radius: 1px;
  animation: wave 0.8s ease-in-out infinite;
}

.widget-wave:nth-child(1) { animation-delay: 0s; }
.widget-wave:nth-child(2) { animation-delay: 0.1s; }
.widget-wave:nth-child(3) { animation-delay: 0.2s; }
.widget-wave:nth-child(4) { animation-delay: 0.3s; }
.widget-wave:nth-child(5) { animation-delay: 0.4s; }
.widget-wave:nth-child(6) { animation-delay: 0.5s; }

@keyframes wave {
  0%, 100% { 
    transform: scaleY(0.3);
    opacity: 0.7;
  }
  50% { 
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .cross-platform-container {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .desktop-mockup {
    width: 350px;
    height: 240px;
  }

  .mobile-mockup {
    width: 160px;
    height: 280px;
  }
}

@media (max-width: 768px) {
  .cross-platform-section {
    padding: 80px 20px;
    min-height: auto;
  }

  .cross-platform-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .device-stack {
    max-width: 400px;
    height: 500px;
    margin: 0 auto;
  }

  .desktop-mockup {
    width: 300px;
    height: 200px;
    left: 50%;
    transform: translateX(-50%);
  }

  .mobile-mockup {
    width: 140px;
    height: 250px;
    top: 150px;
    right: 20px;
  }

  .voice-widget {
    bottom: 30px;
    left: 20px;
    width: 100px;
    height: 70px;
  }
}

@media (max-width: 480px) {
  .cross-platform-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .platform-badges {
    justify-content: center;
    flex-wrap: wrap;
  }

  .platform-badge {
    font-size: 0.75rem;
    padding: 6px 12px;
  }

  .device-stack {
    max-width: 320px;
    height: 400px;
  }

  .desktop-mockup {
    width: 280px;
    height: 180px;
  }

  .mobile-mockup {
    width: 120px;
    height: 220px;
    top: 120px;
    right: 10px;
  }

  .voice-widget {
    width: 90px;
    height: 60px;
    bottom: 20px;
    left: 10px;
  }

  .get-started-btn {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }
}
