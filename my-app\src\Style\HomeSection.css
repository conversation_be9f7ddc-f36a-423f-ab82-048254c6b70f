/* Home Section */
.home-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 120px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
}

.home-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 40px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  color: #92400e;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  width: fit-content;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.section-title {
  font-size: 3.5rem;
  font-weight: 400;
  color: black;
  line-height: 1.1;
  margin: 0;
  font-family: 'Georgia', serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gradient-text {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
}

.section-description {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
  max-width: 500px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
}

.feature-icon {
  font-size: 1.25rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.visual-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.demo-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.demo-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.demo-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.waveform {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 60px;
}

.wave-bar {
  width: 4px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 2px;
  animation: wave 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(1) { height: 20px; animation-delay: 0s; }
.wave-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }
.wave-bar:nth-child(3) { height: 50px; animation-delay: 0.2s; }
.wave-bar:nth-child(4) { height: 30px; animation-delay: 0.3s; }
.wave-bar:nth-child(5) { height: 45px; animation-delay: 0.4s; }
.wave-bar:nth-child(6) { height: 25px; animation-delay: 0.5s; }
.wave-bar:nth-child(7) { height: 40px; animation-delay: 0.6s; }
.wave-bar:nth-child(8) { height: 20px; animation-delay: 0.7s; }

@keyframes wave {
  0%, 100% { 
    transform: scaleY(0.3);
    opacity: 0.7;
  }
  50% { 
    transform: scaleY(1);
    opacity: 1;
  }
}

.demo-text {
  font-size: 0.95rem;
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  border-left: 4px solid #f59e0b;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .home-section {
    padding: 100px 20px;
  }
  
  .content-grid {
    gap: 60px;
  }
  
  .section-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .home-section {
    padding: 80px 20px;
    min-height: auto;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .section-description {
    font-size: 1rem;
    max-width: none;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .feature-list {
    align-items: center;
  }
  
  .feature-item {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .home-section {
    padding: 60px 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .section-description {
    font-size: 0.95rem;
  }
  
  .demo-card {
    padding: 20px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-number {
    font-size: 1.75rem;
  }
  
  .waveform {
    height: 50px;
  }
  
  .wave-bar {
    width: 3px;
  }
}
