/* [project]/src/Style/Marquee.css [app-client] (css) */
.marquee-container {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  align-items: center;
  width: 100%;
  min-height: 100px;
  padding: 20px 0;
  font-family: Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  position: relative;
  overflow: hidden;
}

.marquee-container:before {
  content: "";
  pointer-events: none;
  z-index: 2;
  background: linear-gradient(90deg, #00000026 0%, #0000 15% 85%, #00000026 100%);
  position: absolute;
  inset: 0;
}

.marquee-wrapper {
  white-space: nowrap;
  will-change: transform;
  align-items: center;
  display: flex;
}

.marquee-item {
  backdrop-filter: blur(10px);
  border: 1px solid #fff3;
  border-radius: 12px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  min-width: fit-content;
  margin-right: 20px;
  display: inline-flex;
  box-shadow: 0 4px 15px #0000001a;
}

.marquee-content {
  color: #fff;
  text-shadow: 0 2px 4px #0000004d;
  white-space: nowrap;
  letter-spacing: .5px;
  user-select: none;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
}

@media (width <= 1024px) {
  .marquee-content {
    padding: 11px 18px;
    font-size: 15px;
  }

  .marquee-item {
    margin-right: 18px;
  }
}

@media (width <= 768px) {
  .marquee-container {
    min-height: 80px;
    padding: 15px 0;
  }

  .marquee-content {
    padding: 10px 16px;
    font-size: 14px;
  }

  .marquee-item {
    margin-right: 15px;
  }
}

@media (width <= 480px) {
  .marquee-container {
    min-height: 70px;
    padding: 12px 0;
  }

  .marquee-content {
    letter-spacing: .3px;
    padding: 8px 14px;
    font-size: 13px;
  }

  .marquee-item {
    margin-right: 12px;
  }
}

@media (width <= 320px) {
  .marquee-container {
    min-height: 60px;
    padding: 10px 0;
  }

  .marquee-content {
    padding: 6px 12px;
    font-size: 12px;
  }

  .marquee-item {
    margin-right: 10px;
  }
}

.MarqueeBox {
  color: #fff;
  text-align: center;
  background: #111;
  justify-content: center;
  align-items: center;
  height: 20vh;
  font-family: system-ui;
  display: flex;
}

.carousel {
  background: #00f;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
}

.box {
  color: #000;
  cursor: pointer;
  background: green;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  font-size: 121px;
  display: flex;
  position: relative;
}

.test {
  padding: 20px;
}

.test-2 {
  padding: 20px 10px;
}


/* [project]/src/Style/FeaturesShowcase.css [app-client] (css) */
.features-showcase {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 80px 20px;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-header {
  text-align: center;
  margin-bottom: 60px;
}

.features-title {
  color: #2d3748;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-bottom: 16px;
  font-size: 3rem;
  font-weight: 700;
}

.features-subtitle {
  color: #718096;
  max-width: 600px;
  margin: 0 auto;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  align-items: start;
  gap: 30px;
  display: grid;
}

.feature-card {
  backdrop-filter: blur(10px);
  border: 1px solid #fff3;
  border-radius: 20px;
  padding: 30px;
  transition: all .3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px #0000001a;
}

.feature-large {
  grid-column: span 2;
  min-height: 400px;
}

.feature-content {
  flex-direction: column;
  gap: 20px;
  height: 100%;
  display: flex;
}

.feature-image {
  flex: 1;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  display: flex;
}

.phone-mockup {
  background: #000c;
  border: 3px solid #ffffff4d;
  border-radius: 25px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 300px;
  padding: 20px;
  display: flex;
  position: relative;
}

.ai-interface {
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  display: flex;
}

.ai-suggestions {
  flex-direction: column;
  gap: 10px;
  display: flex;
}

.suggestion-item {
  color: #fff;
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
}

.ai-controls {
  justify-content: center;
  display: flex;
}

.ai-btn {
  color: #fff;
  cursor: pointer;
  background: #4facfe;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-weight: 600;
  transition: all .3s;
}

.ai-btn:hover {
  background: #3d8bfe;
  transform: scale(1.05);
}

.dictionary-interface {
  flex-direction: column;
  gap: 15px;
  width: 100%;
  height: 100%;
  display: flex;
}

.dict-header {
  color: #4facfe;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
}

.dict-items {
  flex-direction: column;
  gap: 8px;
  display: flex;
}

.dict-item {
  color: #fff;
  text-align: center;
  background: #4facfe33;
  border: 1px solid #4facfe4d;
  border-radius: 15px;
  padding: 8px 12px;
  font-size: 11px;
}

.tones-interface {
  flex-direction: column;
  justify-content: center;
  gap: 20px;
  width: 100%;
  height: 100%;
  display: flex;
}

.app-selector {
  justify-content: center;
  gap: 15px;
  display: flex;
}

.app-icon {
  cursor: pointer;
  background: #ffffff1a;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 18px;
  transition: all .3s;
  display: flex;
}

.app-icon.active {
  background: #4facfe4d;
  border: 2px solid #4facfe;
}

.tone-options {
  flex-direction: column;
  gap: 8px;
  display: flex;
}

.tone-btn {
  color: #fff;
  text-align: center;
  cursor: pointer;
  background: #ffffff1a;
  border-radius: 12px;
  padding: 8px 12px;
  font-size: 11px;
  transition: all .3s;
}

.tone-btn.active {
  background: #4facfe4d;
  border: 1px solid #4facfe;
}

.languages-interface {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.lang-wheel {
  border: 2px solid #ffffff4d;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  position: relative;
}

.lang-item {
  color: #fff;
  background: #43e97b4d;
  border: 1px solid #43e97b80;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  position: absolute;
}

.lang-item:first-child {
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
}

.lang-item:nth-child(2) {
  top: 15px;
  right: -15px;
}

.lang-item:nth-child(3) {
  bottom: 15px;
  right: -15px;
}

.lang-item:nth-child(4) {
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
}

.lang-item:nth-child(5) {
  bottom: 15px;
  left: -15px;
}

.lang-item:nth-child(6) {
  top: 15px;
  left: -15px;
}

.lang-center {
  color: #fff;
  background: #43e97b80;
  border: 2px solid #43e97bb3;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.feature-text {
  text-align: center;
}

.feature-title {
  text-shadow: 0 2px 4px #0000001a;
  margin-bottom: 12px;
  font-size: 1.5rem;
  font-weight: 700;
}

.feature-description {
  opacity: .9;
  text-shadow: 0 1px 2px #0000001a;
  font-size: 1rem;
  line-height: 1.6;
}

@media (width <= 768px) {
  .features-showcase {
    padding: 60px 15px;
  }

  .features-title {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-large {
    grid-column: span 1;
  }

  .feature-card {
    padding: 20px;
  }

  .phone-mockup {
    width: 150px;
    height: 220px;
    padding: 15px;
  }
}

@media (width <= 480px) {
  .features-title {
    font-size: 2rem;
  }

  .features-subtitle {
    font-size: 1rem;
  }

  .phone-mockup {
    width: 120px;
    height: 180px;
    padding: 10px;
  }
}


/*# sourceMappingURL=src_Style_185f403e._.css.map*/