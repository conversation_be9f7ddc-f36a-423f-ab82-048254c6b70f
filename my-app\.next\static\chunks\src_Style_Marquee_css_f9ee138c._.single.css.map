{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/Marquee.css"], "sourcesContent": ["/* Marquee Container */\r\n.marquee-container {\r\n  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  width: 100%;\r\n  min-height: 100px;\r\n  padding: 20px 0;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.marquee-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(90deg,\r\n    rgba(0,0,0,0.15) 0%,\r\n    transparent 15%,\r\n    transparent 85%,\r\n    rgba(0,0,0,0.15) 100%);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n.marquee-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: nowrap;\r\n  will-change: transform;\r\n}\r\n\r\n/* Marquee Items */\r\n.marquee-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  flex-shrink: 0;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  min-width: fit-content;\r\n}\r\n\r\n.marquee-content {\r\n  padding: 12px 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: white;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  white-space: nowrap;\r\n  letter-spacing: 0.5px;\r\n  user-select: none;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .marquee-content {\r\n    font-size: 15px;\r\n    padding: 11px 18px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .marquee-container {\r\n    min-height: 80px;\r\n    padding: 15px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 14px;\r\n    padding: 10px 16px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 15px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .marquee-container {\r\n    min-height: 70px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 13px;\r\n    padding: 8px 14px;\r\n    letter-spacing: 0.3px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 320px) {\r\n  .marquee-container {\r\n    min-height: 60px;\r\n    padding: 10px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 10px;\r\n  }\r\n}\r\n\r\n/* Legacy styles for backward compatibility */\r\n.MarqueeBox {\r\n  font-family: system-ui;\r\n  background: #111;\r\n  color: white;\r\n  text-align: center;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 20vh;\r\n}\r\n\r\n.carousel {\r\n  background: blue;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: green;\r\n  margin: 0;\r\n  padding: 0;\r\n  position: relative;\r\n  color: black;\r\n  font-size: 121px;\r\n  cursor: pointer;\r\n}\r\n\r\n.test {\r\n  padding: 20px;\r\n}\r\n\r\n.test-2 {\r\n  padding: 20px 10px;\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;AAaA;;;;;;;;;AAgBA;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;;;;;;;AAYA;EACE;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;;EAMA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAMF;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;AAIA"}}]}