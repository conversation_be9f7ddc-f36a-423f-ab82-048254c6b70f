/* [project]/src/Style/ContentImageSection.css [app-client] (css) */
.content-image-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
}

.content-image-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
}

.content-side {
  flex-direction: column;
  gap: 40px;
  display: flex;
}

.content-header {
  flex-direction: column;
  gap: 20px;
  display: flex;
}

.content-badge {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  width: fit-content;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
}

.content-title {
  color: #1a202c;
  margin: 0;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.content-description {
  color: #4a5568;
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-grid {
  grid-template-columns: 1fr;
  gap: 24px;
  display: grid;
}

.feature-item {
  background: #fff;
  border-radius: 12px;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 6px #0000000d;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px #0000001a;
}

.feature-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 2rem;
  display: flex;
}

.feature-content {
  flex: 1;
}

.feature-title {
  color: #1a202c;
  margin: 0 0 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.feature-description {
  color: #4a5568;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.content-actions {
  flex-wrap: wrap;
  gap: 16px;
  display: flex;
}

.primary-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 14px 28px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 4px 15px #667eea4d;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #667eea66;
}

.secondary-btn {
  color: #667eea;
  cursor: pointer;
  background: none;
  border: 2px solid #667eea;
  border-radius: 8px;
  padding: 12px 26px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.secondary-btn:hover {
  color: #fff;
  background: #667eea;
  transform: translateY(-2px);
}

.image-side {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.image-container {
  width: 100%;
  max-width: 500px;
  position: relative;
}

.main-device {
  z-index: 2;
  background: #1a202c;
  border-radius: 20px;
  padding: 20px;
  position: relative;
  box-shadow: 0 20px 40px #0003;
}

.device-screen {
  background: #2d3748;
  border-radius: 12px;
  overflow: hidden;
}

.screen-header {
  background: #4a5568;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  display: flex;
}

.screen-dots {
  gap: 6px;
  display: flex;
}

.dot {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.dot.red {
  background: #f56565;
}

.dot.yellow {
  background: #ed8936;
}

.dot.green {
  background: #48bb78;
}

.screen-title {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.screen-content {
  flex-direction: column;
  align-items: center;
  gap: 30px;
  padding: 30px 20px;
  display: flex;
}

.voice-wave {
  justify-content: center;
  align-items: center;
  gap: 4px;
  display: flex;
}

.wave-bar {
  background: #667eea;
  border-radius: 2px;
  width: 4px;
  animation: 1.5s ease-in-out infinite wave;
}

.wave-bar:first-child {
  height: 20px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 35px;
  animation-delay: .1s;
}

.wave-bar:nth-child(3) {
  height: 25px;
  animation-delay: .2s;
}

.wave-bar:nth-child(4) {
  height: 40px;
  animation-delay: .3s;
}

.wave-bar:nth-child(5) {
  height: 30px;
  animation-delay: .4s;
}

.wave-bar:nth-child(6) {
  height: 35px;
  animation-delay: .5s;
}

.wave-bar:nth-child(7) {
  height: 20px;
  animation-delay: .6s;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(.3);
  }
}

.text-output {
  background: #4a5568;
  border-radius: 8px;
  align-items: center;
  width: 100%;
  min-height: 60px;
  padding: 16px;
  display: flex;
}

.typing-text {
  color: #e2e8f0;
  font-family: Courier New, monospace;
  font-size: 14px;
}

.cursor {
  color: #667eea;
  animation: 1s infinite blink;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }

  51%, 100% {
    opacity: 0;
  }
}

.floating-elements {
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.floating-card {
  color: #1a202c;
  background: #fff;
  border-radius: 12px;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  animation: 3s ease-in-out infinite float;
  display: flex;
  position: absolute;
  box-shadow: 0 8px 25px #00000026;
}

.card-1 {
  animation-delay: 0s;
  top: 20%;
  left: -10%;
}

.card-2 {
  animation-delay: 1s;
  top: 60%;
  right: -15%;
}

.card-3 {
  animation-delay: 2s;
  bottom: 20%;
  left: -5%;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

.card-icon {
  font-size: 18px;
}

@media (width <= 1024px) {
  .content-image-container {
    gap: 60px;
  }

  .content-title {
    font-size: 3rem;
  }
}

@media (width <= 768px) {
  .content-image-section {
    padding: 80px 20px;
  }

  .content-image-container {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .content-title {
    text-align: center;
    font-size: 2.5rem;
  }

  .content-header {
    text-align: center;
  }

  .content-badge {
    align-self: center;
  }

  .content-actions {
    justify-content: center;
  }

  .floating-card {
    display: none;
  }
}

@media (width <= 480px) {
  .content-image-section {
    padding: 60px 15px;
  }

  .content-title {
    font-size: 2rem;
  }

  .content-description {
    font-size: 1rem;
  }

  .feature-item {
    padding: 16px;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .main-device {
    padding: 15px;
  }

  .screen-content {
    padding: 20px 15px;
  }
}

/*# sourceMappingURL=src_Style_ContentImageSection_css_f9ee138c._.single.css.map*/