/* [project]/src/Style/FAQSection.css [app-client] (css) */
.faq-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  min-height: 100vh;
  padding: 120px 20px;
}

.faq-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.faq-header {
  text-align: center;
  margin-bottom: 60px;
}

.faq-title {
  color: #92400e;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.faq-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 80px;
  display: grid;
}

.faq-item {
  background: #fff;
  border: 2px solid #0000;
  border-radius: 16px;
  transition: all .3s;
  overflow: hidden;
  box-shadow: 0 4px 20px #00000014;
}

.faq-item:hover {
  border-color: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 8px 30px #0000001f;
}

.faq-item.active {
  border-color: #f59e0b;
  box-shadow: 0 8px 30px #f59e0b33;
}

.faq-question {
  cursor: pointer;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  padding: 24px;
  transition: background-color .3s;
  display: flex;
}

.faq-question:hover {
  background-color: #fef3c7;
}

.faq-item.active .faq-question {
  background-color: #fef3c7;
  border-bottom: 1px solid #fde68a;
}

.faq-question h3 {
  color: #1f2937;
  flex: 1;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
}

.faq-icon {
  color: #f59e0b;
  background: #f59e0b1a;
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  transition: all .3s;
  display: flex;
}

.faq-answer {
  opacity: 0;
  height: 0;
  transition: all .4s;
  overflow: hidden;
}

.faq-item.active .faq-answer {
  opacity: 1;
  height: auto;
}

.faq-answer p {
  color: #4b5563;
  margin: 0;
  padding: 0 24px 24px;
  font-size: 1rem;
  line-height: 1.6;
}

.faq-footer {
  text-align: center;
  backdrop-filter: blur(10px);
  background: #fff9;
  border: 1px solid #f59e0b33;
  border-radius: 20px;
  padding: 48px 32px;
}

.contact-support h3 {
  color: #92400e;
  margin: 0 0 16px;
  font-size: 2rem;
  font-weight: 700;
}

.contact-support p {
  color: #78350f;
  margin: 0 0 32px;
  font-size: 1.125rem;
  line-height: 1.6;
}

.contact-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: inline-flex;
  box-shadow: 0 4px 15px #f59e0b4d;
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #f59e0b66;
}

.arrow {
  transition: transform .3s;
}

.contact-btn:hover .arrow {
  transform: translateX(4px);
}

@media (width <= 1024px) {
  .faq-title {
    font-size: 3rem;
  }

  .faq-grid {
    gap: 20px;
  }

  .contact-support h3 {
    font-size: 1.75rem;
  }
}

@media (width <= 768px) {
  .faq-section {
    padding: 80px 20px;
  }

  .faq-title {
    font-size: 2.5rem;
  }

  .faq-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .faq-question {
    padding: 20px;
  }

  .faq-question h3 {
    font-size: 1rem;
  }

  .faq-answer p {
    padding: 0 20px 20px;
    font-size: .875rem;
  }

  .faq-footer {
    padding: 32px 24px;
  }

  .contact-support h3 {
    font-size: 1.5rem;
  }

  .contact-support p {
    font-size: 1rem;
  }
}

@media (width <= 480px) {
  .faq-section {
    padding: 60px 15px;
  }

  .faq-title {
    font-size: 2rem;
  }

  .faq-question {
    padding: 16px;
  }

  .faq-question h3 {
    font-size: .875rem;
  }

  .faq-icon {
    width: 28px;
    height: 28px;
  }

  .faq-answer p {
    padding: 0 16px 16px;
    font-size: .8rem;
  }

  .faq-footer {
    padding: 24px 20px;
  }

  .contact-support h3 {
    font-size: 1.25rem;
  }

  .contact-support p {
    font-size: .875rem;
  }

  .contact-btn {
    padding: 14px 24px;
    font-size: 1rem;
  }
}

/*# sourceMappingURL=src_Style_FAQSection_css_f9ee138c._.single.css.map*/