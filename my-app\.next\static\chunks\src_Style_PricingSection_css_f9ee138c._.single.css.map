{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/PricingSection.css"], "sourcesContent": ["/* Pricing Section */\n.pricing-section {\n  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n}\n\n.pricing-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.pricing-header {\n  text-align: center;\n  margin-bottom: 60px;\n}\n\n.pricing-title {\n  font-size: 4rem;\n  font-weight: 700;\n  color: #581c87;\n  margin: 0 0 16px 0;\n  font-family: 'Georgia', serif;\n}\n\n.pricing-subtitle {\n  font-size: 1.2rem;\n  color: #7c3aed;\n  margin: 0 0 40px 0;\n  line-height: 1.6;\n}\n\n.billing-toggle {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 20px;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 8px;\n  border-radius: 50px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(139, 92, 246, 0.2);\n  width: fit-content;\n  margin: 0 auto;\n}\n\n.toggle-label {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #6b7280;\n  transition: color 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.toggle-label.active {\n  color: #7c3aed;\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  font-size: 0.75rem;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-weight: 600;\n}\n\n.toggle-switch {\n  position: relative;\n  width: 60px;\n  height: 32px;\n  background: #e5e7eb;\n  border-radius: 16px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n}\n\n.toggle-switch:hover {\n  background: #d1d5db;\n}\n\n.toggle-slider {\n  position: absolute;\n  top: 2px;\n  left: 2px;\n  width: 28px;\n  height: 28px;\n  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);\n  border-radius: 50%;\n  transition: transform 0.3s ease;\n  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);\n}\n\n.toggle-slider.yearly {\n  transform: translateX(28px);\n}\n\n.pricing-cards {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 24px;\n  margin-bottom: 60px;\n}\n\n.pricing-card {\n  background: white;\n  border-radius: 20px;\n  padding: 32px 24px;\n  position: relative;\n  border: 2px solid transparent;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.pricing-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.pricing-card.popular {\n  border-color: #7c3aed;\n  transform: scale(1.05);\n  box-shadow: 0 8px 30px rgba(124, 58, 237, 0.2);\n}\n\n.popular-badge {\n  position: absolute;\n  top: -12px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);\n  color: white;\n  padding: 6px 20px;\n  border-radius: 20px;\n  font-size: 0.875rem;\n  font-weight: 600;\n}\n\n.card-header {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.plan-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0 0 16px 0;\n}\n\n.price-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.price-main {\n  display: flex;\n  align-items: baseline;\n  gap: 4px;\n}\n\n.price {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #7c3aed;\n}\n\n.period {\n  font-size: 1rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.price-details {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.original-price {\n  font-size: 0.875rem;\n  color: #9ca3af;\n  text-decoration: line-through;\n}\n\n.savings {\n  font-size: 0.75rem;\n  background: #dcfce7;\n  color: #166534;\n  padding: 2px 8px;\n  border-radius: 8px;\n  font-weight: 600;\n}\n\n.card-features {\n  margin-bottom: 32px;\n}\n\n.features-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.feature-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  font-size: 0.875rem;\n  color: #4b5563;\n  line-height: 1.5;\n}\n\n.check-icon {\n  color: #10b981;\n  font-weight: 700;\n  font-size: 1rem;\n  flex-shrink: 0;\n  margin-top: 2px;\n}\n\n.card-footer {\n  text-align: center;\n}\n\n.cta-button {\n  width: 100%;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n}\n\n.cta-button.primary {\n  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);\n  color: white;\n  border-color: #7c3aed;\n}\n\n.cta-button.primary:hover {\n  background: linear-gradient(135deg, #6d28d9 0%, #4c1d95 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);\n}\n\n.cta-button.outline {\n  background: transparent;\n  color: #7c3aed;\n  border-color: #7c3aed;\n}\n\n.cta-button.outline:hover {\n  background: #7c3aed;\n  color: white;\n  transform: translateY(-1px);\n}\n\n.pricing-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.6);\n  padding: 24px 32px;\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(139, 92, 246, 0.1);\n}\n\n.money-back-guarantee {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.guarantee-icon {\n  font-size: 2rem;\n}\n\n.guarantee-text strong {\n  color: #1f2937;\n  font-size: 1rem;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.guarantee-text p {\n  color: #6b7280;\n  font-size: 0.875rem;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.contact-sales-btn {\n  background: transparent;\n  color: #7c3aed;\n  border: 2px solid #7c3aed;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.contact-sales-btn:hover {\n  background: #7c3aed;\n  color: white;\n  transform: translateY(-1px);\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .pricing-cards {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 20px;\n  }\n\n  .pricing-card.popular {\n    transform: none;\n  }\n\n  .pricing-footer {\n    flex-direction: column;\n    gap: 20px;\n    text-align: center;\n  }\n}\n\n@media (max-width: 768px) {\n  .pricing-section {\n    padding: 80px 20px;\n  }\n\n  .pricing-title {\n    font-size: 2.5rem;\n  }\n\n  .pricing-subtitle {\n    font-size: 1rem;\n  }\n\n  .billing-toggle {\n    flex-direction: column;\n    gap: 12px;\n    padding: 16px;\n  }\n\n  .toggle-label {\n    font-size: 0.875rem;\n  }\n\n  .discount-badge {\n    font-size: 0.7rem;\n  }\n\n  .pricing-cards {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n\n  .pricing-card {\n    padding: 24px 20px;\n  }\n\n  .plan-title {\n    font-size: 1.25rem;\n  }\n\n  .price {\n    font-size: 2rem;\n  }\n\n  .money-back-guarantee {\n    flex-direction: column;\n    text-align: center;\n    gap: 12px;\n  }\n}\n\n@media (max-width: 480px) {\n  .pricing-section {\n    padding: 60px 15px;\n  }\n\n  .pricing-title {\n    font-size: 2rem;\n  }\n\n  .pricing-header {\n    margin-bottom: 40px;\n  }\n\n  .billing-toggle {\n    width: 100%;\n    max-width: 300px;\n  }\n\n  .pricing-card {\n    padding: 20px 16px;\n  }\n\n  .price {\n    font-size: 1.75rem;\n  }\n\n  .feature-item {\n    font-size: 0.8rem;\n  }\n\n  .pricing-footer {\n    padding: 20px;\n  }\n\n  .guarantee-text strong {\n    font-size: 0.875rem;\n  }\n\n  .guarantee-text p {\n    font-size: 0.8rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}