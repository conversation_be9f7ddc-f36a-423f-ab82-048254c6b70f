{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/PricingSection.css"], "sourcesContent": ["/* Pricing Section */\n.pricing-section {\n  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n}\n\n.pricing-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.pricing-header {\n  text-align: center;\n  margin-bottom: 60px;\n}\n\n.pricing-title {\n  font-size: 4rem;\n  font-weight: 700;\n  color: #581c87;\n  margin: 0 0 16px 0;\n  font-family: 'Georgia', serif;\n}\n\n.pricing-subtitle {\n  font-size: 1.2rem;\n  color: #7c3aed;\n  margin: 0 0 40px 0;\n  line-height: 1.6;\n}\n\n.billing-toggle {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 20px;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 8px;\n  border-radius: 50px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(139, 92, 246, 0.2);\n  width: fit-content;\n  margin: 0 auto;\n}\n\n.toggle-label {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #6b7280;\n  transition: color 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.toggle-label.active {\n  color: #7c3aed;\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  font-size: 0.75rem;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-weight: 600;\n}\n\n.toggle-switch {\n  position: relative;\n  width: 60px;\n  height: 32px;\n  background: #e5e7eb;\n  border-radius: 16px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n}\n\n.toggle-switch:hover {\n  background: #d1d5db;\n}\n\n.toggle-slider {\n  position: absolute;\n  top: 2px;\n  left: 2px;\n  width: 28px;\n  height: 28px;\n  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);\n  border-radius: 50%;\n  transition: transform 0.3s ease;\n  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);\n}\n\n.toggle-slider.yearly {\n  transform: translateX(28px);\n}\n\n.pricing-cards {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 24px;\n  margin-bottom: 60px;\n}\n\n.pricing-card {\n  background: white;\n  border-radius: 20px;\n  padding: 32px 24px;\n  position: relative;\n  border: 2px solid transparent;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.pricing-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.pricing-card.popular {\n  border-color: #7c3aed;\n  transform: scale(1.05);\n  box-shadow: 0 8px 30px rgba(124, 58, 237, 0.2);\n}\n\n.popular-badge {\n  position: absolute;\n  top: -12px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);\n  color: white;\n  padding: 6px 20px;\n  border-radius: 20px;\n  font-size: 0.875rem;\n  font-weight: 600;\n}\n\n.card-header {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.plan-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0 0 16px 0;\n}\n\n.price-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.price-main {\n  display: flex;\n  align-items: baseline;\n  gap: 4px;\n}\n\n.price {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #7c3aed;\n}\n\n.period {\n  font-size: 1rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.price-details {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.original-price {\n  font-size: 0.875rem;\n  color: #9ca3af;\n  text-decoration: line-through;\n}\n\n.savings {\n  font-size: 0.75rem;\n  background: #dcfce7;\n  color: #166534;\n  padding: 2px 8px;\n  border-radius: 8px;\n  font-weight: 600;\n}\n\n.card-features {\n  margin-bottom: 32px;\n}\n\n.features-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.feature-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  font-size: 0.875rem;\n  color: #4b5563;\n  line-height: 1.5;\n}\n\n.check-icon {\n  color: #10b981;\n  font-weight: 700;\n  font-size: 1rem;\n  flex-shrink: 0;\n  margin-top: 2px;\n}\n\n.card-footer {\n  text-align: center;\n}\n\n.cta-button {\n  width: 100%;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n}\n\n.cta-button.primary {\n  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);\n  color: white;\n  border-color: #7c3aed;\n}\n\n.cta-button.primary:hover {\n  background: linear-gradient(135deg, #6d28d9 0%, #4c1d95 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);\n}\n\n.cta-button.outline {\n  background: transparent;\n  color: #7c3aed;\n  border-color: #7c3aed;\n}\n\n.cta-button.outline:hover {\n  background: #7c3aed;\n  color: white;\n  transform: translateY(-1px);\n}\n\n.pricing-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.6);\n  padding: 24px 32px;\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(139, 92, 246, 0.1);\n}\n\n.money-back-guarantee {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.guarantee-icon {\n  font-size: 2rem;\n}\n\n.guarantee-text strong {\n  color: #1f2937;\n  font-size: 1rem;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.guarantee-text p {\n  color: #6b7280;\n  font-size: 0.875rem;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.contact-sales-btn {\n  background: transparent;\n  color: #7c3aed;\n  border: 2px solid #7c3aed;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.contact-sales-btn:hover {\n  background: #7c3aed;\n  color: white;\n  transform: translateY(-1px);\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .pricing-cards {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 20px;\n  }\n\n  .pricing-card.popular {\n    transform: none;\n  }\n\n  .pricing-footer {\n    flex-direction: column;\n    gap: 20px;\n    text-align: center;\n  }\n}\n\n@media (max-width: 768px) {\n  .pricing-section {\n    padding: 80px 20px;\n  }\n\n  .pricing-title {\n    font-size: 2.5rem;\n  }\n\n  .pricing-subtitle {\n    font-size: 1rem;\n  }\n\n  .billing-toggle {\n    flex-direction: column;\n    gap: 12px;\n    padding: 16px;\n  }\n\n  .toggle-label {\n    font-size: 0.875rem;\n  }\n\n  .discount-badge {\n    font-size: 0.7rem;\n  }\n\n  .pricing-cards {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n\n  .pricing-card {\n    padding: 24px 20px;\n  }\n\n  .plan-title {\n    font-size: 1.25rem;\n  }\n\n  .price {\n    font-size: 2rem;\n  }\n\n  .money-back-guarantee {\n    flex-direction: column;\n    text-align: center;\n    gap: 12px;\n  }\n}\n\n@media (max-width: 480px) {\n  .pricing-section {\n    padding: 60px 15px;\n  }\n\n  .pricing-title {\n    font-size: 2rem;\n  }\n\n  .pricing-header {\n    margin-bottom: 40px;\n  }\n\n  .billing-toggle {\n    width: 100%;\n    max-width: 300px;\n  }\n\n  .pricing-card {\n    padding: 20px 16px;\n  }\n\n  .price {\n    font-size: 1.75rem;\n  }\n\n  .feature-item {\n    font-size: 0.8rem;\n  }\n\n  .pricing-footer {\n    padding: 20px;\n  }\n\n  .guarantee-text strong {\n    font-size: 0.875rem;\n  }\n\n  .guarantee-text p {\n    font-size: 0.8rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/SavingsCalculator.css"], "sourcesContent": ["/* Savings Calculator Section */\n.savings-calculator-section {\n  background: linear-gradient(135deg, #064e3b 0%, #047857 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  color: white;\n}\n\n.savings-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 60px;\n}\n\n.content-section {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.text-content {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.section-title {\n  font-size: 3.5rem;\n  font-weight: 700;\n  color: white;\n  margin: 0;\n  line-height: 1.1;\n  font-family: 'Georgia', serif;\n}\n\n.scenario-text {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.scenario-main {\n  font-size: 1.5rem;\n  color: #d1fae5;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.scenario-main strong {\n  color: #6ee7b7;\n  font-weight: 700;\n}\n\n.scenario-detail {\n  font-size: 1rem;\n  color: #a7f3d0;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.value-statement {\n  font-size: 1.25rem;\n  color: #d1fae5;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.value-statement strong {\n  color: #6ee7b7;\n  font-weight: 700;\n}\n\n.input-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.input-group {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.input-group label {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #a7f3d0;\n}\n\n.slider {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 100%;\n  height: 8px;\n  border-radius: 4px;\n  background: #065f46;\n  outline: none;\n  cursor: pointer;\n}\n\n.slider::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  cursor: pointer;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n  transition: all 0.3s ease;\n}\n\n.slider::-webkit-slider-thumb:hover {\n  transform: scale(1.1);\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\n}\n\n.slider::-moz-range-thumb {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  cursor: pointer;\n  border: none;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n}\n\n.slider-value {\n  font-size: 1.125rem;\n  font-weight: 700;\n  color: #6ee7b7;\n  text-align: center;\n  background: rgba(16, 185, 129, 0.2);\n  padding: 8px 16px;\n  border-radius: 20px;\n  width: fit-content;\n  align-self: center;\n}\n\n.illustration-section {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.character-illustration {\n  position: relative;\n  width: 300px;\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.character {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n  z-index: 2;\n}\n\n.character-head {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.face {\n  font-size: 4rem;\n  animation: bounce 2s ease-in-out infinite;\n}\n\n.hat {\n  font-size: 2rem;\n  position: absolute;\n  top: -20px;\n  animation: tilt 3s ease-in-out infinite;\n}\n\n.character-body {\n  display: flex;\n  justify-content: center;\n}\n\n.arms {\n  display: flex;\n  gap: 40px;\n}\n\n.arm {\n  font-size: 2rem;\n  animation: juggle 1.5s ease-in-out infinite;\n}\n\n.arm.left {\n  animation-delay: 0s;\n}\n\n.arm.right {\n  animation-delay: 0.75s;\n}\n\n.money-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.money-icon {\n  position: absolute;\n  font-size: 2rem;\n  animation: float 3s ease-in-out infinite;\n}\n\n@keyframes bounce {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-10px); }\n}\n\n@keyframes tilt {\n  0%, 100% { transform: rotate(-5deg); }\n  50% { transform: rotate(5deg); }\n}\n\n@keyframes juggle {\n  0%, 100% { transform: translateY(0) rotate(0deg); }\n  50% { transform: translateY(-15px) rotate(10deg); }\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.8; }\n  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }\n}\n\n.calculator-section {\n  display: flex;\n  justify-content: center;\n}\n\n.calculator-card {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 24px;\n  padding: 40px;\n  max-width: 500px;\n  width: 100%;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n}\n\n.calculator-header {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.calculator-header h3 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #a7f3d0;\n  margin: 0 0 16px 0;\n}\n\n.savings-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #6ee7b7;\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  gap: 8px;\n}\n\n.period {\n  font-size: 1.5rem;\n  color: #a7f3d0;\n  font-weight: 500;\n}\n\n.breakdown {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  margin-bottom: 32px;\n  padding: 24px 0;\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.breakdown-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.breakdown-item .label {\n  font-size: 1rem;\n  color: #d1fae5;\n}\n\n.breakdown-item .value {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #6ee7b7;\n}\n\n.breakdown-item .value.negative {\n  color: #fca5a5;\n}\n\n.calculator-footer {\n  text-align: center;\n}\n\n.get-started-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin: 0 auto;\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n}\n\n.get-started-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);\n}\n\n.arrow {\n  transition: transform 0.3s ease;\n}\n\n.get-started-btn:hover .arrow {\n  transform: translateX(4px);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .content-section {\n    gap: 60px;\n  }\n\n  .section-title {\n    font-size: 3rem;\n  }\n\n  .character-illustration {\n    width: 250px;\n    height: 250px;\n  }\n\n  .face {\n    font-size: 3rem;\n  }\n\n  .calculator-card {\n    padding: 32px;\n  }\n}\n\n@media (max-width: 768px) {\n  .savings-calculator-section {\n    padding: 80px 20px;\n  }\n\n  .content-section {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n  }\n\n  .scenario-main {\n    font-size: 1.25rem;\n  }\n\n  .value-statement {\n    font-size: 1.125rem;\n  }\n\n  .character-illustration {\n    width: 200px;\n    height: 200px;\n  }\n\n  .face {\n    font-size: 2.5rem;\n  }\n\n  .hat {\n    font-size: 1.5rem;\n  }\n\n  .arm {\n    font-size: 1.5rem;\n  }\n\n  .money-icon {\n    font-size: 1.5rem;\n  }\n\n  .calculator-card {\n    padding: 24px;\n    margin: 0 20px;\n  }\n\n  .savings-amount {\n    font-size: 2.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .savings-calculator-section {\n    padding: 60px 15px;\n  }\n\n  .section-title {\n    font-size: 2rem;\n  }\n\n  .scenario-main {\n    font-size: 1.125rem;\n  }\n\n  .scenario-detail {\n    font-size: 0.875rem;\n  }\n\n  .value-statement {\n    font-size: 1rem;\n  }\n\n  .character-illustration {\n    width: 150px;\n    height: 150px;\n  }\n\n  .face {\n    font-size: 2rem;\n  }\n\n  .calculator-card {\n    padding: 20px;\n    margin: 0 10px;\n  }\n\n  .savings-amount {\n    font-size: 2rem;\n  }\n\n  .calculator-header h3 {\n    font-size: 1.25rem;\n  }\n\n  .breakdown-item .label,\n  .breakdown-item .value {\n    font-size: 0.875rem;\n  }\n\n  .get-started-btn {\n    padding: 14px 24px;\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;AAiBA;;;;;AAKA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAYA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAKA", "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/FAQSection.css"], "sourcesContent": ["/* FAQ Section */\n.faq-section {\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n}\n\n.faq-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.faq-header {\n  text-align: center;\n  margin-bottom: 60px;\n}\n\n.faq-title {\n  font-size: 3.5rem;\n  font-weight: 700;\n  color: #92400e;\n  margin: 0;\n  line-height: 1.1;\n  font-family: 'Georgia', serif;\n}\n\n.faq-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 24px;\n  margin-bottom: 80px;\n}\n\n.faq-item {\n  background: white;\n  border-radius: 16px;\n  border: 2px solid transparent;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.faq-item:hover {\n  border-color: #f59e0b;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.faq-item.active {\n  border-color: #f59e0b;\n  box-shadow: 0 8px 30px rgba(245, 158, 11, 0.2);\n}\n\n.faq-question {\n  padding: 24px;\n  cursor: pointer;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 16px;\n  transition: background-color 0.3s ease;\n}\n\n.faq-question:hover {\n  background-color: #fef3c7;\n}\n\n.faq-item.active .faq-question {\n  background-color: #fef3c7;\n  border-bottom: 1px solid #fde68a;\n}\n\n.faq-question h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n}\n\n.faq-icon {\n  color: #f59e0b;\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(245, 158, 11, 0.1);\n}\n\n.faq-answer {\n  height: 0;\n  opacity: 0;\n  overflow: hidden;\n  transition: all 0.4s ease;\n}\n\n.faq-item.active .faq-answer {\n  height: auto;\n  opacity: 1;\n}\n\n.faq-answer p {\n  padding: 0 24px 24px 24px;\n  margin: 0;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: #4b5563;\n}\n\n.faq-footer {\n  text-align: center;\n  background: rgba(255, 255, 255, 0.6);\n  padding: 48px 32px;\n  border-radius: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n.contact-support h3 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #92400e;\n  margin: 0 0 16px 0;\n}\n\n.contact-support p {\n  font-size: 1.125rem;\n  color: #78350f;\n  margin: 0 0 32px 0;\n  line-height: 1.6;\n}\n\n.contact-btn {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n}\n\n.contact-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);\n}\n\n.arrow {\n  transition: transform 0.3s ease;\n}\n\n.contact-btn:hover .arrow {\n  transform: translateX(4px);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .faq-title {\n    font-size: 3rem;\n  }\n  \n  .faq-grid {\n    gap: 20px;\n  }\n  \n  .contact-support h3 {\n    font-size: 1.75rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .faq-section {\n    padding: 80px 20px;\n  }\n  \n  .faq-title {\n    font-size: 2.5rem;\n  }\n  \n  .faq-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n  \n  .faq-question {\n    padding: 20px;\n  }\n  \n  .faq-question h3 {\n    font-size: 1rem;\n  }\n  \n  .faq-answer p {\n    padding: 0 20px 20px 20px;\n    font-size: 0.875rem;\n  }\n  \n  .faq-footer {\n    padding: 32px 24px;\n  }\n  \n  .contact-support h3 {\n    font-size: 1.5rem;\n  }\n  \n  .contact-support p {\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .faq-section {\n    padding: 60px 15px;\n  }\n  \n  .faq-title {\n    font-size: 2rem;\n  }\n  \n  .faq-question {\n    padding: 16px;\n  }\n  \n  .faq-question h3 {\n    font-size: 0.875rem;\n  }\n  \n  .faq-icon {\n    width: 28px;\n    height: 28px;\n  }\n  \n  .faq-answer p {\n    padding: 0 16px 16px 16px;\n    font-size: 0.8rem;\n  }\n  \n  .faq-footer {\n    padding: 24px 20px;\n  }\n  \n  .contact-support h3 {\n    font-size: 1.25rem;\n  }\n  \n  .contact-support p {\n    font-size: 0.875rem;\n  }\n  \n  .contact-btn {\n    padding: 14px 24px;\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAIA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}