"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/StartFlowingSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const StartFlowingSection = () => {
    const sectionRef = useRef(null);
    const contentRef = useRef(null);
    const dottedLineRef = useRef(null);

    useEffect(() => {
        const section = sectionRef.current;
        const content = contentRef.current;
        const dottedLine = dottedLineRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([content], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(content, {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out"
        });

        // Animate the dotted line
        if (dottedLine) {
            gsap.set(dottedLine, { 
                strokeDasharray: "10,10",
                strokeDashoffset: 0
            });

            gsap.to(dottedLine, {
                strokeDashoffset: -20,
                duration: 2,
                ease: "none",
                repeat: -1
            });
        }

        // Floating animation for the entire content
        gsap.to(content, {
            y: -10,
            duration: 4,
            ease: "power2.inOut",
            yoyo: true,
            repeat: -1,
            delay: 1
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="start-flowing-section" ref={sectionRef}>
            <div className="start-flowing-container">
                <div className="content-wrapper" ref={contentRef}>
                    <div className="hero-content">
                        <h1 className="main-title">
                            Transform your voice into
                            <span className="highlight-text"> perfect text</span>
                        </h1>

                        <p className="subtitle">
                            Experience the future of productivity with Flow's AI-powered voice dictation.
                            Speak naturally and watch your ideas come to life instantly.
                        </p>

                        <div className="cta-section">
                            <button className="primary-cta">
                                Get Started Free
                            </button>
                            <button className="secondary-cta">
                                Watch Demo
                            </button>
                        </div>

                        <div className="trust-indicators">
                            <div className="trust-item">
                                <span className="trust-number">1M+</span>
                                <span className="trust-label">Happy Users</span>
                            </div>
                            <div className="trust-item">
                                <span className="trust-number">4x</span>
                                <span className="trust-label">Faster</span>
                            </div>
                            <div className="trust-item">
                                <span className="trust-number">99.9%</span>
                                <span className="trust-label">Accurate</span>
                            </div>
                        </div>
                    </div>

                    <div className="hero-visual">
                        <div className="demo-window">
                            <div className="window-header">
                                <div className="window-controls">
                                    <span className="control red"></span>
                                    <span className="control yellow"></span>
                                    <span className="control green"></span>
                                </div>
                                <div className="window-title">Flow - Voice Dictation</div>
                            </div>
                            <div className="window-content">
                                <div className="voice-indicator">
                                    <div className="pulse-ring"></div>
                                    <div className="microphone-icon">🎤</div>
                                </div>
                                <div className="typing-demo">
                                    <p className="demo-text">
                                        "Transform your voice into perfect text with Flow's AI-powered dictation..."
                                    </p>
                                    <div className="cursor-blink"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default StartFlowingSection;
