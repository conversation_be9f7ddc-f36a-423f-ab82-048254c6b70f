"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/StartFlowingSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const StartFlowingSection = () => {
    const sectionRef = useRef(null);
    const contentRef = useRef(null);
    const dottedLineRef = useRef(null);

    useEffect(() => {
        const section = sectionRef.current;
        const content = contentRef.current;
        const dottedLine = dottedLineRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([content], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(content, {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out"
        });

        // Animate the dotted line
        if (dottedLine) {
            gsap.set(dottedLine, { 
                strokeDasharray: "10,10",
                strokeDashoffset: 0
            });

            gsap.to(dottedLine, {
                strokeDashoffset: -20,
                duration: 2,
                ease: "none",
                repeat: -1
            });
        }

        // Floating animation for the entire content
        gsap.to(content, {
            y: -10,
            duration: 4,
            ease: "power2.inOut",
            yoyo: true,
            repeat: -1,
            delay: 1
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="start-flowing-section" ref={sectionRef}>
            <div className="background-overlay"></div>
            <div className="background-blur"></div>
            
            <svg className="dotted-line-svg" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
                <path
                    ref={dottedLineRef}
                    className="dotted-line"
                    d="M 800 100 Q 1000 200 900 400 Q 800 600 1100 700"
                    fill="none"
                    stroke="rgba(255, 255, 255, 0.6)"
                    strokeWidth="3"
                    strokeLinecap="round"
                />
            </svg>

            <div className="start-flowing-container">
                <div className="content-wrapper" ref={contentRef}>
                    <h1 className="main-title">
                        Start flowing
                        <span className="title-dots">.....</span>
                    </h1>
                    
                    <p className="subtitle">
                        Effortless voice dictation in every application. 4x<br />
                        faster than typing, AI commands and auto-edits.
                    </p>
                    
                    <div className="cta-buttons">
                        <button className="primary-cta">
                            🎤 Try Flow
                        </button>
                        <button className="secondary-cta">
                            Download
                        </button>
                    </div>
                    
                    <p className="availability-text">
                        Available on Mac, Windows and iPhone
                    </p>
                </div>
            </div>
        </section>
    );
};

export default StartFlowingSection;
