/* [project]/src/Style/MultiDeviceSection.css [app-client] (css) */
.multi-device-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  min-height: 200vh;
  padding: 120px 20px;
}

.multi-device-container {
  flex-direction: column;
  gap: 120px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
}

.device-row {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  display: grid;
}

.device-row.reverse {
  direction: rtl;
}

.device-row.reverse > * {
  direction: ltr;
}

.device-mockup {
  background: #1f2937;
  border-radius: 20px;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  box-shadow: 0 25px 50px #0000004d;
}

.device-screen {
  background: #111827;
  border-radius: 12px;
  min-height: 300px;
  overflow: hidden;
}

.screen-header {
  color: #fff;
  background: #374151;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  display: flex;
}

.screen-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  cursor: pointer;
  opacity: .7;
  font-size: 20px;
}

.device-content {
  flex-direction: column;
  gap: 20px;
  max-width: 500px;
  display: flex;
}

.device-content h3 {
  color: #92400e;
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.device-content p {
  color: #78350f;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.dictionary-content {
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  display: flex;
}

.dict-item {
  color: #60a5fa;
  background: #374151;
  border-left: 3px solid #3b82f6;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.dict-item:hover {
  background: #4b5563;
  transform: translateX(4px);
}

.app-selector {
  background: #374151;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  display: flex;
}

.app-icon {
  cursor: pointer;
  background: #4b5563;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 20px;
  transition: all .3s;
  display: flex;
}

.app-icon.active {
  background: #3b82f6;
  transform: scale(1.1);
}

.tones-content {
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  display: flex;
}

.tone-option {
  color: #d1d5db;
  text-align: center;
  cursor: pointer;
  background: #374151;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.tone-option.active {
  color: #fff;
  background: #10b981;
  transform: scale(1.02);
}

.language-wheel {
  width: 200px;
  height: 200px;
  margin: 40px auto;
  position: relative;
}

.language-item {
  color: #fff;
  transform-origin: center;
  background: #3b82f6;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 12px;
  font-weight: 600;
  animation: 20s linear infinite rotate;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
}

@keyframes rotate {
  from {
    transform: rotate(0)translateY(-80px)rotate(0);
  }

  to {
    transform: rotate(360deg)translateY(-80px)rotate(-360deg);
  }
}

.wheel-center {
  background: #1f2937;
  border: 3px solid #3b82f6;
  border-radius: 50%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.lang-count {
  color: #60a5fa;
  font-size: 18px;
  font-weight: 700;
}

.lang-text {
  color: #9ca3af;
  font-size: 10px;
  font-weight: 500;
}

.desktop-device {
  max-width: 500px;
}

.desktop-screen {
  background: #f3f4f6;
  border-radius: 12px;
  min-height: 400px;
  position: relative;
  overflow: hidden;
}

.desktop-header {
  background: #e5e7eb;
  border-bottom: 1px solid #d1d5db;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  display: flex;
}

.window-controls {
  gap: 6px;
  display: flex;
}

.control {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.control.red {
  background: #ef4444;
}

.control.yellow {
  background: #f59e0b;
}

.control.green {
  background: #10b981;
}

.window-title {
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

.desktop-content {
  height: 300px;
  display: flex;
}

.sidebar {
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  flex-direction: column;
  gap: 8px;
  width: 120px;
  padding: 16px 8px;
  display: flex;
}

.sidebar-item {
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  transition: all .3s;
}

.sidebar-item.active {
  color: #fff;
  background: #3b82f6;
}

.main-content {
  background: #fff;
  flex: 1;
  padding: 20px;
}

.document-header {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  display: flex;
}

.document-header h4 {
  color: #1f2937;
  margin: 0;
  font-size: 16px;
}

.doc-status {
  color: #10b981;
  background: #dcfce7;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
}

.document-body {
  flex-direction: column;
  gap: 12px;
  display: flex;
}

.text-line {
  background: #d1d5db;
  border-radius: 2px;
  height: 4px;
}

.text-line.short {
  width: 60%;
}

.text-line.medium {
  width: 80%;
}

.mobile-preview {
  background: #1f2937;
  border-radius: 12px;
  width: 80px;
  height: 120px;
  padding: 8px;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.mobile-screen {
  background: #111827;
  border-radius: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  height: 100%;
  display: flex;
}

.mobile-header {
  color: #fff;
  font-size: 8px;
  font-weight: 600;
}

.voice-indicator {
  align-items: center;
  gap: 2px;
  display: flex;
}

.voice-wave {
  background: #3b82f6;
  border-radius: 1px;
  width: 2px;
  animation: 1s ease-in-out infinite wave;
}

.voice-wave:first-child {
  height: 8px;
  animation-delay: 0s;
}

.voice-wave:nth-child(2) {
  height: 12px;
  animation-delay: .1s;
}

.voice-wave:nth-child(3) {
  height: 6px;
  animation-delay: .2s;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(.3);
  }
}

.cta-button {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 8px;
  width: fit-content;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 4px 15px #f59e0b4d;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #f59e0b66;
}

@media (width <= 1024px) {
  .multi-device-container {
    gap: 100px;
  }

  .device-row {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 2rem;
  }

  .device-mockup {
    max-width: 350px;
  }
}

@media (width <= 768px) {
  .multi-device-section {
    padding: 80px 20px;
  }

  .multi-device-container {
    gap: 80px;
  }

  .device-row {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .device-row.reverse {
    direction: ltr;
  }

  .device-content {
    align-items: center;
    max-width: none;
  }

  .device-content h3 {
    font-size: 1.8rem;
  }

  .device-mockup {
    max-width: 320px;
  }

  .language-wheel {
    width: 160px;
    height: 160px;
  }

  .language-item {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }

  .wheel-center {
    width: 60px;
    height: 60px;
  }

  .lang-count {
    font-size: 14px;
  }

  .lang-text {
    font-size: 8px;
  }
}

@media (width <= 480px) {
  .multi-device-section {
    padding: 60px 15px;
  }

  .multi-device-container {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 1.5rem;
  }

  .device-content p {
    font-size: 1rem;
  }

  .device-mockup {
    max-width: 280px;
    padding: 15px;
  }

  .device-screen {
    min-height: 250px;
  }

  .desktop-content {
    height: 250px;
  }

  .sidebar {
    width: 100px;
    padding: 12px 6px;
  }

  .sidebar-item {
    padding: 6px 8px;
    font-size: 10px;
  }

  .main-content {
    padding: 15px;
  }

  .mobile-preview {
    width: 60px;
    height: 90px;
    bottom: 15px;
    right: 15px;
  }

  .language-wheel {
    width: 140px;
    height: 140px;
  }

  .language-item {
    width: 28px;
    height: 28px;
    font-size: 9px;
  }
}

/*# sourceMappingURL=src_Style_MultiDeviceSection_css_f9ee138c._.single.css.map*/