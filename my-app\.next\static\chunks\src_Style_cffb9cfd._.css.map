{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/StartFlowingSection.css"], "sourcesContent": ["/* Start Flowing Section */\n.start-flowing-section {\n  position: relative;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 50%, #fdba74 100%);\n  overflow: hidden;\n}\n\n.start-flowing-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 40px;\n  width: 100%;\n}\n\n.content-wrapper {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n  min-height: 80vh;\n}\n\n.hero-content {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.main-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  color: #92400e;\n  margin: 0;\n  font-family: 'Georgia', serif;\n  line-height: 1.1;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.highlight-text {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 500;\n}\n\n.subtitle {\n  font-size: 1.25rem;\n  color: #78350f;\n  line-height: 1.6;\n  margin: 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.cta-section {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n}\n\n.primary-cta {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n}\n\n.primary-cta:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);\n}\n\n.secondary-cta {\n  background: transparent;\n  color: #92400e;\n  border: 2px solid #d97706;\n  padding: 14px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.secondary-cta:hover {\n  background: #d97706;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.trust-indicators {\n  display: flex;\n  gap: 40px;\n  align-items: center;\n}\n\n.trust-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.trust-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #92400e;\n  line-height: 1;\n  margin-bottom: 4px;\n}\n\n.trust-label {\n  font-size: 0.875rem;\n  color: #78350f;\n  font-weight: 500;\n}\n\n.hero-visual {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.demo-window {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  width: 100%;\n  max-width: 500px;\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n.window-header {\n  background: #f3f4f6;\n  padding: 16px 20px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.window-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.control {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.control.red { background: #ef4444; }\n.control.yellow { background: #f59e0b; }\n.control.green { background: #10b981; }\n\n.window-title {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.window-content {\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 32px;\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);\n}\n\n.voice-indicator {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 80px;\n  height: 80px;\n  border: 3px solid #f59e0b;\n  border-radius: 50%;\n  animation: pulse 2s ease-in-out infinite;\n}\n\n.microphone-icon {\n  font-size: 2rem;\n  z-index: 1;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.5);\n    opacity: 0;\n  }\n}\n\n.typing-demo {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  width: 100%;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.demo-text {\n  font-size: 1rem;\n  color: #374151;\n  line-height: 1.6;\n  margin: 0;\n  font-style: italic;\n}\n\n.cursor-blink {\n  display: inline-block;\n  width: 2px;\n  height: 20px;\n  background: #f59e0b;\n  animation: blink 1s infinite;\n  margin-left: 4px;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0; }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .content-wrapper {\n    gap: 60px;\n  }\n\n  .main-title {\n    font-size: 3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .start-flowing-section {\n    min-height: auto;\n    padding: 80px 0;\n  }\n\n  .start-flowing-container {\n    padding: 0 20px;\n  }\n\n  .content-wrapper {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .main-title {\n    font-size: 2.5rem;\n  }\n\n  .subtitle {\n    font-size: 1.125rem;\n  }\n\n  .cta-section {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .trust-indicators {\n    justify-content: center;\n    gap: 24px;\n  }\n\n  .demo-window {\n    max-width: 400px;\n  }\n\n  .window-content {\n    padding: 32px 24px;\n  }\n}\n\n@media (max-width: 480px) {\n  .start-flowing-container {\n    padding: 0 15px;\n  }\n\n  .main-title {\n    font-size: 2rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n  }\n\n  .cta-section {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .primary-cta,\n  .secondary-cta {\n    width: 100%;\n    max-width: 280px;\n  }\n\n  .trust-indicators {\n    gap: 16px;\n  }\n\n  .trust-number {\n    font-size: 1.5rem;\n  }\n\n  .demo-window {\n    max-width: 320px;\n  }\n\n  .window-content {\n    padding: 24px 16px;\n    gap: 24px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;;AAMA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/HomeSection.css"], "sourcesContent": ["/* Home Section */\n.home-section {\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 50%, #fed7aa 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.home-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  padding: 0 40px;\n}\n\n.content-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.text-content {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.cta-section {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 16px;\n  margin-top: 32px;\n}\n\n.primary-cta-btn {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);\n  position: relative;\n  overflow: hidden;\n}\n\n.primary-cta-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.primary-cta-btn:hover::before {\n  left: 100%;\n}\n\n.primary-cta-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);\n}\n\n.cta-note {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n  font-style: italic;\n}\n\n.section-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  color: black;\n  line-height: 1.1;\n  margin: 0;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.gradient-text {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 500;\n}\n\n.section-description {\n  font-size: 1.125rem;\n  color: #4b5563;\n  line-height: 1.6;\n  margin: 0;\n  max-width: 500px;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.feature-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 1rem;\n  color: #374151;\n}\n\n.feature-icon {\n  font-size: 1.25rem;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.visual-content {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n}\n\n.stat-card {\n  background: white;\n  padding: 24px;\n  border-radius: 16px;\n  text-align: center;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  border: 1px solid rgba(245, 158, 11, 0.1);\n}\n\n.stat-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n\n.stat-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 8px;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.demo-card {\n  background: white;\n  border-radius: 20px;\n  padding: 24px;\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(245, 158, 11, 0.1);\n}\n\n.demo-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.demo-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.demo-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.875rem;\n  color: #10b981;\n  font-weight: 500;\n}\n\n.status-dot {\n  width: 8px;\n  height: 8px;\n  background: #10b981;\n  border-radius: 50%;\n  animation: pulse 2s ease-in-out infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { \n    opacity: 1;\n    transform: scale(1);\n  }\n  50% { \n    opacity: 0.7;\n    transform: scale(1.1);\n  }\n}\n\n.demo-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.waveform {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 4px;\n  height: 60px;\n}\n\n.wave-bar {\n  width: 4px;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  border-radius: 2px;\n  animation: wave 1.5s ease-in-out infinite;\n}\n\n.wave-bar:nth-child(1) { height: 20px; animation-delay: 0s; }\n.wave-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }\n.wave-bar:nth-child(3) { height: 50px; animation-delay: 0.2s; }\n.wave-bar:nth-child(4) { height: 30px; animation-delay: 0.3s; }\n.wave-bar:nth-child(5) { height: 45px; animation-delay: 0.4s; }\n.wave-bar:nth-child(6) { height: 25px; animation-delay: 0.5s; }\n.wave-bar:nth-child(7) { height: 40px; animation-delay: 0.6s; }\n.wave-bar:nth-child(8) { height: 20px; animation-delay: 0.7s; }\n\n@keyframes wave {\n  0%, 100% { \n    transform: scaleY(0.3);\n    opacity: 0.7;\n  }\n  50% { \n    transform: scaleY(1);\n    opacity: 1;\n  }\n}\n\n.demo-text {\n  font-size: 0.95rem;\n  color: #6b7280;\n  font-style: italic;\n  text-align: center;\n  padding: 16px;\n  background: #f9fafb;\n  border-radius: 12px;\n  border-left: 4px solid #f59e0b;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .home-section {\n    padding: 100px 20px;\n  }\n  \n  .content-grid {\n    gap: 60px;\n  }\n  \n  .section-title {\n    font-size: 3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .home-section {\n    padding: 80px 20px;\n    min-height: auto;\n  }\n  \n  .content-grid {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n  \n  .section-title {\n    font-size: 2.5rem;\n  }\n  \n  .section-description {\n    font-size: 1rem;\n    max-width: none;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n  \n  .feature-list {\n    align-items: center;\n  }\n  \n  .feature-item {\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .home-section {\n    padding: 60px 15px;\n  }\n  \n  .section-title {\n    font-size: 2rem;\n  }\n  \n  .section-description {\n    font-size: 0.95rem;\n  }\n  \n  .demo-card {\n    padding: 20px;\n  }\n  \n  .stat-card {\n    padding: 20px;\n  }\n  \n  .stat-number {\n    font-size: 1.75rem;\n  }\n  \n  .waveform {\n    height: 50px;\n  }\n  \n  .wave-bar {\n    width: 3px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;;;AAWA;;;;;;;;;;;AAYA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAQA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/Marquee.css"], "sourcesContent": ["/* Marquee Container */\r\n.marquee-container {\r\n  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\r\n  color: white;\r\n  width: 100%;\r\n  min-height: 120px;\r\n  padding: 30px 0;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.marquee-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(90deg,\r\n    rgba(0,0,0,0.15) 0%,\r\n    transparent 15%,\r\n    transparent 85%,\r\n    rgba(0,0,0,0.15) 100%);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n.marquee-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: nowrap;\r\n  will-change: transform;\r\n}\r\n\r\n/* Marquee Items */\r\n.marquee-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 24px;\r\n  flex-shrink: 0;\r\n  border-radius: 50px;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  min-width: fit-content;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.marquee-content {\r\n  padding: 12px 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: white;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  white-space: nowrap;\r\n  letter-spacing: 0.5px;\r\n  user-select: none;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .marquee-content {\r\n    font-size: 15px;\r\n    padding: 11px 18px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .marquee-container {\r\n    min-height: 80px;\r\n    padding: 15px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 14px;\r\n    padding: 10px 16px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 15px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .marquee-container {\r\n    min-height: 70px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 13px;\r\n    padding: 8px 14px;\r\n    letter-spacing: 0.3px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 320px) {\r\n  .marquee-container {\r\n    min-height: 60px;\r\n    padding: 10px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 10px;\r\n  }\r\n}\r\n\r\n/* Legacy styles for backward compatibility */\r\n.MarqueeBox {\r\n  font-family: system-ui;\r\n  background: #111;\r\n  color: white;\r\n  text-align: center;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 20vh;\r\n}\r\n\r\n.carousel {\r\n  background: blue;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: green;\r\n  margin: 0;\r\n  padding: 0;\r\n  position: relative;\r\n  color: black;\r\n  font-size: 121px;\r\n  cursor: pointer;\r\n}\r\n\r\n.test {\r\n  padding: 20px;\r\n}\r\n\r\n.test-2 {\r\n  padding: 20px 10px;\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;AAcA;;;;;;;;;AAgBA;;;;;;;AAQA;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;AAYA;EACE;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;;EAMA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAMF;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;AAIA", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/FlowSection.css"], "sourcesContent": ["/* Flow Section Styles */\n.flow-section {\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n  color: white;\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.flow-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),\n              radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.flow-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  padding: 0 40px;\n}\n\n.flow-content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 60px;\n  align-items: center;\n  max-width: 100%;\n}\n\n.flow-left {\n  display: flex;\n  flex-direction: column;\n  gap: 40px;\n}\n\n.flow-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  line-height: 1.1;\n  margin: 0;\n  font-family: 'Georgia', serif;\n  max-width: 100%;\n  word-wrap: break-word;\n  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n  position: relative;\n  z-index: 1;\n}\n\n.profession-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n  max-width: 100%;\n  width: 100%;\n}\n\n.profession-tag {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 25px;\n  padding: 8px 16px;\n  font-size: 14px;\n  font-weight: 500;\n  color: white;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  backdrop-filter: blur(10px);\n}\n\n.profession-tag:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.4);\n  transform: translateY(-2px);\n}\n\n.flow-right {\n  display: flex;\n  flex-direction: column;\n  gap: 40px;\n  align-items: flex-end;\n}\n\n.flow-accessibility {\n  max-width: 400px;\n  text-align: left;\n}\n\n.accessibility-title {\n  font-size: 2rem;\n  font-weight: 600;\n  margin-bottom: 16px;\n  color: white;\n}\n\n.accessibility-description {\n  font-size: 1.1rem;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 24px;\n}\n\n.get-started-btn {\n  background: #6366f1;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.get-started-btn:hover {\n  background: #5855eb;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);\n}\n\n/* Character Illustration */\n.flow-illustration {\n  position: relative;\n  width: 300px;\n  height: 300px;\n}\n\n.character-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.character {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.character-head {\n  width: 80px;\n  height: 80px;\n  background: #ff9ff3;\n  border-radius: 50%;\n  position: relative;\n  margin: 0 auto 10px;\n}\n\n.character-face {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.eye {\n  width: 8px;\n  height: 8px;\n  background: #2a2a2a;\n  border-radius: 50%;\n  position: absolute;\n}\n\n.left-eye {\n  left: -12px;\n  top: -2px;\n}\n\n.right-eye {\n  right: -12px;\n  top: -2px;\n}\n\n.mouth {\n  width: 12px;\n  height: 6px;\n  border: 2px solid #2a2a2a;\n  border-top: none;\n  border-radius: 0 0 12px 12px;\n  position: absolute;\n  left: -6px;\n  top: 8px;\n}\n\n.character-ears {\n  position: absolute;\n  top: 10px;\n  width: 100%;\n}\n\n.ear {\n  width: 20px;\n  height: 25px;\n  background: #ff9ff3;\n  border-radius: 50%;\n  position: absolute;\n}\n\n.left-ear {\n  left: -10px;\n  transform: rotate(-20deg);\n}\n\n.right-ear {\n  right: -10px;\n  transform: rotate(20deg);\n}\n\n.character-body {\n  width: 60px;\n  height: 80px;\n  background: #ff9ff3;\n  border-radius: 30px;\n  margin: 0 auto;\n  position: relative;\n}\n\n.character-arms {\n  position: absolute;\n  top: 20px;\n  width: 100%;\n}\n\n.arm {\n  width: 30px;\n  height: 8px;\n  background: #ff9ff3;\n  border-radius: 4px;\n  position: absolute;\n}\n\n.left-arm {\n  left: -25px;\n  transform: rotate(-30deg);\n}\n\n.right-arm {\n  right: -25px;\n  transform: rotate(30deg);\n}\n\n.speech-bubble {\n  position: absolute;\n  top: -20px;\n  right: -80px;\n  width: 120px;\n  height: 80px;\n  background: white;\n  border-radius: 15px;\n  padding: 15px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.speech-bubble::before {\n  content: '';\n  position: absolute;\n  left: -10px;\n  top: 30px;\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 10px 10px 10px 0;\n  border-color: transparent white transparent transparent;\n}\n\n.bubble-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.text-lines {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.text-line {\n  height: 3px;\n  background: #ff9500;\n  border-radius: 2px;\n}\n\n.text-line.short {\n  width: 60%;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .flow-content {\n    gap: 60px;\n  }\n  \n  .flow-title {\n    font-size: 3.5rem;\n  }\n  \n  .flow-illustration {\n    width: 250px;\n    height: 250px;\n  }\n}\n\n@media (max-width: 768px) {\n  .flow-section {\n    padding: 80px 20px;\n  }\n  \n  .flow-content {\n    grid-template-columns: 1fr;\n    gap: 60px;\n    text-align: center;\n  }\n  \n  .flow-title {\n    font-size: 3rem;\n  }\n  \n  .flow-right {\n    align-items: center;\n  }\n  \n  .flow-accessibility {\n    text-align: center;\n  }\n  \n  .profession-tags {\n    justify-content: center;\n  }\n  \n  .flow-illustration {\n    width: 200px;\n    height: 200px;\n  }\n  \n  .speech-bubble {\n    right: -60px;\n    width: 100px;\n    height: 60px;\n    padding: 10px;\n  }\n}\n\n@media (max-width: 480px) {\n  .flow-section {\n    padding: 60px 15px;\n  }\n  \n  .flow-title {\n    font-size: 2.5rem;\n  }\n  \n  .accessibility-title {\n    font-size: 1.5rem;\n  }\n  \n  .accessibility-description {\n    font-size: 1rem;\n  }\n  \n  .profession-tag {\n    font-size: 12px;\n    padding: 6px 12px;\n  }\n  \n  .flow-illustration {\n    width: 180px;\n    height: 180px;\n  }\n  \n  .character-head {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .character-body {\n    width: 45px;\n    height: 60px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAWA;;;;;;;;AAYA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;;;AAQF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/ContentImageSection.css"], "sourcesContent": ["/* Content Image Section Styles */\n.content-image-section {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n}\n\n.content-image-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.content-side {\n  display: flex;\n  flex-direction: column;\n  gap: 40px;\n}\n\n.content-header {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.content-badge {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 14px;\n  font-weight: 600;\n  width: fit-content;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.content-title {\n  font-size: 3.5rem;\n  font-weight: 700;\n  line-height: 1.1;\n  color: #1a202c;\n  margin: 0;\n}\n\n.highlight {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.content-description {\n  font-size: 1.2rem;\n  line-height: 1.6;\n  color: #4a5568;\n  margin: 0;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 24px;\n}\n\n.feature-item {\n  display: flex;\n  gap: 16px;\n  align-items: flex-start;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n}\n\n.feature-item:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  font-size: 2rem;\n  width: 50px;\n  height: 50px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  flex-shrink: 0;\n}\n\n.feature-content {\n  flex: 1;\n}\n\n.feature-title {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0 0 8px 0;\n}\n\n.feature-description {\n  font-size: 1rem;\n  line-height: 1.5;\n  color: #4a5568;\n  margin: 0;\n}\n\n.content-actions {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.primary-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 14px 28px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n}\n\n.primary-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n}\n\n.secondary-btn {\n  background: transparent;\n  color: #667eea;\n  border: 2px solid #667eea;\n  padding: 12px 26px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.secondary-btn:hover {\n  background: #667eea;\n  color: white;\n  transform: translateY(-2px);\n}\n\n/* Image Side Styles */\n.image-side {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.image-container {\n  position: relative;\n  width: 100%;\n  max-width: 500px;\n}\n\n.main-device {\n  background: #1a202c;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);\n  position: relative;\n  z-index: 2;\n}\n\n.device-screen {\n  background: #2d3748;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.screen-header {\n  background: #4a5568;\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.screen-dots {\n  display: flex;\n  gap: 6px;\n}\n\n.dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.dot.red { background: #f56565; }\n.dot.yellow { background: #ed8936; }\n.dot.green { background: #48bb78; }\n\n.screen-title {\n  color: #e2e8f0;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.screen-content {\n  padding: 30px 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n  align-items: center;\n}\n\n.voice-wave {\n  display: flex;\n  gap: 4px;\n  align-items: center;\n  justify-content: center;\n}\n\n.wave-bar {\n  width: 4px;\n  background: #667eea;\n  border-radius: 2px;\n  animation: wave 1.5s ease-in-out infinite;\n}\n\n.wave-bar:nth-child(1) { height: 20px; animation-delay: 0s; }\n.wave-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }\n.wave-bar:nth-child(3) { height: 25px; animation-delay: 0.2s; }\n.wave-bar:nth-child(4) { height: 40px; animation-delay: 0.3s; }\n.wave-bar:nth-child(5) { height: 30px; animation-delay: 0.4s; }\n.wave-bar:nth-child(6) { height: 35px; animation-delay: 0.5s; }\n.wave-bar:nth-child(7) { height: 20px; animation-delay: 0.6s; }\n\n@keyframes wave {\n  0%, 100% { transform: scaleY(1); }\n  50% { transform: scaleY(0.3); }\n}\n\n.text-output {\n  background: #4a5568;\n  border-radius: 8px;\n  padding: 16px;\n  width: 100%;\n  min-height: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.typing-text {\n  color: #e2e8f0;\n  font-size: 14px;\n  font-family: 'Courier New', monospace;\n}\n\n.cursor {\n  animation: blink 1s infinite;\n  color: #667eea;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0; }\n}\n\n.floating-elements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.floating-card {\n  position: absolute;\n  background: white;\n  border-radius: 12px;\n  padding: 12px 16px;\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #1a202c;\n  animation: float 3s ease-in-out infinite;\n}\n\n.card-1 {\n  top: 20%;\n  left: -10%;\n  animation-delay: 0s;\n}\n\n.card-2 {\n  top: 60%;\n  right: -15%;\n  animation-delay: 1s;\n}\n\n.card-3 {\n  bottom: 20%;\n  left: -5%;\n  animation-delay: 2s;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.card-icon {\n  font-size: 18px;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .content-image-container {\n    gap: 60px;\n  }\n  \n  .content-title {\n    font-size: 3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .content-image-section {\n    padding: 80px 20px;\n  }\n  \n  .content-image-container {\n    grid-template-columns: 1fr;\n    gap: 60px;\n  }\n  \n  .content-title {\n    font-size: 2.5rem;\n    text-align: center;\n  }\n  \n  .content-header {\n    text-align: center;\n  }\n  \n  .content-badge {\n    align-self: center;\n  }\n  \n  .content-actions {\n    justify-content: center;\n  }\n  \n  .floating-card {\n    display: none;\n  }\n}\n\n@media (max-width: 480px) {\n  .content-image-section {\n    padding: 60px 15px;\n  }\n  \n  .content-title {\n    font-size: 2rem;\n  }\n  \n  .content-description {\n    font-size: 1rem;\n  }\n  \n  .feature-item {\n    padding: 16px;\n  }\n  \n  .feature-icon {\n    width: 40px;\n    height: 40px;\n    font-size: 1.5rem;\n  }\n  \n  .main-device {\n    padding: 15px;\n  }\n  \n  .screen-content {\n    padding: 20px 15px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;AAKA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAKA;;;;AAKA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1750, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/AIAutoEditsSection.css"], "sourcesContent": ["/* AI Auto Edits Section */\n.ai-auto-edits-section {\n  background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 50%, #fdba74 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.ai-auto-edits-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),\n              radial-gradient(circle at 30% 70%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.ai-auto-edits-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n  padding: 0 40px;\n}\n\n/* Phone Mockup */\n.phone-mockup-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.phone-device {\n  width: 320px;\n  height: 640px;\n  background: linear-gradient(145deg, #1f2937, #374151);\n  border-radius: 30px;\n  padding: 8px;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);\n  position: relative;\n}\n\n.phone-device::before {\n  content: '';\n  position: absolute;\n  top: 15px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60px;\n  height: 4px;\n  background: #6b7280;\n  border-radius: 2px;\n}\n\n.phone-screen {\n  width: 100%;\n  height: 100%;\n  background: #111827;\n  border-radius: 22px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.screen-header {\n  background: #1f2937;\n  padding: 12px 16px 8px;\n}\n\n.status-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: white;\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.status-icons {\n  display: flex;\n  gap: 4px;\n}\n\n.app-header h3 {\n  color: white;\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0;\n  text-align: center;\n}\n\n.screen-content {\n  flex: 1;\n  padding: 20px 16px;\n  overflow-y: auto;\n}\n\n.text-editor {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  height: 100%;\n}\n\n.original-text {\n  background: #374151;\n  border-radius: 12px;\n  padding: 16px;\n  border-left: 4px solid #f59e0b;\n}\n\n.original-text p {\n  color: #e5e7eb;\n  font-size: 14px;\n  line-height: 1.5;\n  margin: 0;\n}\n\n.ai-suggestions {\n  background: #1f2937;\n  border-radius: 12px;\n  padding: 16px;\n  border: 1px solid #374151;\n}\n\n.suggestion-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #60a5fa;\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 12px;\n}\n\n.suggestion-items {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-bottom: 16px;\n}\n\n.suggestion-item {\n  background: #374151;\n  color: #d1d5db;\n  padding: 10px 12px;\n  border-radius: 8px;\n  font-size: 13px;\n  transition: all 0.3s ease;\n  opacity: 0.6;\n}\n\n.suggestion-item.active {\n  background: #3b82f6;\n  color: white;\n  opacity: 1;\n  transform: scale(1.02);\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.apply-btn, .preview-btn {\n  flex: 1;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 600;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.apply-btn {\n  background: #10b981;\n  color: white;\n}\n\n.apply-btn:hover {\n  background: #059669;\n}\n\n.preview-btn {\n  background: #374151;\n  color: #d1d5db;\n}\n\n.preview-btn:hover {\n  background: #4b5563;\n}\n\n.improved-text {\n  background: #065f46;\n  border-radius: 12px;\n  padding: 16px;\n  border-left: 4px solid #10b981;\n}\n\n.improved-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #6ee7b7;\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 12px;\n}\n\n.typing-text {\n  color: #d1fae5;\n  font-size: 14px;\n  line-height: 1.5;\n  margin: 0;\n  animation: typewriter 4s steps(40) infinite;\n}\n\n@keyframes typewriter {\n  0% { width: 0; }\n  50% { width: 100%; }\n  100% { width: 100%; }\n}\n\n/* Content Section */\n.content-section {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.content-badge {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  background: rgba(251, 191, 36, 0.2);\n  color: #92400e;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 14px;\n  font-weight: 600;\n  width: fit-content;\n  border: 1px solid rgba(251, 191, 36, 0.3);\n}\n\n.sparkle {\n  animation: sparkle 2s ease-in-out infinite;\n}\n\n@keyframes sparkle {\n  0%, 100% { transform: scale(1) rotate(0deg); }\n  50% { transform: scale(1.2) rotate(180deg); }\n}\n\n.section-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  color: #92400e;\n  margin: 0;\n  line-height: 1.1;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.section-description {\n  font-size: 1.2rem;\n  line-height: 1.6;\n  color: #78350f;\n  margin: 0;\n  max-width: 500px;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.features-list {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 16px;\n  margin: 20px 0;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  background: rgba(255, 255, 255, 0.5);\n  padding: 12px 16px;\n  border-radius: 12px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #92400e;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n.feature-icon {\n  font-size: 18px;\n}\n\n.cta-button {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  width: fit-content;\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);\n}\n\n.cta-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);\n}\n\n.arrow {\n  transition: transform 0.3s ease;\n}\n\n.cta-button:hover .arrow {\n  transform: translateX(4px);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .ai-auto-edits-container {\n    gap: 60px;\n  }\n  \n  .section-title {\n    font-size: 3rem;\n  }\n  \n  .phone-device {\n    width: 280px;\n    height: 560px;\n  }\n}\n\n@media (max-width: 768px) {\n  .ai-auto-edits-section {\n    padding: 80px 20px;\n  }\n  \n  .ai-auto-edits-container {\n    grid-template-columns: 1fr;\n    gap: 60px;\n    text-align: center;\n  }\n  \n  .section-title {\n    font-size: 2.5rem;\n  }\n  \n  .features-list {\n    grid-template-columns: 1fr;\n  }\n  \n  .phone-device {\n    width: 260px;\n    height: 520px;\n  }\n}\n\n@media (max-width: 480px) {\n  .ai-auto-edits-section {\n    padding: 60px 15px;\n  }\n  \n  .section-title {\n    font-size: 2rem;\n  }\n  \n  .section-description {\n    font-size: 1rem;\n  }\n  \n  .phone-device {\n    width: 240px;\n    height: 480px;\n  }\n  \n  .screen-content {\n    padding: 16px 12px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;;AAYA;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;;;;;;;;;;;;AAiBA;;;;;AAKA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 2159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/MultiDeviceSection.css"], "sourcesContent": ["/* Multi Device Section */\n.multi-device-section {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #fed7aa 100%);\n  padding: 120px 20px;\n  min-height: auto;\n  position: relative;\n  overflow: hidden;\n}\n\n.multi-device-section::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 20% 30%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),\n              radial-gradient(circle at 80% 70%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.multi-device-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 120px;\n  padding: 0 40px;\n}\n\n.device-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.device-row.reverse {\n  grid-template-columns: 1fr 1fr;\n}\n\n.device-row.reverse .device-mockup {\n  order: 2;\n}\n\n.device-row.reverse .device-content {\n  order: 1;\n}\n\n.device-mockup {\n  background: #1f2937;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n.device-screen {\n  background: #111827;\n  border-radius: 12px;\n  overflow: hidden;\n  min-height: 300px;\n}\n\n.screen-header {\n  background: #374151;\n  padding: 16px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: white;\n}\n\n.screen-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.close-btn {\n  font-size: 20px;\n  cursor: pointer;\n  opacity: 0.7;\n}\n\n.device-content {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  max-width: 500px;\n}\n\n.device-content h3 {\n  font-size: 2.5rem;\n  font-weight: 400;\n  color: #92400e;\n  margin: 0;\n  line-height: 1.2;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.device-content p {\n  font-size: 1.1rem;\n  line-height: 1.6;\n  color: #78350f;\n  margin: 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n/* Dictionary Device */\n.dictionary-content {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.dict-item {\n  background: #374151;\n  color: #60a5fa;\n  padding: 12px 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  border-left: 3px solid #3b82f6;\n  transition: all 0.3s ease;\n}\n\n.dict-item:hover {\n  background: #4b5563;\n  transform: translateX(4px);\n}\n\n/* Tones Device */\n.app-selector {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  padding: 20px;\n  background: #374151;\n}\n\n.app-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 12px;\n  background: #4b5563;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.app-icon.active {\n  background: #3b82f6;\n  transform: scale(1.1);\n}\n\n.tones-content {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.tone-option {\n  background: #374151;\n  color: #d1d5db;\n  padding: 12px 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tone-option.active {\n  background: #10b981;\n  color: white;\n  transform: scale(1.02);\n}\n\n/* Languages Device */\n.language-wheel {\n  position: relative;\n  width: 200px;\n  height: 200px;\n  margin: 40px auto;\n}\n\n.language-item {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 40px;\n  height: 40px;\n  background: #3b82f6;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: 600;\n  transform-origin: center;\n  animation: rotate 20s linear infinite;\n}\n\n@keyframes rotate {\n  from { transform: rotate(0deg) translateY(-80px) rotate(0deg); }\n  to { transform: rotate(360deg) translateY(-80px) rotate(-360deg); }\n}\n\n.wheel-center {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80px;\n  height: 80px;\n  background: #1f2937;\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid #3b82f6;\n}\n\n.lang-count {\n  color: #60a5fa;\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.lang-text {\n  color: #9ca3af;\n  font-size: 10px;\n  font-weight: 500;\n}\n\n/* Desktop Device */\n.desktop-device {\n  max-width: 500px;\n}\n\n.desktop-screen {\n  background: #f3f4f6;\n  border-radius: 12px;\n  overflow: hidden;\n  min-height: 400px;\n  position: relative;\n}\n\n.desktop-header {\n  background: #e5e7eb;\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  border-bottom: 1px solid #d1d5db;\n}\n\n.window-controls {\n  display: flex;\n  gap: 6px;\n}\n\n.control {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.control.red { background: #ef4444; }\n.control.yellow { background: #f59e0b; }\n.control.green { background: #10b981; }\n\n.window-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n}\n\n.desktop-content {\n  display: flex;\n  height: 300px;\n}\n\n.sidebar {\n  width: 120px;\n  background: #f9fafb;\n  border-right: 1px solid #e5e7eb;\n  padding: 16px 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.sidebar-item {\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.sidebar-item.active {\n  background: #3b82f6;\n  color: white;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  background: white;\n}\n\n.document-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.document-header h4 {\n  margin: 0;\n  font-size: 16px;\n  color: #1f2937;\n}\n\n.doc-status {\n  font-size: 12px;\n  color: #10b981;\n  background: #dcfce7;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n.document-body {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.text-line {\n  height: 4px;\n  background: #d1d5db;\n  border-radius: 2px;\n}\n\n.text-line.short { width: 60%; }\n.text-line.medium { width: 80%; }\n\n.mobile-preview {\n  position: absolute;\n  bottom: 20px;\n  right: 20px;\n  width: 80px;\n  height: 120px;\n  background: #1f2937;\n  border-radius: 12px;\n  padding: 8px;\n}\n\n.mobile-screen {\n  background: #111827;\n  border-radius: 8px;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n}\n\n.mobile-header {\n  color: white;\n  font-size: 8px;\n  font-weight: 600;\n}\n\n.voice-indicator {\n  display: flex;\n  gap: 2px;\n  align-items: center;\n}\n\n.voice-wave {\n  width: 2px;\n  background: #3b82f6;\n  border-radius: 1px;\n  animation: wave 1s ease-in-out infinite;\n}\n\n.voice-wave:nth-child(1) { height: 8px; animation-delay: 0s; }\n.voice-wave:nth-child(2) { height: 12px; animation-delay: 0.1s; }\n.voice-wave:nth-child(3) { height: 6px; animation-delay: 0.2s; }\n\n@keyframes wave {\n  0%, 100% { transform: scaleY(1); }\n  50% { transform: scaleY(0.3); }\n}\n\n.cta-button {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: fit-content;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n}\n\n.cta-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .multi-device-container {\n    gap: 100px;\n  }\n\n  .device-row {\n    gap: 60px;\n  }\n\n  .device-content h3 {\n    font-size: 2rem;\n  }\n\n  .device-mockup {\n    max-width: 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .multi-device-section {\n    padding: 80px 20px;\n  }\n\n  .multi-device-container {\n    gap: 80px;\n  }\n\n  .device-row {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .device-row.reverse .device-mockup,\n  .device-row.reverse .device-content {\n    order: unset;\n  }\n\n  .device-content {\n    max-width: none;\n    align-items: center;\n  }\n\n  .device-content h3 {\n    font-size: 1.8rem;\n  }\n\n  .device-mockup {\n    max-width: 320px;\n  }\n\n  .language-wheel {\n    width: 160px;\n    height: 160px;\n  }\n\n  .language-item {\n    width: 32px;\n    height: 32px;\n    font-size: 10px;\n  }\n\n  .wheel-center {\n    width: 60px;\n    height: 60px;\n  }\n\n  .lang-count {\n    font-size: 14px;\n  }\n\n  .lang-text {\n    font-size: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .multi-device-section {\n    padding: 60px 15px;\n  }\n\n  .multi-device-container {\n    gap: 60px;\n  }\n\n  .device-content h3 {\n    font-size: 1.5rem;\n  }\n\n  .device-content p {\n    font-size: 1rem;\n  }\n\n  .device-mockup {\n    max-width: 280px;\n    padding: 15px;\n  }\n\n  .device-screen {\n    min-height: 250px;\n  }\n\n  .desktop-content {\n    height: 250px;\n  }\n\n  .sidebar {\n    width: 100px;\n    padding: 12px 6px;\n  }\n\n  .sidebar-item {\n    font-size: 10px;\n    padding: 6px 8px;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .mobile-preview {\n    width: 60px;\n    height: 90px;\n    bottom: 15px;\n    right: 15px;\n  }\n\n  .language-wheel {\n    width: 140px;\n    height: 140px;\n  }\n\n  .language-item {\n    width: 28px;\n    height: 28px;\n    font-size: 9px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;AAYA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;AACA;;;;AAEA;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;;;EAOA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 2756, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/CrossPlatformSection.css"], "sourcesContent": ["/* Cross Platform Section */\n.cross-platform-section {\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n}\n\n.cross-platform-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n  padding: 0 40px;\n}\n\n.content-section {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.platform-badges {\n  display: flex;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.platform-badge {\n  background: rgba(255, 255, 255, 0.8);\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n  border: 1px solid rgba(245, 158, 11, 0.2);\n  backdrop-filter: blur(10px);\n}\n\n.section-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  color: #1f2937;\n  line-height: 1.1;\n  margin: 0;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  letter-spacing: -0.02em;\n}\n\n.section-description {\n  font-size: 1.125rem;\n  color: #374151;\n  line-height: 1.6;\n  margin: 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n  font-weight: 400;\n}\n\n.get-started-btn {\n  background: transparent;\n  color: #1f2937;\n  border: 2px solid #d1d5db;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: fit-content;\n}\n\n.get-started-btn:hover {\n  border-color: #f59e0b;\n  color: #f59e0b;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);\n}\n\n.devices-section {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.device-stack {\n  position: relative;\n  width: 100%;\n  max-width: 500px;\n  height: 600px;\n}\n\n/* Desktop Mockup */\n.desktop-mockup {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 400px;\n  height: 280px;\n  background: #1f2937;\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  z-index: 3;\n}\n\n.desktop-header {\n  background: #374151;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n}\n\n.window-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.control {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.control.red { background: #ef4444; }\n.control.yellow { background: #f59e0b; }\n.control.green { background: #10b981; }\n\n.desktop-content {\n  display: flex;\n  height: calc(100% - 32px);\n}\n\n.sidebar {\n  width: 120px;\n  background: #2d3748;\n  padding: 16px 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.sidebar-item {\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 0.75rem;\n  color: #d1d5db;\n  cursor: pointer;\n  transition: background 0.2s;\n}\n\n.sidebar-item.active {\n  background: #f59e0b;\n  color: white;\n}\n\n.main-area {\n  flex: 1;\n  padding: 16px;\n  background: #1f2937;\n  position: relative;\n}\n\n.document-title {\n  color: #f59e0b;\n  font-size: 0.875rem;\n  font-weight: 600;\n  margin-bottom: 16px;\n}\n\n.document-section h4 {\n  color: #e5e7eb;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n}\n\n.idea-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 4px;\n  font-size: 0.7rem;\n  color: #d1d5db;\n}\n\n.bullet {\n  color: #f59e0b;\n}\n\n.voice-indicator-desktop {\n  position: absolute;\n  bottom: 16px;\n  right: 16px;\n  background: rgba(245, 158, 11, 0.2);\n  border-radius: 20px;\n  padding: 8px 12px;\n}\n\n.voice-waves {\n  display: flex;\n  gap: 2px;\n  align-items: center;\n}\n\n.wave {\n  width: 2px;\n  height: 12px;\n  background: #f59e0b;\n  border-radius: 1px;\n  animation: wave 1s ease-in-out infinite;\n}\n\n.wave:nth-child(1) { animation-delay: 0s; }\n.wave:nth-child(2) { animation-delay: 0.2s; }\n.wave:nth-child(3) { animation-delay: 0.4s; }\n.wave:nth-child(4) { animation-delay: 0.6s; }\n\n/* Mobile Mockup */\n.mobile-mockup {\n  position: absolute;\n  top: 200px;\n  right: 50px;\n  width: 180px;\n  height: 320px;\n  background: #1f2937;\n  border-radius: 20px;\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  z-index: 2;\n}\n\n.mobile-header {\n  background: #374151;\n  padding: 12px 16px 8px;\n}\n\n.status-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.7rem;\n  color: #e5e7eb;\n  margin-bottom: 8px;\n}\n\n.app-title {\n  color: #f59e0b;\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-align: center;\n}\n\n.mobile-content {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24px;\n}\n\n.mobile-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #e5e7eb;\n  font-size: 0.875rem;\n}\n\n.recording-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n}\n\n.recording-circle {\n  width: 60px;\n  height: 60px;\n  border: 2px solid #f59e0b;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.record-button {\n  width: 20px;\n  height: 20px;\n  background: #ef4444;\n  border-radius: 50%;\n}\n\n.recording-waves {\n  display: flex;\n  gap: 3px;\n  align-items: center;\n}\n\n.mobile-wave {\n  width: 3px;\n  height: 20px;\n  background: #3b82f6;\n  border-radius: 2px;\n  animation: wave 1.2s ease-in-out infinite;\n}\n\n.mobile-wave:nth-child(1) { animation-delay: 0s; }\n.mobile-wave:nth-child(2) { animation-delay: 0.15s; }\n.mobile-wave:nth-child(3) { animation-delay: 0.3s; }\n.mobile-wave:nth-child(4) { animation-delay: 0.45s; }\n.mobile-wave:nth-child(5) { animation-delay: 0.6s; }\n\n/* Voice Widget */\n.voice-widget {\n  position: absolute;\n  bottom: 50px;\n  left: 80px;\n  width: 120px;\n  height: 80px;\n  background: #1f2937;\n  border-radius: 16px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\n  padding: 12px;\n  z-index: 1;\n}\n\n.widget-header {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 8px;\n}\n\n.widget-icon {\n  font-size: 1.2rem;\n}\n\n.widget-waves {\n  display: flex;\n  gap: 2px;\n  align-items: center;\n  justify-content: center;\n}\n\n.widget-wave {\n  width: 2px;\n  height: 16px;\n  background: #10b981;\n  border-radius: 1px;\n  animation: wave 0.8s ease-in-out infinite;\n}\n\n.widget-wave:nth-child(1) { animation-delay: 0s; }\n.widget-wave:nth-child(2) { animation-delay: 0.1s; }\n.widget-wave:nth-child(3) { animation-delay: 0.2s; }\n.widget-wave:nth-child(4) { animation-delay: 0.3s; }\n.widget-wave:nth-child(5) { animation-delay: 0.4s; }\n.widget-wave:nth-child(6) { animation-delay: 0.5s; }\n\n@keyframes wave {\n  0%, 100% { \n    transform: scaleY(0.3);\n    opacity: 0.7;\n  }\n  50% { \n    transform: scaleY(1);\n    opacity: 1;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .cross-platform-container {\n    gap: 60px;\n  }\n\n  .section-title {\n    font-size: 3rem;\n  }\n\n  .desktop-mockup {\n    width: 350px;\n    height: 240px;\n  }\n\n  .mobile-mockup {\n    width: 160px;\n    height: 280px;\n  }\n}\n\n@media (max-width: 768px) {\n  .cross-platform-section {\n    padding: 80px 20px;\n    min-height: auto;\n  }\n\n  .cross-platform-container {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n  }\n\n  .section-description {\n    font-size: 1rem;\n  }\n\n  .device-stack {\n    max-width: 400px;\n    height: 500px;\n    margin: 0 auto;\n  }\n\n  .desktop-mockup {\n    width: 300px;\n    height: 200px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n\n  .mobile-mockup {\n    width: 140px;\n    height: 250px;\n    top: 150px;\n    right: 20px;\n  }\n\n  .voice-widget {\n    bottom: 30px;\n    left: 20px;\n    width: 100px;\n    height: 70px;\n  }\n}\n\n@media (max-width: 480px) {\n  .cross-platform-section {\n    padding: 60px 15px;\n  }\n\n  .section-title {\n    font-size: 2rem;\n  }\n\n  .platform-badges {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .platform-badge {\n    font-size: 0.75rem;\n    padding: 6px 12px;\n  }\n\n  .device-stack {\n    max-width: 320px;\n    height: 400px;\n  }\n\n  .desktop-mockup {\n    width: 280px;\n    height: 180px;\n  }\n\n  .mobile-mockup {\n    width: 120px;\n    height: 220px;\n    top: 120px;\n    right: 10px;\n  }\n\n  .voice-widget {\n    width: 90px;\n    height: 60px;\n    bottom: 20px;\n    left: 10px;\n  }\n\n  .get-started-btn {\n    width: 100%;\n    max-width: 200px;\n    margin: 0 auto;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;AAQA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;;;;;;;;;AAYA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAMF;EACE;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;;EAOA;;;;;;;EAOA;;;;;;;;AAQF;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;;EAOA;;;;;;;EAOA", "debugId": null}}, {"offset": {"line": 3298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Flow/my-app/src/Style/TestimonialsSection.css"], "sourcesContent": ["/* Testimonials Section */\n.testimonials-section {\n  background: #1a1a1a;\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.testimonials-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  padding: 0 40px;\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: 80px;\n  position: relative;\n}\n\n.section-title {\n  font-size: 4rem;\n  font-weight: 400;\n  color: white;\n  line-height: 1.1;\n  margin: 0 0 16px 0;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n.section-subtitle {\n  font-size: 1.25rem;\n  color: rgba(255, 255, 255, 0.8);\n  margin: 0;\n  text-align: center;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n}\n\n.carousel-container {\n  position: relative;\n  margin-top: 60px;\n}\n\n.carousel-wrapper {\n  overflow: hidden;\n  border-radius: 20px;\n}\n\n.carousel-track {\n  display: flex;\n  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  gap: 24px;\n  will-change: transform;\n}\n\n.carousel-controls {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n  pointer-events: none;\n  z-index: 10;\n}\n\n.carousel-btn {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.9);\n  border: none;\n  color: #1f2937;\n  font-size: 1.5rem;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  pointer-events: all;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n}\n\n.carousel-btn:hover {\n  background: white;\n  transform: scale(1.1);\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);\n}\n\n.carousel-btn:active {\n  transform: scale(0.95);\n}\n\n.prev-btn {\n  margin-left: -25px;\n}\n\n.next-btn {\n  margin-right: -25px;\n}\n\n.carousel-dots {\n  display: flex;\n  justify-content: center;\n  gap: 12px;\n  margin-top: 40px;\n}\n\n.dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.3);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.dot.active {\n  background: #f59e0b;\n  transform: scale(1.2);\n}\n\n.dot:hover {\n  background: rgba(255, 255, 255, 0.6);\n}\n\n.testimonial-card {\n  background: white;\n  border-radius: 20px;\n  padding: 32px;\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  flex-shrink: 0;\n  margin-right: 24px;\n  height: auto;\n  min-height: 300px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.testimonial-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.testimonial-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n\n.testimonial-card:hover::before {\n  opacity: 1;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.avatar {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 4px 0;\n}\n\n.user-role {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0 0 4px 0;\n}\n\n.user-company {\n  font-size: 0.75rem;\n  color: #9ca3af;\n  margin: 0;\n  font-style: italic;\n}\n\n.testimonial-content {\n  margin-bottom: 16px;\n}\n\n.testimonial-text {\n  font-size: 0.95rem;\n  line-height: 1.6;\n  color: #374151;\n  margin: 0;\n  font-style: italic;\n}\n\n.rating {\n  display: flex;\n  gap: 2px;\n}\n\n.star {\n  font-size: 1rem;\n  opacity: 0.3;\n  transition: opacity 0.2s ease;\n}\n\n.star.filled {\n  opacity: 1;\n}\n\n/* Masonry-like positioning for visual interest */\n.card-1 { grid-row: span 1; }\n.card-2 { grid-row: span 1; }\n.card-3 { grid-row: span 1; }\n.card-4 { grid-row: span 1; }\n.card-5 { grid-row: span 1; }\n.card-6 { grid-row: span 1; }\n.card-7 { grid-row: span 1; }\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .testimonials-section {\n    padding: 100px 20px;\n  }\n  \n  .section-title {\n    font-size: 3.5rem;\n  }\n  \n  .testimonials-grid {\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: 20px;\n  }\n  \n  .decorative-lines {\n    width: 250px;\n    height: 250px;\n  }\n}\n\n@media (max-width: 1024px) {\n  .testimonial-card {\n    padding: 28px;\n    min-height: 280px;\n  }\n\n  .carousel-btn {\n    width: 45px;\n    height: 45px;\n    font-size: 1.3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .testimonials-section {\n    padding: 80px 20px;\n    min-height: auto;\n  }\n\n  .testimonials-container {\n    padding: 0 20px;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n  }\n\n  .section-header {\n    margin-bottom: 40px;\n  }\n\n  .testimonial-card {\n    padding: 24px;\n    min-height: 250px;\n    margin-right: 16px;\n  }\n\n  .carousel-controls {\n    display: none;\n  }\n\n  .carousel-dots {\n    margin-top: 24px;\n  }\n\n  .carousel-track {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .testimonial-card {\n    padding: 20px;\n    min-height: 220px;\n  }\n\n  .section-title {\n    font-size: 2rem;\n  }\n\n  .section-subtitle {\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .testimonials-section {\n    padding: 60px 15px;\n  }\n  \n  .section-title {\n    font-size: 2rem;\n  }\n  \n  .testimonial-card {\n    padding: 16px;\n  }\n  \n  .card-header {\n    gap: 12px;\n  }\n  \n  .avatar {\n    width: 40px;\n    height: 40px;\n    font-size: 1.2rem;\n  }\n  \n  .user-name {\n    font-size: 0.9rem;\n  }\n  \n  .user-role {\n    font-size: 0.8rem;\n  }\n  \n  .testimonial-text {\n    font-size: 0.9rem;\n  }\n  \n  .decorative-lines {\n    display: none;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AAKA;;;;AASA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAOA;;;;;EAKA;;;;;;;AAOF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EASA;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 3658, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/Footer.css"], "sourcesContent": ["/* Footer Section */\n.footer-section {\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n  color: white;\n  padding: 80px 0 0;\n}\n\n.footer-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 40px;\n}\n\n.footer-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.footer-main {\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 80px;\n  margin-bottom: 60px;\n}\n\n.footer-brand {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.brand-logo {\n  margin-bottom: 16px;\n}\n\n.brand-name {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: white;\n  margin: 0 0 8px 0;\n  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.brand-tagline {\n  font-size: 1rem;\n  color: #9ca3af;\n  margin: 0;\n  font-weight: 500;\n}\n\n.brand-description {\n  font-size: 1rem;\n  line-height: 1.6;\n  color: #d1d5db;\n  margin: 0 0 32px 0;\n  max-width: 400px;\n}\n\n.newsletter-signup {\n  margin-bottom: 32px;\n}\n\n.newsletter-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: white;\n  margin: 0 0 8px 0;\n}\n\n.newsletter-description {\n  font-size: 0.875rem;\n  color: #9ca3af;\n  margin: 0 0 16px 0;\n}\n\n.newsletter-form {\n  display: flex;\n  gap: 12px;\n  max-width: 400px;\n}\n\n.newsletter-input {\n  flex: 1;\n  padding: 12px 16px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  font-size: 0.875rem;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n}\n\n.newsletter-input::placeholder {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.newsletter-input:focus {\n  outline: none;\n  border-color: #f59e0b;\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.newsletter-btn {\n  padding: 12px 24px;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n}\n\n.newsletter-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n}\n\n.social-links {\n  display: flex;\n  gap: 16px;\n}\n\n.social-link {\n  width: 44px;\n  height: 44px;\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #d1d5db;\n  transition: all 0.3s ease;\n  text-decoration: none;\n}\n\n.social-link:hover {\n  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);\n  color: white;\n  transform: translateY(-2px);\n}\n\n.footer-links {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 40px;\n}\n\n.link-group {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.link-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: white;\n  margin: 0 0 8px 0;\n}\n\n.link-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.footer-link {\n  color: #9ca3af;\n  text-decoration: none;\n  font-size: 0.95rem;\n  transition: color 0.3s ease;\n  position: relative;\n}\n\n.footer-link:hover {\n  color: #f59e0b;\n}\n\n.footer-link::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  width: 0;\n  height: 1px;\n  background: #f59e0b;\n  transition: width 0.3s ease;\n}\n\n.footer-link:hover::after {\n  width: 100%;\n}\n\n.footer-bottom {\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 32px 0;\n}\n\n.footer-divider {\n  width: 100%;\n  height: 1px;\n  background: linear-gradient(90deg, transparent 0%, rgba(245, 158, 11, 0.3) 50%, transparent 100%);\n  margin-bottom: 32px;\n}\n\n.footer-bottom-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.copyright {\n  font-size: 0.875rem;\n  color: #9ca3af;\n  margin: 0;\n}\n\n.footer-bottom-links {\n  display: flex;\n  gap: 24px;\n}\n\n.footer-bottom-link {\n  font-size: 0.875rem;\n  color: #9ca3af;\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n\n.footer-bottom-link:hover {\n  color: #f59e0b;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .footer-main {\n    grid-template-columns: 1fr;\n    gap: 60px;\n  }\n  \n  .footer-links {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 40px;\n  }\n}\n\n@media (max-width: 768px) {\n  .footer-section {\n    padding: 60px 0 0;\n  }\n  \n  .footer-container {\n    padding: 0 20px;\n  }\n  \n  .footer-main {\n    gap: 40px;\n    margin-bottom: 40px;\n  }\n  \n  .footer-links {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 32px;\n  }\n  \n  .brand-name {\n    font-size: 2rem;\n  }\n  \n  .brand-description {\n    font-size: 0.95rem;\n  }\n  \n  .footer-bottom-content {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n  \n  .footer-bottom-links {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .footer-section {\n    padding: 40px 0 0;\n  }\n  \n  .footer-container {\n    padding: 0 15px;\n  }\n  \n  .footer-main {\n    gap: 32px;\n    margin-bottom: 32px;\n  }\n  \n  .footer-links {\n    grid-template-columns: 1fr;\n    gap: 24px;\n  }\n  \n  .brand-name {\n    font-size: 1.75rem;\n  }\n  \n  .brand-description {\n    font-size: 0.9rem;\n  }\n  \n  .social-links {\n    gap: 12px;\n  }\n  \n  .social-link {\n    width: 40px;\n    height: 40px;\n  }\n  \n  .link-title {\n    font-size: 1rem;\n  }\n  \n  .footer-link {\n    font-size: 0.9rem;\n  }\n  \n  .footer-bottom {\n    padding: 24px 0;\n  }\n  \n  .copyright,\n  .footer-bottom-link {\n    font-size: 0.8rem;\n  }\n  \n  .footer-bottom-links {\n    gap: 12px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;AAKA;EACE;;;;;EAKA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA", "debugId": null}}]}