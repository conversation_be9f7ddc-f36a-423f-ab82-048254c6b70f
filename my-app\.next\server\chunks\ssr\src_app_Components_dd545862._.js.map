{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/PricingSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/PricingSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst PricingSection = () => {\n    const [isYearly, setIsYearly] = useState(false);\n    const sectionRef = useRef(null);\n    const cardsRef = useRef([]);\n\n    const pricingPlans = {\n        monthly: {\n            free: {\n                title: \"Get Basic\",\n                subtitle: \"Free\",\n                price: \"Free\",\n                features: [\n                    \"2,000 words per month\",\n                    \"Basic writing assistance\",\n                    \"Grammar and spell check\",\n                    \"Limited AI suggestions\",\n                    \"Email support\"\n                ],\n                buttonText: \"Get started\",\n                buttonStyle: \"outline\",\n                popular: false\n            },\n            pro: {\n                title: \"Pro Plan\",\n                subtitle: \"$15/mo\",\n                price: \"$15\",\n                period: \"/mo\",\n                features: [\n                    \"Everything in Free Plan\",\n                    \"50,000 words per month\",\n                    \"Advanced AI writing tools\",\n                    \"Custom tone settings\",\n                    \"Priority support\",\n                    \"Export to multiple formats\"\n                ],\n                buttonText: \"Get started for free\",\n                buttonStyle: \"primary\",\n                popular: true\n            },\n            team: {\n                title: \"For Teams\",\n                subtitle: \"$12/user/mo\",\n                price: \"$12\",\n                period: \"/user/mo\",\n                features: [\n                    \"Everything in Pro Plan\",\n                    \"Unlimited words\",\n                    \"Team collaboration tools\",\n                    \"Admin dashboard\",\n                    \"Custom integrations\",\n                    \"24/7 priority support\"\n                ],\n                buttonText: \"Get started\",\n                buttonStyle: \"primary\",\n                popular: false\n            },\n            enterprise: {\n                title: \"For Enterprise\",\n                subtitle: \"Contact us\",\n                price: \"Custom\",\n                features: [\n                    \"Everything in Team Plan\",\n                    \"Custom AI model training\",\n                    \"Advanced security features\",\n                    \"Dedicated account manager\",\n                    \"Custom integrations\",\n                    \"SLA guarantee\"\n                ],\n                buttonText: \"Contact sales\",\n                buttonStyle: \"outline\",\n                popular: false\n            }\n        },\n        yearly: {\n            free: {\n                title: \"Get Basic\",\n                subtitle: \"Free\",\n                price: \"Free\",\n                features: [\n                    \"2,000 words per month\",\n                    \"Basic writing assistance\",\n                    \"Grammar and spell check\",\n                    \"Limited AI suggestions\",\n                    \"Email support\"\n                ],\n                buttonText: \"Get started\",\n                buttonStyle: \"outline\",\n                popular: false\n            },\n            pro: {\n                title: \"Pro Plan\",\n                subtitle: \"$150/year\",\n                price: \"$150\",\n                period: \"/year\",\n                originalPrice: \"$180\",\n                savings: \"Save $30\",\n                features: [\n                    \"Everything in Free Plan\",\n                    \"50,000 words per month\",\n                    \"Advanced AI writing tools\",\n                    \"Custom tone settings\",\n                    \"Priority support\",\n                    \"Export to multiple formats\"\n                ],\n                buttonText: \"Get started for free\",\n                buttonStyle: \"primary\",\n                popular: true\n            },\n            team: {\n                title: \"For Teams\",\n                subtitle: \"$120/user/year\",\n                price: \"$120\",\n                period: \"/user/year\",\n                originalPrice: \"$144\",\n                savings: \"Save $24\",\n                features: [\n                    \"Everything in Pro Plan\",\n                    \"Unlimited words\",\n                    \"Team collaboration tools\",\n                    \"Admin dashboard\",\n                    \"Custom integrations\",\n                    \"24/7 priority support\"\n                ],\n                buttonText: \"Get started\",\n                buttonStyle: \"primary\",\n                popular: false\n            },\n            enterprise: {\n                title: \"For Enterprise\",\n                subtitle: \"Contact us\",\n                price: \"Custom\",\n                features: [\n                    \"Everything in Team Plan\",\n                    \"Custom AI model training\",\n                    \"Advanced security features\",\n                    \"Dedicated account manager\",\n                    \"Custom integrations\",\n                    \"SLA guarantee\"\n                ],\n                buttonText: \"Contact sales\",\n                buttonStyle: \"outline\",\n                popular: false\n            }\n        }\n    };\n\n    const currentPlans = isYearly ? pricingPlans.yearly : pricingPlans.monthly;\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const cards = cardsRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set(cards, { opacity: 0, y: 50, scale: 0.9 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        // Animate cards in sequence\n        tl.to(cards, {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            duration: 0.6,\n            ease: \"back.out(1.7)\",\n            stagger: 0.1\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"pricing-section\" ref={sectionRef}>\n            <div className=\"pricing-container\">\n                <div className=\"pricing-header\">\n                    <h1 className=\"pricing-title\">Pricing</h1>\n                    <p className=\"pricing-subtitle\">\n                        Choose the plan to get started for free.<br />\n                        No credit card required.\n                    </p>\n                    \n                    <div className=\"billing-toggle\">\n                        <span className={`toggle-label ${!isYearly ? 'active' : ''}`}>Monthly</span>\n                        <div className=\"toggle-switch\" onClick={() => setIsYearly(!isYearly)}>\n                            <div className={`toggle-slider ${isYearly ? 'yearly' : 'monthly'}`}></div>\n                        </div>\n                        <span className={`toggle-label ${isYearly ? 'active' : ''}`}>\n                            Yearly\n                            <span className=\"discount-badge\">Save 20% discount</span>\n                        </span>\n                    </div>\n                </div>\n\n                <div className=\"pricing-cards\">\n                    {Object.entries(currentPlans).map(([key, plan], index) => (\n                        <div \n                            key={key}\n                            className={`pricing-card ${plan.popular ? 'popular' : ''}`}\n                            ref={el => cardsRef.current[index] = el}\n                        >\n                            {plan.popular && <div className=\"popular-badge\">Most Popular</div>}\n                            \n                            <div className=\"card-header\">\n                                <h3 className=\"plan-title\">{plan.title}</h3>\n                                <div className=\"price-container\">\n                                    <div className=\"price-main\">\n                                        <span className=\"price\">{plan.price}</span>\n                                        {plan.period && <span className=\"period\">{plan.period}</span>}\n                                    </div>\n                                    {plan.originalPrice && (\n                                        <div className=\"price-details\">\n                                            <span className=\"original-price\">{plan.originalPrice}</span>\n                                            <span className=\"savings\">{plan.savings}</span>\n                                        </div>\n                                    )}\n                                </div>\n                            </div>\n\n                            <div className=\"card-features\">\n                                <ul className=\"features-list\">\n                                    {plan.features.map((feature, idx) => (\n                                        <li key={idx} className=\"feature-item\">\n                                            <span className=\"check-icon\">✓</span>\n                                            {feature}\n                                        </li>\n                                    ))}\n                                </ul>\n                            </div>\n\n                            <div className=\"card-footer\">\n                                <button className={`cta-button ${plan.buttonStyle}`}>\n                                    {plan.buttonText}\n                                </button>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n\n                <div className=\"pricing-footer\">\n                    <div className=\"money-back-guarantee\">\n                        <span className=\"guarantee-icon\">🛡️</span>\n                        <div className=\"guarantee-text\">\n                            <strong>30-day money-back guarantee</strong>\n                            <p>We offer a full refund within 30 days of purchase if you're not satisfied.</p>\n                        </div>\n                    </div>\n                    <button className=\"contact-sales-btn\">Contact Sales</button>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default PricingSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,iBAAiB;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,MAAM,eAAe;QACjB,SAAS;YACL,MAAM;gBACF,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;YACA,KAAK;gBACD,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;YACA,MAAM;gBACF,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;YACA,YAAY;gBACR,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;QACJ;QACA,QAAQ;YACJ,MAAM;gBACF,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;YACA,KAAK;gBACD,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,eAAe;gBACf,SAAS;gBACT,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;YACA,MAAM;gBACF,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,eAAe;gBACf,SAAS;gBACT,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;YACA,YAAY;gBACR,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,UAAU;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;iBACH;gBACD,YAAY;gBACZ,aAAa;gBACb,SAAS;YACb;QACJ;IACJ;IAEA,MAAM,eAAe,WAAW,aAAa,MAAM,GAAG,aAAa,OAAO;IAE1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAE9B,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QAEhD,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,4BAA4B;QAC5B,GAAG,EAAE,CAAC,OAAO;YACT,SAAS;YACT,GAAG;YACH,OAAO;YACP,UAAU;YACV,MAAM;YACN,SAAS;QACb;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAAkB,KAAK;kBACtC,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAAgB;;;;;;sCAC9B,8OAAC;4BAAE,WAAU;;gCAAmB;8CACY,8OAAC;;;;;gCAAK;;;;;;;sCAIlD,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAW,CAAC,aAAa,EAAE,CAAC,WAAW,WAAW,IAAI;8CAAE;;;;;;8CAC9D,8OAAC;oCAAI,WAAU;oCAAgB,SAAS,IAAM,YAAY,CAAC;8CACvD,cAAA,8OAAC;wCAAI,WAAW,CAAC,cAAc,EAAE,WAAW,WAAW,WAAW;;;;;;;;;;;8CAEtE,8OAAC;oCAAK,WAAW,CAAC,aAAa,EAAE,WAAW,WAAW,IAAI;;wCAAE;sDAEzD,8OAAC;4CAAK,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAK7C,8OAAC;oBAAI,WAAU;8BACV,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,sBAC5C,8OAAC;4BAEG,WAAW,CAAC,aAAa,EAAE,KAAK,OAAO,GAAG,YAAY,IAAI;4BAC1D,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;;gCAEpC,KAAK,OAAO,kBAAI,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAEhD,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAG,WAAU;sDAAc,KAAK,KAAK;;;;;;sDACtC,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAK,WAAU;sEAAS,KAAK,KAAK;;;;;;wDAClC,KAAK,MAAM,kBAAI,8OAAC;4DAAK,WAAU;sEAAU,KAAK,MAAM;;;;;;;;;;;;gDAExD,KAAK,aAAa,kBACf,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAK,WAAU;sEAAkB,KAAK,aAAa;;;;;;sEACpD,8OAAC;4DAAK,WAAU;sEAAW,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;8CAMvD,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC;wCAAG,WAAU;kDACT,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBACzB,8OAAC;gDAAa,WAAU;;kEACpB,8OAAC;wDAAK,WAAU;kEAAa;;;;;;oDAC5B;;+CAFI;;;;;;;;;;;;;;;8CAQrB,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC;wCAAO,WAAW,CAAC,WAAW,EAAE,KAAK,WAAW,EAAE;kDAC9C,KAAK,UAAU;;;;;;;;;;;;2BAnCnB;;;;;;;;;;8BA0CjB,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;8CACjC,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;sDAAO;;;;;;sDACR,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAGX,8OAAC;4BAAO,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;AAK1D;uCAEe", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/SavingsCalculator.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/SavingsCalculator.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst SavingsCalculator = () => {\n    const [hoursPerDay, setHoursPerDay] = useState(2);\n    const [hourlyRate, setHourlyRate] = useState(50);\n    const sectionRef = useRef(null);\n    const calculatorRef = useRef(null);\n    const illustrationRef = useRef(null);\n\n    // Calculate savings\n    const wordsPerHour = 1200; // Average typing speed\n    const dailyWords = hoursPerDay * wordsPerHour;\n    const monthlyWords = dailyWords * 22; // Working days\n    const timeSavedHours = hoursPerDay * 0.6; // 60% time saved with Flow\n    const monthlySavings = timeSavedHours * 22 * hourlyRate;\n    const flowMonthlyCost = 15; // Pro plan cost\n    const netSavings = monthlySavings - flowMonthlyCost;\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const calculator = calculatorRef.current;\n        const illustration = illustrationRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([calculator, illustration], { opacity: 0, y: 50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(illustration, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(calculator, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animation for money icons\n        const moneyIcons = document.querySelectorAll('.money-icon');\n        moneyIcons.forEach((icon, index) => {\n            gsap.to(icon, {\n                y: -10,\n                duration: 2 + index * 0.3,\n                ease: \"power2.inOut\",\n                yoyo: true,\n                repeat: -1,\n                delay: index * 0.2\n            });\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"savings-calculator-section\" ref={sectionRef}>\n            <div className=\"savings-container\">\n                <div className=\"content-section\">\n                    <div className=\"text-content\">\n                        <h2 className=\"section-title\">Calculate your savings</h2>\n                        <div className=\"scenario-text\">\n                            <p className=\"scenario-main\">\n                                I spend <strong>{hoursPerDay} hours</strong> typing a day ({dailyWords.toLocaleString()} words)\n                            </p>\n                            <p className=\"scenario-detail\">\n                                On average, people type 2 hours a day (that's {monthlyWords.toLocaleString()} words/month)\n                            </p>\n                            <p className=\"value-statement\">\n                                and, my time is worth <strong>${hourlyRate}/hour</strong>\n                            </p>\n                        </div>\n\n                        <div className=\"input-controls\">\n                            <div className=\"input-group\">\n                                <label htmlFor=\"hours\">Hours per day:</label>\n                                <input\n                                    type=\"range\"\n                                    id=\"hours\"\n                                    min=\"1\"\n                                    max=\"8\"\n                                    step=\"0.5\"\n                                    value={hoursPerDay}\n                                    onChange={(e) => setHoursPerDay(parseFloat(e.target.value))}\n                                    className=\"slider\"\n                                />\n                                <span className=\"slider-value\">{hoursPerDay}h</span>\n                            </div>\n                            \n                            <div className=\"input-group\">\n                                <label htmlFor=\"rate\">Hourly rate:</label>\n                                <input\n                                    type=\"range\"\n                                    id=\"rate\"\n                                    min=\"25\"\n                                    max=\"200\"\n                                    step=\"5\"\n                                    value={hourlyRate}\n                                    onChange={(e) => setHourlyRate(parseInt(e.target.value))}\n                                    className=\"slider\"\n                                />\n                                <span className=\"slider-value\">${hourlyRate}</span>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"illustration-section\" ref={illustrationRef}>\n                        <div className=\"character-illustration\">\n                            <div className=\"character\">\n                                <div className=\"character-head\">\n                                    <div className=\"face\">😊</div>\n                                    <div className=\"hat\">🎩</div>\n                                </div>\n                                <div className=\"character-body\">\n                                    <div className=\"arms\">\n                                        <div className=\"arm left\">🤲</div>\n                                        <div className=\"arm right\">🤲</div>\n                                    </div>\n                                </div>\n                            </div>\n                            \n                            <div className=\"money-animation\">\n                                <div className=\"money-icon\" style={{top: '10%', left: '20%'}}>💰</div>\n                                <div className=\"money-icon\" style={{top: '30%', right: '15%'}}>💵</div>\n                                <div className=\"money-icon\" style={{top: '50%', left: '10%'}}>💸</div>\n                                <div className=\"money-icon\" style={{top: '70%', right: '25%'}}>💰</div>\n                                <div className=\"money-icon\" style={{top: '20%', right: '35%'}}>💵</div>\n                                <div className=\"money-icon\" style={{top: '80%', left: '30%'}}>💸</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"calculator-section\" ref={calculatorRef}>\n                    <div className=\"calculator-card\">\n                        <div className=\"calculator-header\">\n                            <h3>Monthly, you'll save</h3>\n                            <div className=\"savings-amount\">\n                                ${netSavings.toLocaleString()}<span className=\"period\">/mo</span>\n                            </div>\n                        </div>\n\n                        <div className=\"breakdown\">\n                            <div className=\"breakdown-item\">\n                                <span className=\"label\">Hours spent typing</span>\n                                <span className=\"value\">{(hoursPerDay * 22).toFixed(0)}h</span>\n                            </div>\n                            <div className=\"breakdown-item\">\n                                <span className=\"label\">Hours saved monthly</span>\n                                <span className=\"value\">{(timeSavedHours * 22).toFixed(0)}h</span>\n                            </div>\n                            <div className=\"breakdown-item\">\n                                <span className=\"label\">Time value saved</span>\n                                <span className=\"value\">${monthlySavings.toLocaleString()}</span>\n                            </div>\n                            <div className=\"breakdown-item\">\n                                <span className=\"label\">Flow Pro monthly cost</span>\n                                <span className=\"value negative\">-${flowMonthlyCost}</span>\n                            </div>\n                        </div>\n\n                        <div className=\"calculator-footer\">\n                            <button className=\"get-started-btn\">\n                                Start saving time today\n                                <span className=\"arrow\">→</span>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default SavingsCalculator;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,oBAAoB;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,oBAAoB;IACpB,MAAM,eAAe,MAAM,uBAAuB;IAClD,MAAM,aAAa,cAAc;IACjC,MAAM,eAAe,aAAa,IAAI,eAAe;IACrD,MAAM,iBAAiB,cAAc,KAAK,2BAA2B;IACrE,MAAM,iBAAiB,iBAAiB,KAAK;IAC7C,MAAM,kBAAkB,IAAI,gBAAgB;IAC5C,MAAM,aAAa,iBAAiB;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,aAAa,cAAc,OAAO;QACxC,MAAM,eAAe,gBAAgB,OAAO;QAE5C,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC;YAAC;YAAY;SAAa,EAAE;YAAE,SAAS;YAAG,GAAG;QAAG;QAEzD,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,GAAG,EAAE,CAAC,cAAc;YAChB,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GACC,EAAE,CAAC,YAAY;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;QACV,GAAG;QAEH,qCAAqC;QACrC,MAAM,aAAa,SAAS,gBAAgB,CAAC;QAC7C,WAAW,OAAO,CAAC,CAAC,MAAM;YACtB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;gBACV,GAAG,CAAC;gBACJ,UAAU,IAAI,QAAQ;gBACtB,MAAM;gBACN,MAAM;gBACN,QAAQ,CAAC;gBACT,OAAO,QAAQ;YACnB;QACJ;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,qBACI,8OAAC;QAAQ,WAAU;QAA6B,KAAK;kBACjD,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAE,WAAU;;gDAAgB;8DACjB,8OAAC;;wDAAQ;wDAAY;;;;;;;gDAAe;gDAAgB,WAAW,cAAc;gDAAG;;;;;;;sDAE5F,8OAAC;4CAAE,WAAU;;gDAAkB;gDACoB,aAAa,cAAc;gDAAG;;;;;;;sDAEjF,8OAAC;4CAAE,WAAU;;gDAAkB;8DACL,8OAAC;;wDAAO;wDAAE;wDAAW;;;;;;;;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAM,SAAQ;8DAAQ;;;;;;8DACvB,8OAAC;oDACG,MAAK;oDACL,IAAG;oDACH,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzD,WAAU;;;;;;8DAEd,8OAAC;oDAAK,WAAU;;wDAAgB;wDAAY;;;;;;;;;;;;;sDAGhD,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAM,SAAQ;8DAAO;;;;;;8DACtB,8OAAC;oDACG,MAAK;oDACL,IAAG;oDACH,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK;oDACtD,WAAU;;;;;;8DAEd,8OAAC;oDAAK,WAAU;;wDAAe;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;4BAAuB,KAAK;sCACvC,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACX,8OAAC;wDAAI,WAAU;kEAAO;;;;;;kEACtB,8OAAC;wDAAI,WAAU;kEAAM;;;;;;;;;;;;0DAEzB,8OAAC;gDAAI,WAAU;0DACX,cAAA,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;sEAAW;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;gDAAa,OAAO;oDAAC,KAAK;oDAAO,MAAM;gDAAK;0DAAG;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;gDAAa,OAAO;oDAAC,KAAK;oDAAO,OAAO;gDAAK;0DAAG;;;;;;0DAC/D,8OAAC;gDAAI,WAAU;gDAAa,OAAO;oDAAC,KAAK;oDAAO,MAAM;gDAAK;0DAAG;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;gDAAa,OAAO;oDAAC,KAAK;oDAAO,OAAO;gDAAK;0DAAG;;;;;;0DAC/D,8OAAC;gDAAI,WAAU;gDAAa,OAAO;oDAAC,KAAK;oDAAO,OAAO;gDAAK;0DAAG;;;;;;0DAC/D,8OAAC;gDAAI,WAAU;gDAAa,OAAO;oDAAC,KAAK;oDAAO,MAAM;gDAAK;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM9E,8OAAC;oBAAI,WAAU;oBAAqB,KAAK;8BACrC,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAI,WAAU;;4CAAiB;4CAC1B,WAAW,cAAc;0DAAG,8OAAC;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;0CAI/D,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;;oDAAS,CAAC,cAAc,EAAE,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAE3D,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;;oDAAS,CAAC,iBAAiB,EAAE,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAE9D,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;;oDAAQ;oDAAE,eAAe,cAAc;;;;;;;;;;;;;kDAE3D,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;0DACxB,8OAAC;gDAAK,WAAU;;oDAAiB;oDAAG;;;;;;;;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAO,WAAU;;wCAAkB;sDAEhC,8OAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD;uCAEe", "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/FAQSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/FAQSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst FAQSection = () => {\n    const [openFAQ, setOpenFAQ] = useState(null);\n    const sectionRef = useRef(null);\n    const faqItemsRef = useRef([]);\n\n    const faqs = [\n        {\n            question: \"How do I upgrade?\",\n            answer: \"You can upgrade your plan at any time from your account settings. Simply go to the billing section, select your desired plan, and follow the payment process. Your upgrade will take effect immediately, and you'll have access to all the new features right away.\"\n        },\n        {\n            question: \"Is there a free trial or free plan available?\",\n            answer: \"Yes! We offer a free plan that includes 2,000 words per month with basic writing assistance. You can also start a 14-day free trial of our Pro plan to experience all premium features before committing to a paid subscription.\"\n        },\n        {\n            question: \"Can I change or cancel my subscription at any time?\",\n            answer: \"Absolutely! You have full control over your subscription. You can upgrade, downgrade, or cancel at any time from your account settings. If you cancel, you'll continue to have access to your paid features until the end of your current billing period.\"\n        },\n        {\n            question: \"Do you offer discounts for specific groups?\",\n            answer: \"Yes, we offer special discounts for students, educators, non-profit organizations, and startups. We also provide volume discounts for teams of 10 or more users. Contact our sales team to learn more about available discounts and how to apply them.\"\n        },\n        {\n            question: \"What integrations are included in each plan?\",\n            answer: \"Our Free plan includes basic integrations with Google Docs and Microsoft Word. The Pro plan adds integrations with Slack, Notion, and popular email clients. Team and Enterprise plans include all integrations plus custom API access and priority support for new integration requests.\"\n        },\n        {\n            question: \"How does the word count limit work?\",\n            answer: \"Word count limits reset monthly on your billing date. Unused words don't roll over to the next month. If you exceed your limit, you can either upgrade your plan or purchase additional word packs. We'll notify you when you're approaching your limit.\"\n        }\n    ];\n\n    const toggleFAQ = (index) => {\n        setOpenFAQ(openFAQ === index ? null : index);\n    };\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const faqItems = faqItemsRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set(faqItems, { opacity: 0, y: 30 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        // Animate FAQ items in sequence\n        tl.to(faqItems, {\n            opacity: 1,\n            y: 0,\n            duration: 0.6,\n            ease: \"power3.out\",\n            stagger: 0.1\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    useEffect(() => {\n        // Animate FAQ content when opened/closed\n        faqItemsRef.current.forEach((item, index) => {\n            if (!item) return;\n            \n            const content = item.querySelector('.faq-answer');\n            const icon = item.querySelector('.faq-icon');\n            \n            if (openFAQ === index) {\n                gsap.to(content, {\n                    height: 'auto',\n                    opacity: 1,\n                    duration: 0.4,\n                    ease: \"power3.out\"\n                });\n                gsap.to(icon, {\n                    rotation: 180,\n                    duration: 0.3,\n                    ease: \"power3.out\"\n                });\n            } else {\n                gsap.to(content, {\n                    height: 0,\n                    opacity: 0,\n                    duration: 0.3,\n                    ease: \"power3.in\"\n                });\n                gsap.to(icon, {\n                    rotation: 0,\n                    duration: 0.3,\n                    ease: \"power3.out\"\n                });\n            }\n        });\n    }, [openFAQ]);\n\n    return (\n        <section className=\"faq-section\" ref={sectionRef}>\n            <div className=\"faq-container\">\n                <div className=\"faq-header\">\n                    <h2 className=\"faq-title\">Frequently asked questions</h2>\n                </div>\n\n                <div className=\"faq-grid\">\n                    {faqs.map((faq, index) => (\n                        <div \n                            key={index}\n                            className={`faq-item ${openFAQ === index ? 'active' : ''}`}\n                            ref={el => faqItemsRef.current[index] = el}\n                        >\n                            <div \n                                className=\"faq-question\"\n                                onClick={() => toggleFAQ(index)}\n                            >\n                                <h3>{faq.question}</h3>\n                                <div className=\"faq-icon\">\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\">\n                                        <path \n                                            d=\"M5 7.5L10 12.5L15 7.5\" \n                                            stroke=\"currentColor\" \n                                            strokeWidth=\"2\" \n                                            strokeLinecap=\"round\" \n                                            strokeLinejoin=\"round\"\n                                        />\n                                    </svg>\n                                </div>\n                            </div>\n                            <div className=\"faq-answer\">\n                                <p>{faq.answer}</p>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n\n                <div className=\"faq-footer\">\n                    <div className=\"contact-support\">\n                        <h3>Still have questions?</h3>\n                        <p>Can't find the answer you're looking for? Please chat with our friendly team.</p>\n                        <button className=\"contact-btn\">\n                            Get in touch\n                            <span className=\"arrow\">→</span>\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default FAQSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;;AAMA,gCAAgC;AAChC,uCAAmC;;AAEnC;AAEA,MAAM,aAAa;IACf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE7B,MAAM,OAAO;QACT;YACI,UAAU;YACV,QAAQ;QACZ;QACA;YACI,UAAU;YACV,QAAQ;QACZ;QACA;YACI,UAAU;YACV,QAAQ;QACZ;QACA;YACI,UAAU;YACV,QAAQ;QACZ;QACA;YACI,UAAU;YACV,QAAQ;QACZ;QACA;YACI,UAAU;YACV,QAAQ;QACZ;KACH;IAED,MAAM,YAAY,CAAC;QACf,WAAW,YAAY,QAAQ,OAAO;IAC1C;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,WAAW,YAAY,OAAO;QAEpC,IAAI,CAAC,SAAS;QAEd,gBAAgB;QAChB,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU;YAAE,SAAS;YAAG,GAAG;QAAG;QAEvC,0CAA0C;QAC1C,MAAM,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACrB,eAAe;gBACX,SAAS;gBACT,OAAO;gBACP,eAAe;YACnB;QACJ;QAEA,gCAAgC;QAChC,GAAG,EAAE,CAAC,UAAU;YACZ,SAAS;YACT,GAAG;YACH,UAAU;YACV,MAAM;YACN,SAAS;QACb;QAEA,OAAO;YACH,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QAC1D;IACJ,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,yCAAyC;QACzC,YAAY,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM;YAC/B,IAAI,CAAC,MAAM;YAEX,MAAM,UAAU,KAAK,aAAa,CAAC;YACnC,MAAM,OAAO,KAAK,aAAa,CAAC;YAEhC,IAAI,YAAY,OAAO;gBACnB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;oBACb,QAAQ;oBACR,SAAS;oBACT,UAAU;oBACV,MAAM;gBACV;gBACA,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACV,UAAU;oBACV,UAAU;oBACV,MAAM;gBACV;YACJ,OAAO;gBACH,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;oBACb,QAAQ;oBACR,SAAS;oBACT,UAAU;oBACV,MAAM;gBACV;gBACA,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACV,UAAU;oBACV,UAAU;oBACV,MAAM;gBACV;YACJ;QACJ;IACJ,GAAG;QAAC;KAAQ;IAEZ,qBACI,8OAAC;QAAQ,WAAU;QAAc,KAAK;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAG,WAAU;kCAAY;;;;;;;;;;;8BAG9B,8OAAC;oBAAI,WAAU;8BACV,KAAK,GAAG,CAAC,CAAC,KAAK,sBACZ,8OAAC;4BAEG,WAAW,CAAC,SAAS,EAAE,YAAY,QAAQ,WAAW,IAAI;4BAC1D,KAAK,CAAA,KAAM,YAAY,OAAO,CAAC,MAAM,GAAG;;8CAExC,8OAAC;oCACG,WAAU;oCACV,SAAS,IAAM,UAAU;;sDAEzB,8OAAC;sDAAI,IAAI,QAAQ;;;;;;sDACjB,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,MAAK;0DACjD,cAAA,8OAAC;oDACG,GAAE;oDACF,QAAO;oDACP,aAAY;oDACZ,eAAc;oDACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC;kDAAG,IAAI,MAAM;;;;;;;;;;;;2BAtBb;;;;;;;;;;8BA4BjB,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAO,WAAU;;oCAAc;kDAE5B,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;uCAEe", "debugId": null}}]}