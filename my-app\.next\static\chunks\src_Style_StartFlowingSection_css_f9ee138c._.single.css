/* [project]/src/Style/StartFlowingSection.css [app-client] (css) */
.start-flowing-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 50%, #fdba74 100%);
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  display: flex;
  position: relative;
  overflow: hidden;
}

.start-flowing-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.content-wrapper {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  min-height: 80vh;
  display: grid;
}

.hero-content {
  flex-direction: column;
  gap: 32px;
  display: flex;
}

.main-title {
  color: #92400e;
  text-shadow: 0 2px 4px #0000001a;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.highlight-text {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 500;
}

.subtitle {
  color: #78350f;
  text-shadow: 0 1px 2px #0000001a;
  margin: 0;
  font-size: 1.25rem;
  line-height: 1.6;
}

.cta-section {
  align-items: center;
  gap: 16px;
  display: flex;
}

.primary-cta {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 4px 15px #f59e0b4d;
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #f59e0b66;
}

.secondary-cta {
  color: #92400e;
  cursor: pointer;
  background: none;
  border: 2px solid #d97706;
  border-radius: 12px;
  padding: 14px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
}

.secondary-cta:hover {
  color: #fff;
  background: #d97706;
  transform: translateY(-2px);
}

.trust-indicators {
  align-items: center;
  gap: 40px;
  display: flex;
}

.trust-item {
  text-align: center;
  flex-direction: column;
  align-items: center;
  display: flex;
}

.trust-number {
  color: #92400e;
  margin-bottom: 4px;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.trust-label {
  color: #78350f;
  font-size: .875rem;
  font-weight: 500;
}

.hero-visual {
  justify-content: center;
  align-items: center;
  display: flex;
}

.demo-window {
  background: #fff;
  border: 1px solid #f59e0b33;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  overflow: hidden;
  box-shadow: 0 20px 50px #00000026;
}

.window-header {
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  display: flex;
}

.window-controls {
  gap: 8px;
  display: flex;
}

.control {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.control.red {
  background: #ef4444;
}

.control.yellow {
  background: #f59e0b;
}

.control.green {
  background: #10b981;
}

.window-title {
  color: #374151;
  font-size: .875rem;
  font-weight: 600;
}

.window-content {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  flex-direction: column;
  align-items: center;
  gap: 32px;
  padding: 40px;
  display: flex;
}

.voice-indicator {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.pulse-ring {
  border: 3px solid #f59e0b;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  animation: 2s ease-in-out infinite pulse;
  position: absolute;
}

.microphone-icon {
  z-index: 1;
  font-size: 2rem;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

.typing-demo {
  background: #fff;
  border-radius: 12px;
  width: 100%;
  padding: 24px;
  position: relative;
  box-shadow: 0 4px 15px #0000001a;
}

.demo-text {
  color: #374151;
  margin: 0;
  font-size: 1rem;
  font-style: italic;
  line-height: 1.6;
}

.cursor-blink {
  background: #f59e0b;
  width: 2px;
  height: 20px;
  margin-left: 4px;
  animation: 1s infinite blink;
  display: inline-block;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }

  51%, 100% {
    opacity: 0;
  }
}

@media (width <= 1024px) {
  .content-wrapper {
    gap: 60px;
  }

  .main-title {
    font-size: 3rem;
  }
}

@media (width <= 768px) {
  .start-flowing-section {
    min-height: auto;
    padding: 80px 0;
  }

  .start-flowing-container {
    padding: 0 20px;
  }

  .content-wrapper {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.125rem;
  }

  .cta-section {
    flex-wrap: wrap;
    justify-content: center;
  }

  .trust-indicators {
    justify-content: center;
    gap: 24px;
  }

  .demo-window {
    max-width: 400px;
  }

  .window-content {
    padding: 32px 24px;
  }
}

@media (width <= 480px) {
  .start-flowing-container {
    padding: 0 15px;
  }

  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .cta-section {
    flex-direction: column;
    gap: 12px;
  }

  .primary-cta, .secondary-cta {
    width: 100%;
    max-width: 280px;
  }

  .trust-indicators {
    gap: 16px;
  }

  .trust-number {
    font-size: 1.5rem;
  }

  .demo-window {
    max-width: 320px;
  }

  .window-content {
    gap: 24px;
    padding: 24px 16px;
  }
}

/*# sourceMappingURL=src_Style_StartFlowingSection_css_f9ee138c._.single.css.map*/