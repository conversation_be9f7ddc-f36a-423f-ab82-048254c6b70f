/* [project]/src/Style/StartFlowingSection.css [app-client] (css) */
.start-flowing-section {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 25%, #7c3aed 50%, #db2777 75%, #dc2626 100%);
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  display: flex;
  position: relative;
  overflow: hidden;
}

.background-overlay {
  z-index: 1;
  background: #0000004d;
  position: absolute;
  inset: 0;
}

.background-blur {
  backdrop-filter: blur(1px);
  z-index: 2;
  position: absolute;
  inset: 0;
}

.dotted-line-svg {
  z-index: 3;
  pointer-events: none;
  width: 60%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
}

.dotted-line {
  filter: drop-shadow(0 0 10px #ffffff4d);
}

.start-flowing-container {
  z-index: 4;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

.content-wrapper {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.main-title {
  color: #fff;
  text-shadow: 0 4px 20px #0000004d;
  margin: 0 0 24px;
  font-family: Georgia, serif;
  font-size: 4.5rem;
  font-weight: 400;
  line-height: 1.1;
  position: relative;
}

.title-dots {
  color: #ffffffb3;
  animation: 2s ease-in-out infinite dots-fade;
}

@keyframes dots-fade {
  0%, 100% {
    opacity: .7;
  }

  50% {
    opacity: .3;
  }
}

.subtitle {
  color: #ffffffe6;
  text-shadow: 0 2px 10px #0000004d;
  margin: 0 0 40px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.6;
}

.cta-buttons {
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
  display: flex;
}

.primary-cta {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px #f59e0b4d;
}

.primary-cta:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.primary-cta:hover:before {
  left: 100%;
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px #f59e0b66;
}

.secondary-cta {
  color: #fff;
  cursor: pointer;
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 2px solid #ffffff4d;
  border-radius: 50px;
  padding: 14px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
}

.secondary-cta:hover {
  background: #fff3;
  border-color: #ffffff80;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #ffffff1a;
}

.availability-text {
  color: #ffffffb3;
  text-shadow: 0 2px 10px #0000004d;
  margin: 0;
  font-size: .95rem;
}

@media (width <= 1024px) {
  .main-title {
    font-size: 3.5rem;
  }

  .subtitle {
    font-size: 1.125rem;
  }

  .dotted-line-svg {
    width: 70%;
  }
}

@media (width <= 768px) {
  .start-flowing-section {
    min-height: 90vh;
    padding: 60px 0;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    margin-bottom: 32px;
    font-size: 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .primary-cta, .secondary-cta {
    width: 100%;
    max-width: 280px;
    padding: 16px 24px;
    font-size: 1rem;
  }

  .dotted-line-svg {
    width: 80%;
  }

  .content-wrapper {
    max-width: 500px;
  }
}

@media (width <= 480px) {
  .start-flowing-container {
    padding: 0 15px;
  }

  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: .95rem;
  }

  .primary-cta, .secondary-cta {
    padding: 14px 20px;
    font-size: .95rem;
  }

  .availability-text {
    font-size: .875rem;
  }

  .dotted-line-svg {
    display: none;
  }
}

/*# sourceMappingURL=src_Style_StartFlowingSection_css_f9ee138c._.single.css.map*/