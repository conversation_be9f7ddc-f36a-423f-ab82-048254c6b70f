/* [project]/src/Style/Footer.css [app-client] (css) */
.footer-section {
  color: #fff;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  padding: 80px 0 0;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  flex-direction: column;
  display: flex;
}

.footer-main {
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  margin-bottom: 60px;
  display: grid;
}

.footer-brand {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.brand-logo {
  margin-bottom: 16px;
}

.brand-name {
  color: #fff;
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin: 0 0 8px;
  font-size: 2.5rem;
  font-weight: 700;
}

.brand-tagline {
  color: #9ca3af;
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.brand-description {
  color: #d1d5db;
  max-width: 400px;
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
}

.social-links {
  gap: 16px;
  display: flex;
}

.social-link {
  color: #d1d5db;
  background: #ffffff1a;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.social-link:hover {
  color: #fff;
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  transform: translateY(-2px);
}

.footer-links {
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  display: grid;
}

.link-group {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.link-title {
  color: #fff;
  margin: 0 0 8px;
  font-size: 1.125rem;
  font-weight: 600;
}

.link-list {
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.footer-link {
  color: #9ca3af;
  font-size: .95rem;
  text-decoration: none;
  transition: color .3s;
  position: relative;
}

.footer-link:hover {
  color: #f59e0b;
}

.footer-link:after {
  content: "";
  background: #f59e0b;
  width: 0;
  height: 1px;
  transition: width .3s;
  position: absolute;
  bottom: -2px;
  left: 0;
}

.footer-link:hover:after {
  width: 100%;
}

.footer-bottom {
  border-top: 1px solid #ffffff1a;
  padding: 32px 0;
}

.footer-divider {
  background: linear-gradient(90deg, #0000 0%, #f59e0b4d 50%, #0000 100%);
  width: 100%;
  height: 1px;
  margin-bottom: 32px;
}

.footer-bottom-content {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.copyright {
  color: #9ca3af;
  margin: 0;
  font-size: .875rem;
}

.footer-bottom-links {
  gap: 24px;
  display: flex;
}

.footer-bottom-link {
  color: #9ca3af;
  font-size: .875rem;
  text-decoration: none;
  transition: color .3s;
}

.footer-bottom-link:hover {
  color: #f59e0b;
}

@media (width <= 1024px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }
}

@media (width <= 768px) {
  .footer-section {
    padding: 60px 0 0;
  }

  .footer-container {
    padding: 0 20px;
  }

  .footer-main {
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }

  .brand-name {
    font-size: 2rem;
  }

  .brand-description {
    font-size: .95rem;
  }

  .footer-bottom-content {
    text-align: center;
    flex-direction: column;
    gap: 16px;
  }

  .footer-bottom-links {
    gap: 16px;
  }
}

@media (width <= 480px) {
  .footer-section {
    padding: 40px 0 0;
  }

  .footer-container {
    padding: 0 15px;
  }

  .footer-main {
    gap: 32px;
    margin-bottom: 32px;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .brand-name {
    font-size: 1.75rem;
  }

  .brand-description {
    font-size: .9rem;
  }

  .social-links {
    gap: 12px;
  }

  .social-link {
    width: 40px;
    height: 40px;
  }

  .link-title {
    font-size: 1rem;
  }

  .footer-link {
    font-size: .9rem;
  }

  .footer-bottom {
    padding: 24px 0;
  }

  .copyright, .footer-bottom-link {
    font-size: .8rem;
  }

  .footer-bottom-links {
    gap: 12px;
  }
}

/*# sourceMappingURL=src_Style_Footer_css_f9ee138c._.single.css.map*/