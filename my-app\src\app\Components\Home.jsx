import React from 'react';
import { Play, ArrowRight } from 'lucide-react';

const AudioStorytellingTemplate = () => {
    return (
        <div className="min-h-screen pt-10">
            <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    {/* Left Column - Images */}
                    <div className="space-y-4">
                        {/* Main Image */}
                        <div className="relative">
                            <img
                                src="https://images.unsplash.com/photo-1590736969955-71cc94901144?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                                alt="Woman podcasting"
                                className="w-full h-80 object-cover rounded-lg"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-30 rounded-lg flex items-center justify-center">
                                <button className="bg-white bg-opacity-90 hover:bg-opacity-100 p-4 rounded-full transition-all duration-200">
                                    <Play className="w-8 h-8 text-gray-800 ml-1" />
                                </button>
                            </div>
                            <div className="absolute bottom-4 left-4 text-white">
                                <span className="text-sm font-medium">Play this episode</span>
                            </div>
                        </div>

                        {/* Bottom Row */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Podcast Equipment Image */}
                            <div className="relative">
                                <img
                                    src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                                    alt="Podcast equipment"
                                    className="w-full h-40 object-cover rounded-lg"
                                />
                            </div>

                            {/* Info Card */}
                            <div className="bg-orange-50 p-6 rounded-lg">
                                <div className="flex items-start justify-between">
                                    <div>
                                        <p className="text-sm text-gray-600 mb-3">More than just a platform for your podcasts</p>

                                        {/* Profile Images */}
                                        <div className="flex -space-x-2 mb-3">
                                            <img
                                                src="https://images.unsplash.com/photo-1494790108755-2616b612b647?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
                                                alt="User 1"
                                                className="w-8 h-8 rounded-full border-2 border-white"
                                            />
                                            <img
                                                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
                                                alt="User 2"
                                                className="w-8 h-8 rounded-full border-2 border-white"
                                            />
                                            <img
                                                src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
                                                alt="User 3"
                                                className="w-8 h-8 rounded-full border-2 border-white"
                                            />
                                        </div>

                                        <div className="flex items-center">
                                            <span className="text-2xl font-bold text-gray-900">250k+</span>
                                        </div>
                                    </div>

                                    <button className="bg-white p-2 rounded-full shadow-sm hover:shadow-md transition-shadow">
                                        <ArrowRight className="w-4 h-4 text-gray-600" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Column - Content */}
                    <div className="space-y-6">
                        <div>
                            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                                Your destination for the best in audio storytelling
                            </h1>

                            <p className="text-gray-600 text-lg mb-8 leading-relaxed">
                                Founded with a <span className="text-blue-600 font-medium">passion for connecting people through shared experiences</span>, our platform is dedicated to bringing you the most <span className="text-blue-600 font-medium">engaging, informative</span>.
                            </p>

                            <button className="bg-white border border-gray-300 px-6 py-3 rounded-lg font-medium text-gray-800 hover:bg-gray-50 transition-colors">
                                Browse Episodes
                            </button>
                        </div>

                        {/* Stats Section */}
                        <div className="pt-8 border-t border-gray-200">
                            <p className="text-gray-600 mb-4">
                                With over <span className="font-bold text-gray-900">700 episodes</span> produced, we've crafted nearly <span className="font-bold text-gray-900">58,000 minutes</span> of audio content that inspires and informs.
                            </p>

                            <div className="flex items-center space-x-4">
                                <span className="text-gray-600">Listen on</span>

                                {/* Podcast Platform Icons */}
                                <div className="flex items-center space-x-3">
                                    {/* Apple Podcasts */}
                                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2c5.514 0 10 4.486 10 10s-4.486 10-10 10S2 17.514 2 12 6.486 2 12 2zm0 3c-3.866 0-7 3.134-7 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 2c2.761 0 5 2.239 5 5s-2.239 5-5 5-5-2.239-5-5 2.239-5 5-5zm0 2c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3-1.343-3-3-3z" />
                                        </svg>
                                    </div>

                                    {/* Google Podcasts */}
                                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-green-500 rounded-lg flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm-1 4h2v6h-2V4zm0 8h2v6h-2v-6zm-3-4h2v6H8v-6zm6 0h2v6h-2v-6z" />
                                        </svg>
                                    </div>

                                    {/* Spotify */}
                                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.779-.54 0-.36.18-.66.479-.781 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.242 1.022zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.301.421-1.02.599-1.559.3z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AudioStorytellingTemplate;