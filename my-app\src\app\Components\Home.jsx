"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/HomeSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const HomeSection = () => {
    const sectionRef = useRef(null);
    const contentRef = useRef(null);
    const statsRef = useRef(null);

    useEffect(() => {
        const section = sectionRef.current;
        const content = contentRef.current;
        const stats = statsRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([content, stats], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(content, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(stats, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4");

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="home-section" ref={sectionRef}>
            <div className="home-container">
                <div className="content-grid">
                    <div className="text-content" ref={contentRef}>
                        <div className="section-badge">
                            ✨ Powered by AI
                        </div>
                        <h2 className="section-title text-black" style={{color: 'black'}}>
                            Transform your voice into<br />
                            <span className="gradient-text">perfect text</span>
                        </h2>
                        <p className="section-description">
                            Experience the future of productivity with Flow's advanced AI voice dictation.
                            Speak naturally and watch your thoughts transform into polished, professional text
                            across any application.
                        </p>
                        <div className="feature-list">
                            <div className="feature-item">
                                <span className="feature-icon">🎯</span>
                                <span>99.9% accuracy with AI enhancement</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-icon">⚡</span>
                                <span>4x faster than traditional typing</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-icon">🔄</span>
                                <span>Real-time auto-corrections and formatting</span>
                            </div>
                        </div>
                    </div>

                    <div className="visual-content" ref={statsRef}>
                        <div className="stats-grid">
                            <div className="stat-card">
                                <div className="stat-number">4x</div>
                                <div className="stat-label">Faster than typing</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">99.9%</div>
                                <div className="stat-label">Accuracy rate</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">50+</div>
                                <div className="stat-label">Languages supported</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">1M+</div>
                                <div className="stat-label">Happy users</div>
                            </div>
                        </div>

                        <div className="demo-card">
                            <div className="demo-header">
                                <div className="demo-title">Live Demo</div>
                                <div className="demo-status">
                                    <span className="status-dot"></span>
                                    Recording
                                </div>
                            </div>
                            <div className="demo-content">
                                <div className="waveform">
                                    <div className="wave-bar"></div>
                                    <div className="wave-bar"></div>
                                    <div className="wave-bar"></div>
                                    <div className="wave-bar"></div>
                                    <div className="wave-bar"></div>
                                    <div className="wave-bar"></div>
                                    <div className="wave-bar"></div>
                                    <div className="wave-bar"></div>
                                </div>
                                <div className="demo-text">
                                    "Transform your voice into perfect text with Flow's AI-powered dictation..."
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default HomeSection;