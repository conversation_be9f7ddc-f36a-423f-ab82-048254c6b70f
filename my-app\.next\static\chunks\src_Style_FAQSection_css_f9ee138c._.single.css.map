{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/FAQSection.css"], "sourcesContent": ["/* FAQ Section */\n.faq-section {\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n}\n\n.faq-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.faq-header {\n  text-align: center;\n  margin-bottom: 60px;\n}\n\n.faq-title {\n  font-size: 3.5rem;\n  font-weight: 700;\n  color: #92400e;\n  margin: 0;\n  line-height: 1.1;\n  font-family: 'Georgia', serif;\n}\n\n.faq-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 24px;\n  margin-bottom: 80px;\n}\n\n.faq-item {\n  background: white;\n  border-radius: 16px;\n  border: 2px solid transparent;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.faq-item:hover {\n  border-color: #f59e0b;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.faq-item.active {\n  border-color: #f59e0b;\n  box-shadow: 0 8px 30px rgba(245, 158, 11, 0.2);\n}\n\n.faq-question {\n  padding: 24px;\n  cursor: pointer;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 16px;\n  transition: background-color 0.3s ease;\n}\n\n.faq-question:hover {\n  background-color: #fef3c7;\n}\n\n.faq-item.active .faq-question {\n  background-color: #fef3c7;\n  border-bottom: 1px solid #fde68a;\n}\n\n.faq-question h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n}\n\n.faq-icon {\n  color: #f59e0b;\n  transition: all 0.3s ease;\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: rgba(245, 158, 11, 0.1);\n}\n\n.faq-answer {\n  height: 0;\n  opacity: 0;\n  overflow: hidden;\n  transition: all 0.4s ease;\n}\n\n.faq-item.active .faq-answer {\n  height: auto;\n  opacity: 1;\n}\n\n.faq-answer p {\n  padding: 0 24px 24px 24px;\n  margin: 0;\n  font-size: 1rem;\n  line-height: 1.6;\n  color: #4b5563;\n}\n\n.faq-footer {\n  text-align: center;\n  background: rgba(255, 255, 255, 0.6);\n  padding: 48px 32px;\n  border-radius: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n.contact-support h3 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #92400e;\n  margin: 0 0 16px 0;\n}\n\n.contact-support p {\n  font-size: 1.125rem;\n  color: #78350f;\n  margin: 0 0 32px 0;\n  line-height: 1.6;\n}\n\n.contact-btn {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n}\n\n.contact-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);\n}\n\n.arrow {\n  transition: transform 0.3s ease;\n}\n\n.contact-btn:hover .arrow {\n  transform: translateX(4px);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .faq-title {\n    font-size: 3rem;\n  }\n  \n  .faq-grid {\n    gap: 20px;\n  }\n  \n  .contact-support h3 {\n    font-size: 1.75rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .faq-section {\n    padding: 80px 20px;\n  }\n  \n  .faq-title {\n    font-size: 2.5rem;\n  }\n  \n  .faq-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n  \n  .faq-question {\n    padding: 20px;\n  }\n  \n  .faq-question h3 {\n    font-size: 1rem;\n  }\n  \n  .faq-answer p {\n    padding: 0 20px 20px 20px;\n    font-size: 0.875rem;\n  }\n  \n  .faq-footer {\n    padding: 32px 24px;\n  }\n  \n  .contact-support h3 {\n    font-size: 1.5rem;\n  }\n  \n  .contact-support p {\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .faq-section {\n    padding: 60px 15px;\n  }\n  \n  .faq-title {\n    font-size: 2rem;\n  }\n  \n  .faq-question {\n    padding: 16px;\n  }\n  \n  .faq-question h3 {\n    font-size: 0.875rem;\n  }\n  \n  .faq-icon {\n    width: 28px;\n    height: 28px;\n  }\n  \n  .faq-answer p {\n    padding: 0 16px 16px 16px;\n    font-size: 0.8rem;\n  }\n  \n  .faq-footer {\n    padding: 24px 20px;\n  }\n  \n  .contact-support h3 {\n    font-size: 1.25rem;\n  }\n  \n  .contact-support p {\n    font-size: 0.875rem;\n  }\n  \n  .contact-btn {\n    padding: 14px 24px;\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAIA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}