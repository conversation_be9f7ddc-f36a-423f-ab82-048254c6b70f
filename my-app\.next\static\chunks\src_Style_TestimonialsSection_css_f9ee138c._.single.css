/* [project]/src/Style/TestimonialsSection.css [app-client] (css) */
.testimonials-section {
  background: #1a1a1a;
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
}

.section-title {
  color: #fff;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 4rem;
  font-weight: 400;
  line-height: 1.1;
}

.decorative-lines {
  pointer-events: none;
  width: 300px;
  height: 300px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.line {
  background: linear-gradient(90deg, #0000 0%, #10b981 50%, #0000 100%);
  border-radius: 2px;
  position: absolute;
}

.line-1 {
  width: 60px;
  height: 2px;
  animation: 3s ease-in-out infinite glow;
  top: 20%;
  left: 10%;
  transform: rotate(25deg);
}

.line-2 {
  width: 40px;
  height: 2px;
  animation: 3s ease-in-out .5s infinite glow;
  top: 30%;
  right: 15%;
  transform: rotate(-15deg);
}

.line-3 {
  width: 50px;
  height: 2px;
  animation: 3s ease-in-out 1s infinite glow;
  bottom: 25%;
  left: 20%;
  transform: rotate(45deg);
}

.line-4 {
  width: 35px;
  height: 2px;
  animation: 3s ease-in-out 1.5s infinite glow;
  bottom: 35%;
  right: 10%;
  transform: rotate(-30deg);
}

.line-5 {
  width: 45px;
  height: 2px;
  animation: 3s ease-in-out 2s infinite glow;
  top: 50%;
  left: 5%;
  transform: rotate(60deg);
}

.line-6 {
  width: 55px;
  height: 2px;
  animation: 3s ease-in-out 2.5s infinite glow;
  top: 60%;
  right: 5%;
  transform: rotate(-45deg);
}

@keyframes glow {
  0%, 100% {
    opacity: .3;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.testimonials-grid {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.testimonial-card {
  background: #fff;
  border-radius: 20px;
  padding: 24px;
  transition: all .3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px #0000001a;
}

.testimonial-card:before {
  content: "";
  opacity: 0;
  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);
  height: 3px;
  transition: opacity .3s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px #00000026;
}

.testimonial-card:hover:before {
  opacity: 1;
}

.card-header {
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  display: flex;
}

.avatar {
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  font-size: 1.5rem;
  display: flex;
}

.user-info {
  flex: 1;
}

.user-name {
  color: #1f2937;
  margin: 0 0 4px;
  font-size: 1rem;
  font-weight: 600;
}

.user-role {
  color: #6b7280;
  margin: 0;
  font-size: .875rem;
}

.testimonial-content {
  margin-bottom: 16px;
}

.testimonial-text {
  color: #374151;
  margin: 0;
  font-size: .95rem;
  font-style: italic;
  line-height: 1.6;
}

.rating {
  gap: 2px;
  display: flex;
}

.star {
  opacity: .3;
  font-size: 1rem;
  transition: opacity .2s;
}

.star.filled {
  opacity: 1;
}

.card-1, .card-2, .card-3, .card-4, .card-5, .card-6, .card-7 {
  grid-row: span 1;
}

@media (width <= 1024px) {
  .testimonials-section {
    padding: 100px 20px;
  }

  .section-title {
    font-size: 3.5rem;
  }

  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .decorative-lines {
    width: 250px;
    height: 250px;
  }
}

@media (width <= 768px) {
  .testimonials-section {
    min-height: auto;
    padding: 80px 20px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-header {
    margin-bottom: 60px;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .testimonial-card {
    padding: 20px;
  }

  .decorative-lines {
    width: 200px;
    height: 200px;
  }

  .line-1, .line-2, .line-3, .line-4, .line-5, .line-6 {
    width: 30px;
  }
}

@media (width <= 480px) {
  .testimonials-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .testimonial-card {
    padding: 16px;
  }

  .card-header {
    gap: 12px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .user-name {
    font-size: .9rem;
  }

  .user-role {
    font-size: .8rem;
  }

  .testimonial-text {
    font-size: .9rem;
  }

  .decorative-lines {
    display: none;
  }
}

/*# sourceMappingURL=src_Style_TestimonialsSection_css_f9ee138c._.single.css.map*/