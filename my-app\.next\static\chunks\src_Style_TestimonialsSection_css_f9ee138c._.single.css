/* [project]/src/Style/TestimonialsSection.css [app-client] (css) */
.testimonials-section {
  background: #1a1a1a;
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
}

.section-title {
  color: #fff;
  text-shadow: 0 4px 20px #0000004d;
  margin: 0 0 16px;
  font-family: Georgia, serif;
  font-size: 4rem;
  font-weight: 400;
  line-height: 1.1;
}

.section-subtitle {
  color: #fffc;
  text-align: center;
  text-shadow: 0 2px 10px #0000004d;
  margin: 0;
  font-size: 1.25rem;
}

.carousel-container {
  margin-top: 60px;
  position: relative;
}

.carousel-wrapper {
  border-radius: 20px;
  overflow: hidden;
}

.carousel-track {
  will-change: transform;
  gap: 24px;
  transition: transform .6s cubic-bezier(.25, .46, .45, .94);
  display: flex;
}

.carousel-controls {
  pointer-events: none;
  z-index: 10;
  justify-content: space-between;
  width: 100%;
  display: flex;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.carousel-btn {
  color: #1f2937;
  cursor: pointer;
  pointer-events: all;
  backdrop-filter: blur(10px);
  background: #ffffffe6;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 1.5rem;
  font-weight: bold;
  transition: all .3s;
  box-shadow: 0 4px 20px #0003;
}

.carousel-btn:hover {
  background: #fff;
  transform: scale(1.1);
  box-shadow: 0 6px 25px #0000004d;
}

.carousel-btn:active {
  transform: scale(.95);
}

.prev-btn {
  margin-left: -25px;
}

.next-btn {
  margin-right: -25px;
}

.carousel-dots {
  justify-content: center;
  gap: 12px;
  margin-top: 40px;
  display: flex;
}

.dot {
  cursor: pointer;
  background: #ffffff4d;
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s;
}

.dot.active {
  background: #f59e0b;
  transform: scale(1.2);
}

.dot:hover {
  background: #fff9;
}

.testimonial-card {
  background: #fff;
  border-radius: 20px;
  flex-direction: column;
  flex-shrink: 0;
  justify-content: space-between;
  height: auto;
  min-height: 300px;
  margin-right: 24px;
  padding: 32px;
  transition: all .3s;
  display: flex;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 30px #00000026;
}

.testimonial-card:before {
  content: "";
  opacity: 0;
  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);
  height: 3px;
  transition: opacity .3s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px #00000026;
}

.testimonial-card:hover:before {
  opacity: 1;
}

.card-header {
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  display: flex;
}

.avatar {
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  font-size: 1.5rem;
  display: flex;
}

.user-info {
  flex: 1;
}

.user-name {
  color: #1f2937;
  margin: 0 0 4px;
  font-size: 1rem;
  font-weight: 600;
}

.user-role {
  color: #6b7280;
  margin: 0 0 4px;
  font-size: .875rem;
}

.user-company {
  color: #9ca3af;
  margin: 0;
  font-size: .75rem;
  font-style: italic;
}

.testimonial-content {
  margin-bottom: 16px;
}

.testimonial-text {
  color: #374151;
  margin: 0;
  font-size: .95rem;
  font-style: italic;
  line-height: 1.6;
}

.rating {
  gap: 2px;
  display: flex;
}

.star {
  opacity: .3;
  font-size: 1rem;
  transition: opacity .2s;
}

.star.filled {
  opacity: 1;
}

.card-1, .card-2, .card-3, .card-4, .card-5, .card-6, .card-7 {
  grid-row: span 1;
}

@media (width <= 1024px) {
  .testimonials-section {
    padding: 100px 20px;
  }

  .section-title {
    font-size: 3.5rem;
  }

  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .decorative-lines {
    width: 250px;
    height: 250px;
  }

  .testimonial-card {
    min-height: 280px;
    padding: 28px;
  }

  .carousel-btn {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }
}

@media (width <= 768px) {
  .testimonials-section {
    min-height: auto;
    padding: 80px 20px;
  }

  .testimonials-container {
    padding: 0 20px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .testimonial-card {
    min-height: 250px;
    margin-right: 16px;
    padding: 24px;
  }

  .carousel-controls {
    display: none;
  }

  .carousel-dots {
    margin-top: 24px;
  }

  .carousel-track {
    gap: 16px;
  }
}

@media (width <= 480px) {
  .testimonial-card {
    min-height: 220px;
    padding: 20px;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .testimonials-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .testimonial-card {
    padding: 16px;
  }

  .card-header {
    gap: 12px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .user-name {
    font-size: .9rem;
  }

  .user-role {
    font-size: .8rem;
  }

  .testimonial-text {
    font-size: .9rem;
  }

  .decorative-lines {
    display: none;
  }
}

/*# sourceMappingURL=src_Style_TestimonialsSection_css_f9ee138c._.single.css.map*/