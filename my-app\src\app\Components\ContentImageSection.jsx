"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/ContentImageSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const ContentImageSection = () => {
    const sectionRef = useRef(null);
    const contentRef = useRef(null);
    const imageRef = useRef(null);
    const featuresRef = useRef([]);

    const features = [
        {
            icon: "🎯",
            title: "Precision & Accuracy",
            description: "Advanced AI algorithms ensure your voice is converted to text with remarkable precision and context awareness."
        },
        {
            icon: "⚡",
            title: "Lightning Fast",
            description: "Real-time processing means your thoughts become text instantly, keeping up with your natural speaking pace."
        },
        {
            icon: "🌐",
            title: "Multi-Language Support",
            description: "Communicate in over 100 languages with seamless translation and localization capabilities."
        },
        {
            icon: "🔒",
            title: "Privacy First",
            description: "Your data stays secure with end-to-end encryption and local processing options for sensitive content."
        }
    ];

    useEffect(() => {
        const section = sectionRef.current;
        const content = contentRef.current;
        const image = imageRef.current;
        const featureElements = featuresRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([content, image], { opacity: 0, y: 50 });
        gsap.set(featureElements, { opacity: 0, x: -30 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(content, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(image, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4")
        .to(featureElements, {
            opacity: 1,
            x: 0,
            duration: 0.6,
            stagger: 0.1,
            ease: "power3.out"
        }, "-=0.4");

        // Parallax effect for image
        gsap.to(image, {
            y: -20,
            scrollTrigger: {
                trigger: section,
                start: "top bottom",
                end: "bottom top",
                scrub: 1
            }
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="content-image-section" ref={sectionRef}>
            <div className="content-image-container">
                <div className="content-side" ref={contentRef}>
                    <div className="content-header">
                        <span className="content-badge">Why Choose Flow</span>
                        <h2 className="content-title">
                            Transform Your Voice Into 
                            <span className="highlight"> Powerful Content</span>
                        </h2>
                        <p className="content-description">
                            Experience the future of content creation with our advanced voice-to-text 
                            technology. Whether you're writing emails, creating documents, or brainstorming 
                            ideas, Flow makes it effortless and natural.
                        </p>
                    </div>

                    <div className="features-grid">
                        {features.map((feature, index) => (
                            <div 
                                key={index}
                                className="feature-item"
                                ref={el => featuresRef.current[index] = el}
                            >
                                <div className="feature-icon">{feature.icon}</div>
                                <div className="feature-content">
                                    <h3 className="feature-title">{feature.title}</h3>
                                    <p className="feature-description">{feature.description}</p>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="content-actions">
                        <button className="primary-btn">Start Free Trial</button>
                        <button className="secondary-btn">Watch Demo</button>
                    </div>
                </div>

                <div className="image-side" ref={imageRef}>
                    <div className="image-container">
                        <div className="main-device">
                            <div className="device-screen">
                                <div className="screen-header">
                                    <div className="screen-dots">
                                        <span className="dot red"></span>
                                        <span className="dot yellow"></span>
                                        <span className="dot green"></span>
                                    </div>
                                    <div className="screen-title">Flow - Voice to Text</div>
                                </div>
                                <div className="screen-content">
                                    <div className="voice-wave">
                                        <div className="wave-bar"></div>
                                        <div className="wave-bar"></div>
                                        <div className="wave-bar"></div>
                                        <div className="wave-bar"></div>
                                        <div className="wave-bar"></div>
                                        <div className="wave-bar"></div>
                                        <div className="wave-bar"></div>
                                    </div>
                                    <div className="text-output">
                                        <div className="typing-text">
                                            <span>Hello, this is a demonstration of Flow's</span>
                                            <span className="cursor">|</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {/* <div className="floating-elements">
                            <div className="floating-card card-1">
                                <div className="card-icon">🎤</div>
                                <div className="card-text">Voice Input</div>
                            </div>
                            <div className="floating-card card-2">
                                <div className="card-icon">📝</div>
                                <div className="card-text">Text Output</div>
                            </div>
                            <div className="floating-card card-3">
                                <div className="card-icon">⚡</div>
                                <div className="card-text">Real-time</div>
                            </div>
                        </div> */}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default ContentImageSection;
