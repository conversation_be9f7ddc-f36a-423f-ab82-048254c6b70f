{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Marquee.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport \"@/Style/Marquee.css\";\r\n\r\nconst Marquee = ({\r\n    items = [\"Creative\", \"Design\", \"Development\", \"Innovation\", \"Excellence\", \"Quality\", \"Performance\", \"Success\"],\r\n    speed = 1, // speed multiplier\r\n    colors = [\"#ff6b6b\", \"#4ecdc4\", \"#45b7d1\", \"#96ceb4\", \"#feca57\", \"#ff9ff3\", \"#54a0ff\", \"#5f27cd\"]\r\n}) => {\r\n    const wrapperRef = useRef(null);\r\n    const animationRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const wrapper = wrapperRef.current;\r\n        if (!wrapper) return;\r\n\r\n        const marqueeItems = wrapper.querySelectorAll(\".marquee-item\");\r\n\r\n        // Apply background colors\r\n        gsap.set(marqueeItems, {\r\n            backgroundColor: gsap.utils.wrap(colors),\r\n        });\r\n\r\n        // Create infinite scroll animation\r\n        const createAnimation = () => {\r\n            if (marqueeItems.length === 0) return;\r\n\r\n            // Calculate the total width of one set of items\r\n            let totalWidth = 0;\r\n            const itemCount = items.length; // Original items count\r\n\r\n            for (let i = 0; i < itemCount; i++) {\r\n                if (marqueeItems[i]) {\r\n                    totalWidth += marqueeItems[i].offsetWidth + 20; // 20px margin\r\n                }\r\n            }\r\n\r\n            // Animate the wrapper to move left infinitely\r\n            animationRef.current = gsap.to(wrapper, {\r\n                x: -totalWidth,\r\n                duration: totalWidth / (50 * speed), // Adjust duration based on speed\r\n                ease: \"none\",\r\n                repeat: -1,\r\n            });\r\n        };\r\n\r\n        // Wait for layout to be ready\r\n        const timer = setTimeout(createAnimation, 200);\r\n\r\n        return () => {\r\n            clearTimeout(timer);\r\n            if (animationRef.current) {\r\n                animationRef.current.kill();\r\n            }\r\n        };\r\n    }, [items, speed, colors]);\r\n    return (\r\n        <div className=\"marquee-container\">\r\n            <div className=\"marquee-wrapper\" ref={wrapperRef}>\r\n                {/* Duplicate items for seamless infinite loop */}\r\n                {[...items, ...items].map((item, index) => (\r\n                    <div key={index} className=\"marquee-item\">\r\n                        <div className=\"marquee-content\">\r\n                            {item}\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Marquee;"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;;AAKA,MAAM,UAAU,CAAC,EACb,QAAQ;IAAC;IAAY;IAAU;IAAe;IAAc;IAAc;IAAW;IAAe;CAAU,EAC9G,QAAQ,CAAC,EACT,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU,EACpG;;IACG,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,CAAC,SAAS;YAEd,MAAM,eAAe,QAAQ,gBAAgB,CAAC;YAE9C,0BAA0B;YAC1B,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,cAAc;gBACnB,iBAAiB,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACrC;YAEA,mCAAmC;YACnC,MAAM;qDAAkB;oBACpB,IAAI,aAAa,MAAM,KAAK,GAAG;oBAE/B,gDAAgD;oBAChD,IAAI,aAAa;oBACjB,MAAM,YAAY,MAAM,MAAM,EAAE,uBAAuB;oBAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;wBAChC,IAAI,YAAY,CAAC,EAAE,EAAE;4BACjB,cAAc,YAAY,CAAC,EAAE,CAAC,WAAW,GAAG,IAAI,cAAc;wBAClE;oBACJ;oBAEA,8CAA8C;oBAC9C,aAAa,OAAO,GAAG,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;wBACpC,GAAG,CAAC;wBACJ,UAAU,aAAa,CAAC,KAAK,KAAK;wBAClC,MAAM;wBACN,QAAQ,CAAC;oBACb;gBACJ;;YAEA,8BAA8B;YAC9B,MAAM,QAAQ,WAAW,iBAAiB;YAE1C;qCAAO;oBACH,aAAa;oBACb,IAAI,aAAa,OAAO,EAAE;wBACtB,aAAa,OAAO,CAAC,IAAI;oBAC7B;gBACJ;;QACJ;4BAAG;QAAC;QAAO;QAAO;KAAO;IACzB,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;YAAkB,KAAK;sBAEjC;mBAAI;mBAAU;aAAM,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;oBAAgB,WAAU;8BACvB,cAAA,6LAAC;wBAAI,WAAU;kCACV;;;;;;mBAFC;;;;;;;;;;;;;;;AAS9B;GAlEM;KAAA;uCAoES", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/FeaturesShowcase.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/FeaturesShowcase.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst FeaturesShowcase = () => {\n    const containerRef = useRef(null);\n    const cardsRef = useRef([]);\n\n    useEffect(() => {\n        const cards = cardsRef.current;\n        \n        // Initial animation for cards\n        gsap.fromTo(cards, \n            {\n                opacity: 0,\n                y: 50,\n                scale: 0.9\n            },\n            {\n                opacity: 1,\n                y: 0,\n                scale: 1,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: containerRef.current,\n                    start: \"top 80%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            }\n        );\n\n        // Hover animations\n        cards.forEach((card, index) => {\n            if (card) {\n                card.addEventListener('mouseenter', () => {\n                    gsap.to(card, {\n                        scale: 1.05,\n                        y: -10,\n                        duration: 0.3,\n                        ease: \"power2.out\"\n                    });\n                });\n\n                card.addEventListener('mouseleave', () => {\n                    gsap.to(card, {\n                        scale: 1,\n                        y: 0,\n                        duration: 0.3,\n                        ease: \"power2.out\"\n                    });\n                });\n            }\n        });\n\n        return () => {\n            cards.forEach(card => {\n                if (card) {\n                    card.removeEventListener('mouseenter', () => {});\n                    card.removeEventListener('mouseleave', () => {});\n                }\n            });\n        };\n    }, []);\n\n    const features = [\n        {\n            id: 1,\n            title: \"AI Auto Edits\",\n            description: \"Capture naturally and have interactive voice assistants that work seamlessly. Enhance your content with AI-powered editing tools.\",\n            image: \"/api/placeholder/300/400\",\n            bgColor: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 2,\n            title: \"Personal dictionary\",\n            description: \"Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 3,\n            title: \"Different tones for each app\",\n            description: \"Adapt your communication style with different tones optimized for each application and context.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 4,\n            title: \"100+ languages\",\n            description: \"Communicate globally with support for over 100 languages and seamless translation capabilities.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            textColor: \"#ffffff\"\n        }\n    ];\n\n    return (\n        <section className=\"features-showcase\" ref={containerRef}>\n            <div className=\"features-container\">\n                <div className=\"features-header\">\n                    <h2 className=\"features-title\">Powerful Features</h2>\n                    <p className=\"features-subtitle\">\n                        Discover the advanced capabilities that make our platform unique\n                    </p>\n                </div>\n                \n                <div className=\"features-grid\">\n                    {features.map((feature, index) => (\n                        <div\n                            key={feature.id}\n                            className={`feature-card ${index === 0 ? 'feature-large' : ''}`}\n                            ref={el => cardsRef.current[index] = el}\n                            style={{ \n                                background: feature.bgColor,\n                                color: feature.textColor \n                            }}\n                        >\n                            <div className=\"feature-content\">\n                                <div className=\"feature-image\">\n                                    <div className=\"phone-mockup\">\n                                        {index === 0 && (\n                                            <div className=\"ai-interface\">\n                                                <div className=\"ai-suggestions\">\n                                                    <div className=\"suggestion-item\">✨ Enhance clarity</div>\n                                                    <div className=\"suggestion-item\">🎯 Improve tone</div>\n                                                    <div className=\"suggestion-item\">📝 Fix grammar</div>\n                                                </div>\n                                                <div className=\"ai-controls\">\n                                                    <button className=\"ai-btn\">Apply</button>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 1 && (\n                                            <div className=\"dictionary-interface\">\n                                                <div className=\"dict-header\">Your Dictionary</div>\n                                                <div className=\"dict-items\">\n                                                    <div className=\"dict-item\">Technical terms</div>\n                                                    <div className=\"dict-item\">Brand names</div>\n                                                    <div className=\"dict-item\">Custom phrases</div>\n                                                    <div className=\"dict-item\">Abbreviations</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 2 && (\n                                            <div className=\"tones-interface\">\n                                                <div className=\"app-selector\">\n                                                    <div className=\"app-icon\">📧</div>\n                                                    <div className=\"app-icon active\">💬</div>\n                                                    <div className=\"app-icon\">📱</div>\n                                                </div>\n                                                <div className=\"tone-options\">\n                                                    <div className=\"tone-btn\">Professional</div>\n                                                    <div className=\"tone-btn active\">Casual</div>\n                                                    <div className=\"tone-btn\">Friendly</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 3 && (\n                                            <div className=\"languages-interface\">\n                                                <div className=\"lang-wheel\">\n                                                    <div className=\"lang-item\">EN</div>\n                                                    <div className=\"lang-item\">ES</div>\n                                                    <div className=\"lang-item\">FR</div>\n                                                    <div className=\"lang-item\">DE</div>\n                                                    <div className=\"lang-item\">ZH</div>\n                                                    <div className=\"lang-item\">JA</div>\n                                                    <div className=\"lang-center\">100+</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                    </div>\n                                </div>\n                                \n                                <div className=\"feature-text\">\n                                    <h3 className=\"feature-title\">{feature.title}</h3>\n                                    <p className=\"feature-description\">{feature.description}</p>\n                                </div>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default FeaturesShowcase;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,gCAAgC;AAChC,wCAAmC;IAC/B,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACrC;AAEA,MAAM,mBAAmB;;IACrB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,MAAM,QAAQ,SAAS,OAAO;YAE9B,8BAA8B;YAC9B,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACR;gBACI,SAAS;gBACT,GAAG;gBACH,OAAO;YACX,GACA;gBACI,SAAS;gBACT,GAAG;gBACH,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACX,SAAS,aAAa,OAAO;oBAC7B,OAAO;oBACP,eAAe;gBACnB;YACJ;YAGJ,mBAAmB;YACnB,MAAM,OAAO;8CAAC,CAAC,MAAM;oBACjB,IAAI,MAAM;wBACN,KAAK,gBAAgB,CAAC;0DAAc;gCAChC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oCACV,OAAO;oCACP,GAAG,CAAC;oCACJ,UAAU;oCACV,MAAM;gCACV;4BACJ;;wBAEA,KAAK,gBAAgB,CAAC;0DAAc;gCAChC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oCACV,OAAO;oCACP,GAAG;oCACH,UAAU;oCACV,MAAM;gCACV;4BACJ;;oBACJ;gBACJ;;YAEA;8CAAO;oBACH,MAAM,OAAO;sDAAC,CAAA;4BACV,IAAI,MAAM;gCACN,KAAK,mBAAmB,CAAC;kEAAc,KAAO;;gCAC9C,KAAK,mBAAmB,CAAC;kEAAc,KAAO;;4BAClD;wBACJ;;gBACJ;;QACJ;qCAAG,EAAE;IAEL,MAAM,WAAW;QACb;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;KACH;IAED,qBACI,6LAAC;QAAQ,WAAU;QAAoB,KAAK;kBACxC,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAAiB;;;;;;sCAC/B,6LAAC;4BAAE,WAAU;sCAAoB;;;;;;;;;;;;8BAKrC,6LAAC;oBAAI,WAAU;8BACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,6LAAC;4BAEG,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,kBAAkB,IAAI;4BAC/D,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;4BACrC,OAAO;gCACH,YAAY,QAAQ,OAAO;gCAC3B,OAAO,QAAQ,SAAS;4BAC5B;sCAEA,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;gDACV,UAAU,mBACP,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;;;;;;;sEAErC,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAO,WAAU;0EAAS;;;;;;;;;;;;;;;;;gDAItC,UAAU,mBACP,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,6LAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,6LAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,6LAAC;oEAAI,WAAU;8EAAY;;;;;;;;;;;;;;;;;;gDAItC,UAAU,mBACP,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAW;;;;;;8EAC1B,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAW;;;;;;;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAW;;;;;;8EAC1B,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAW;;;;;;;;;;;;;;;;;;gDAIrC,UAAU,mBACP,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOjD,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAAiB,QAAQ,KAAK;;;;;;0DAC5C,6LAAC;gDAAE,WAAU;0DAAuB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2BAlE1D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AA2E3C;GAzLM;KAAA;uCA2LS", "debugId": null}}]}