{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Marquee.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport \"@/Style/Marquee.css\";\r\n\r\nconst Marquee = ({\r\n    items = [\"Creative\", \"Design\", \"Development\", \"Innovation\", \"Excellence\", \"Quality\", \"Performance\", \"Success\"],\r\n    speed = 1,\r\n    direction = \"left\",\r\n    pauseOnHover = true,\r\n    colors = [\"#ff6b6b\", \"#4ecdc4\", \"#45b7d1\", \"#96ceb4\", \"#feca57\", \"#ff9ff3\", \"#54a0ff\", \"#5f27cd\"]\r\n}) => {\r\n    const wrapperRef = useRef(null);\r\n    const loopRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const boxes = gsap.utils.toArray(\".marquee-item\");\r\n\r\n        // Apply background colors and initial animations\r\n        gsap.set(boxes, {\r\n            backgroundColor: gsap.utils.wrap(colors),\r\n            scale: 1,\r\n            rotation: 0,\r\n        });\r\n\r\n        // Add hover effects\r\n        boxes.forEach((box) => {\r\n            const handleMouseEnter = () => {\r\n                gsap.to(box, {\r\n                    scale: 1.1,\r\n                    rotation: 5,\r\n                    duration: 0.3,\r\n                    ease: \"power2.out\"\r\n                });\r\n                if (pauseOnHover && loopRef.current) {\r\n                    loopRef.current.pause();\r\n                }\r\n            };\r\n\r\n            const handleMouseLeave = () => {\r\n                gsap.to(box, {\r\n                    scale: 1,\r\n                    rotation: 0,\r\n                    duration: 0.3,\r\n                    ease: \"power2.out\"\r\n                });\r\n                if (pauseOnHover && loopRef.current) {\r\n                    loopRef.current.resume();\r\n                }\r\n            };\r\n\r\n            box.addEventListener('mouseenter', handleMouseEnter);\r\n            box.addEventListener('mouseleave', handleMouseLeave);\r\n        });\r\n\r\n        // Call horizontalLoop with the boxes\r\n        loopRef.current = horizontalLoop(boxes, {\r\n            paused: false,\r\n            repeat: -1,\r\n            speed: speed,\r\n            reversed: direction === \"right\",\r\n        });\r\n\r\n        return () => {\r\n            loopRef.current?.kill();\r\n            // Clean up event listeners\r\n            boxes.forEach((box) => {\r\n                box.removeEventListener('mouseenter', () => {});\r\n                box.removeEventListener('mouseleave', () => {});\r\n            });\r\n        };\r\n    }, [colors, speed, direction, pauseOnHover]);\r\n    function horizontalLoop(items, config) {\r\n        items = gsap.utils.toArray(items);\r\n        config = config || {};\r\n        let tl = gsap.timeline({\r\n            repeat: config.repeat,\r\n            paused: config.paused,\r\n            defaults: { ease: \"none\" },\r\n            onReverseComplete: () => tl.totalTime(tl.rawTime() + tl.duration() * 100),\r\n        }),\r\n            length = items.length,\r\n            startX = items[0].offsetLeft,\r\n            times = [],\r\n            widths = [],\r\n            xPercents = [],\r\n            curIndex = 0,\r\n            pixelsPerSecond = (config.speed || 1) * 100,\r\n            snap = config.snap === false\r\n                ? (v) => v\r\n                : gsap.utils.snap(config.snap || 1),\r\n            totalWidth,\r\n            curX,\r\n            distanceToStart,\r\n            distanceToLoop,\r\n            item,\r\n            i;\r\n\r\n        gsap.set(items, {\r\n            xPercent: (i, el) => {\r\n                let w = (widths[i] = parseFloat(gsap.getProperty(el, \"width\", \"px\")));\r\n                xPercents[i] = snap(\r\n                    (parseFloat(gsap.getProperty(el, \"x\", \"px\")) / w) * 100 +\r\n                    gsap.getProperty(el, \"xPercent\")\r\n                );\r\n                return xPercents[i];\r\n            },\r\n        });\r\n        gsap.set(items, { x: 0 });\r\n        totalWidth =\r\n            items[length - 1].offsetLeft +\r\n            (xPercents[length - 1] / 100) * widths[length - 1] -\r\n            startX +\r\n            items[length - 1].offsetWidth * gsap.getProperty(items[length - 1], \"scaleX\") +\r\n            (parseFloat(config.paddingRight) || 0);\r\n\r\n        for (i = 0; i < length; i++) {\r\n            item = items[i];\r\n            curX = (xPercents[i] / 100) * widths[i];\r\n            distanceToStart = item.offsetLeft + curX - startX;\r\n            distanceToLoop = distanceToStart + widths[i] * gsap.getProperty(item, \"scaleX\");\r\n\r\n            tl.to(\r\n                item,\r\n                {\r\n                    xPercent: snap(((curX - distanceToLoop) / widths[i]) * 100),\r\n                    duration: distanceToLoop / pixelsPerSecond,\r\n                },\r\n                0\r\n            )\r\n                .fromTo(\r\n                    item,\r\n                    {\r\n                        xPercent: snap(\r\n                            ((curX - distanceToLoop + totalWidth) / widths[i]) * 100\r\n                        ),\r\n                    },\r\n                    {\r\n                        xPercent: xPercents[i],\r\n                        duration:\r\n                            (curX - distanceToLoop + totalWidth - curX) / pixelsPerSecond,\r\n                        immediateRender: false,\r\n                    },\r\n                    distanceToLoop / pixelsPerSecond\r\n                )\r\n                .add(\"label\" + i, distanceToStart / pixelsPerSecond);\r\n\r\n            times[i] = distanceToStart / pixelsPerSecond;\r\n        }\r\n\r\n        function toIndex(index, vars) {\r\n            vars = vars || {};\r\n            if (Math.abs(index - curIndex) > length / 2) {\r\n                index += index > curIndex ? -length : length;\r\n            }\r\n            let newIndex = gsap.utils.wrap(0, length, index),\r\n                time = times[newIndex];\r\n            if ((time > tl.time()) !== index > curIndex) {\r\n                vars.modifiers = { time: gsap.utils.wrap(0, tl.duration()) };\r\n                time += tl.duration() * (index > curIndex ? 1 : -1);\r\n            }\r\n            curIndex = newIndex;\r\n            vars.overwrite = true;\r\n            return tl.tweenTo(time, vars);\r\n        }\r\n\r\n        tl.next = (vars) => toIndex(curIndex + 1, vars);\r\n        tl.previous = (vars) => toIndex(curIndex - 1, vars);\r\n        tl.current = () => curIndex;\r\n        tl.toIndex = (index, vars) => toIndex(index, vars);\r\n        tl.times = times;\r\n        tl.progress(1, true).progress(0, true);\r\n        if (config.reversed) {\r\n            tl.vars.onReverseComplete();\r\n            tl.reverse();\r\n        }\r\n        return tl;\r\n    }\r\n\r\n\r\n\r\n    return (\r\n        <div className=\"marquee-container\">\r\n            <div className=\"wrapper\" ref={wrapperRef}>\r\n                {items.map((item, index) => (\r\n                    <div key={index} className=\"marquee-item\">\r\n                        <div className=\"marquee-content\">\r\n                            {item}\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Marquee;"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;;AAKA,MAAM,UAAU,CAAC,EACb,QAAQ;IAAC;IAAY;IAAU;IAAe;IAAc;IAAc;IAAW;IAAe;CAAU,EAC9G,QAAQ,CAAC,EACT,YAAY,MAAM,EAClB,eAAe,IAAI,EACnB,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU,EACpG;IACG,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QAEjC,iDAAiD;QACjD,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;YACZ,iBAAiB,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACjC,OAAO;YACP,UAAU;QACd;QAEA,oBAAoB;QACpB,MAAM,OAAO,CAAC,CAAC;YACX,MAAM,mBAAmB;gBACrB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,KAAK;oBACT,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,MAAM;gBACV;gBACA,IAAI,gBAAgB,QAAQ,OAAO,EAAE;oBACjC,QAAQ,OAAO,CAAC,KAAK;gBACzB;YACJ;YAEA,MAAM,mBAAmB;gBACrB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,KAAK;oBACT,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,MAAM;gBACV;gBACA,IAAI,gBAAgB,QAAQ,OAAO,EAAE;oBACjC,QAAQ,OAAO,CAAC,MAAM;gBAC1B;YACJ;YAEA,IAAI,gBAAgB,CAAC,cAAc;YACnC,IAAI,gBAAgB,CAAC,cAAc;QACvC;QAEA,qCAAqC;QACrC,QAAQ,OAAO,GAAG,eAAe,OAAO;YACpC,QAAQ;YACR,QAAQ,CAAC;YACT,OAAO;YACP,UAAU,cAAc;QAC5B;QAEA,OAAO;YACH,QAAQ,OAAO,EAAE;YACjB,2BAA2B;YAC3B,MAAM,OAAO,CAAC,CAAC;gBACX,IAAI,mBAAmB,CAAC,cAAc,KAAO;gBAC7C,IAAI,mBAAmB,CAAC,cAAc,KAAO;YACjD;QACJ;IACJ,GAAG;QAAC;QAAQ;QAAO;QAAW;KAAa;IAC3C,SAAS,eAAe,KAAK,EAAE,MAAM;QACjC,QAAQ,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QAC3B,SAAS,UAAU,CAAC;QACpB,IAAI,KAAK,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACnB,QAAQ,OAAO,MAAM;YACrB,QAAQ,OAAO,MAAM;YACrB,UAAU;gBAAE,MAAM;YAAO;YACzB,mBAAmB,IAAM,GAAG,SAAS,CAAC,GAAG,OAAO,KAAK,GAAG,QAAQ,KAAK;QACzE,IACI,SAAS,MAAM,MAAM,EACrB,SAAS,KAAK,CAAC,EAAE,CAAC,UAAU,EAC5B,QAAQ,EAAE,EACV,SAAS,EAAE,EACX,YAAY,EAAE,EACd,WAAW,GACX,kBAAkB,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,KACxC,OAAO,OAAO,IAAI,KAAK,QACjB,CAAC,IAAM,IACP,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,IACrC,YACA,MACA,iBACA,gBACA,MACA;QAEJ,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;YACZ,UAAU,CAAC,GAAG;gBACV,IAAI,IAAK,MAAM,CAAC,EAAE,GAAG,WAAW,6IAAA,CAAA,OAAI,CAAC,WAAW,CAAC,IAAI,SAAS;gBAC9D,SAAS,CAAC,EAAE,GAAG,KACX,AAAC,WAAW,6IAAA,CAAA,OAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,IAAK,MACpD,6IAAA,CAAA,OAAI,CAAC,WAAW,CAAC,IAAI;gBAEzB,OAAO,SAAS,CAAC,EAAE;YACvB;QACJ;QACA,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;YAAE,GAAG;QAAE;QACvB,aACI,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,GAC5B,AAAC,SAAS,CAAC,SAAS,EAAE,GAAG,MAAO,MAAM,CAAC,SAAS,EAAE,GAClD,SACA,KAAK,CAAC,SAAS,EAAE,CAAC,WAAW,GAAG,6IAAA,CAAA,OAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,YACpE,CAAC,WAAW,OAAO,YAAY,KAAK,CAAC;QAEzC,IAAK,IAAI,GAAG,IAAI,QAAQ,IAAK;YACzB,OAAO,KAAK,CAAC,EAAE;YACf,OAAO,AAAC,SAAS,CAAC,EAAE,GAAG,MAAO,MAAM,CAAC,EAAE;YACvC,kBAAkB,KAAK,UAAU,GAAG,OAAO;YAC3C,iBAAiB,kBAAkB,MAAM,CAAC,EAAE,GAAG,6IAAA,CAAA,OAAI,CAAC,WAAW,CAAC,MAAM;YAEtE,GAAG,EAAE,CACD,MACA;gBACI,UAAU,KAAK,AAAC,CAAC,OAAO,cAAc,IAAI,MAAM,CAAC,EAAE,GAAI;gBACvD,UAAU,iBAAiB;YAC/B,GACA,GAEC,MAAM,CACH,MACA;gBACI,UAAU,KACN,AAAC,CAAC,OAAO,iBAAiB,UAAU,IAAI,MAAM,CAAC,EAAE,GAAI;YAE7D,GACA;gBACI,UAAU,SAAS,CAAC,EAAE;gBACtB,UACI,CAAC,OAAO,iBAAiB,aAAa,IAAI,IAAI;gBAClD,iBAAiB;YACrB,GACA,iBAAiB,iBAEpB,GAAG,CAAC,UAAU,GAAG,kBAAkB;YAExC,KAAK,CAAC,EAAE,GAAG,kBAAkB;QACjC;QAEA,SAAS,QAAQ,KAAK,EAAE,IAAI;YACxB,OAAO,QAAQ,CAAC;YAChB,IAAI,KAAK,GAAG,CAAC,QAAQ,YAAY,SAAS,GAAG;gBACzC,SAAS,QAAQ,WAAW,CAAC,SAAS;YAC1C;YACA,IAAI,WAAW,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,QACtC,OAAO,KAAK,CAAC,SAAS;YAC1B,IAAI,AAAC,OAAO,GAAG,IAAI,OAAQ,QAAQ,UAAU;gBACzC,KAAK,SAAS,GAAG;oBAAE,MAAM,6IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ;gBAAI;gBAC3D,QAAQ,GAAG,QAAQ,KAAK,CAAC,QAAQ,WAAW,IAAI,CAAC,CAAC;YACtD;YACA,WAAW;YACX,KAAK,SAAS,GAAG;YACjB,OAAO,GAAG,OAAO,CAAC,MAAM;QAC5B;QAEA,GAAG,IAAI,GAAG,CAAC,OAAS,QAAQ,WAAW,GAAG;QAC1C,GAAG,QAAQ,GAAG,CAAC,OAAS,QAAQ,WAAW,GAAG;QAC9C,GAAG,OAAO,GAAG,IAAM;QACnB,GAAG,OAAO,GAAG,CAAC,OAAO,OAAS,QAAQ,OAAO;QAC7C,GAAG,KAAK,GAAG;QACX,GAAG,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,GAAG;QACjC,IAAI,OAAO,QAAQ,EAAE;YACjB,GAAG,IAAI,CAAC,iBAAiB;YACzB,GAAG,OAAO;QACd;QACA,OAAO;IACX;IAIA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;YAAU,KAAK;sBACzB,MAAM,GAAG,CAAC,CAAC,MAAM,sBACd,8OAAC;oBAAgB,WAAU;8BACvB,cAAA,8OAAC;wBAAI,WAAU;kCACV;;;;;;mBAFC;;;;;;;;;;;;;;;AAS9B;uCAEe", "debugId": null}}]}