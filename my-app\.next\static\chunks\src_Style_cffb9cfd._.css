/* [project]/src/Style/StartFlowingSection.css [app-client] (css) */
.start-flowing-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%) fixed;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  display: flex;
  position: relative;
  overflow: hidden;
}

.background-overlay {
  z-index: 1;
  background: #0000004d;
  position: absolute;
  inset: 0;
}

.background-blur {
  backdrop-filter: blur(1px);
  z-index: 2;
  position: absolute;
  inset: 0;
}

.dotted-line-svg {
  z-index: 3;
  pointer-events: none;
  width: 60%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
}

.dotted-line {
  filter: drop-shadow(0 0 10px #ffffff4d);
}

.start-flowing-container {
  z-index: 4;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
}

.floating-elements {
  z-index: 1;
  pointer-events: none;
  position: absolute;
  inset: 0;
}

.floating-circle {
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border-radius: 50%;
  position: absolute;
}

.circle-1 {
  width: 120px;
  height: 120px;
  animation: 6s ease-in-out infinite float;
  top: 20%;
  left: 10%;
}

.circle-2 {
  width: 80px;
  height: 80px;
  animation: 8s ease-in-out infinite reverse float;
  top: 60%;
  right: 15%;
}

.circle-3 {
  width: 60px;
  height: 60px;
  animation: 7s ease-in-out infinite float;
  bottom: 20%;
  left: 20%;
}

.floating-triangle {
  border-style: solid;
  width: 0;
  height: 0;
  position: absolute;
}

.triangle-1 {
  border-bottom: 43px solid #ffffff1a;
  border-left: 25px solid #0000;
  border-right: 25px solid #0000;
  animation: 10s linear infinite rotate;
  top: 30%;
  right: 20%;
}

.triangle-2 {
  border-bottom: 26px solid #ffffff1a;
  border-left: 15px solid #0000;
  border-right: 15px solid #0000;
  animation: 12s linear infinite reverse rotate;
  bottom: 30%;
  right: 10%;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0)rotate(0);
  }

  50% {
    transform: translateY(-20px)rotate(180deg);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.content-wrapper {
  text-align: center;
  z-index: 5;
  max-width: 700px;
  margin: 0 auto;
  position: relative;
}

.hero-badge {
  color: #fff;
  backdrop-filter: blur(10px);
  background: #fff3;
  border: 1px solid #ffffff4d;
  border-radius: 25px;
  align-items: center;
  margin-bottom: 24px;
  padding: 8px 20px;
  font-size: .9rem;
  font-weight: 600;
  display: inline-flex;
}

.main-title {
  color: #fff;
  text-shadow: 0 4px 20px #0000004d;
  margin: 0 0 24px;
  font-family: Georgia, serif;
  font-size: 4.5rem;
  font-weight: 400;
  line-height: 1.1;
  position: relative;
}

.title-dots {
  color: #ffffffb3;
  animation: 2s ease-in-out infinite dots-fade;
}

@keyframes dots-fade {
  0%, 100% {
    opacity: .7;
  }

  50% {
    opacity: .3;
  }
}

.subtitle {
  color: #ffffffe6;
  text-shadow: 0 2px 10px #0000004d;
  margin: 0 0 32px;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.6;
}

.feature-highlights {
  flex-wrap: wrap;
  justify-content: center;
  gap: 32px;
  margin-bottom: 40px;
  display: flex;
}

.feature-highlight {
  color: #fff;
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 25px;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: .9rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.feature-highlight:hover {
  background: #fff3;
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 1.1rem;
}

.cta-buttons {
  flex-wrap: wrap;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
  display: flex;
}

.primary-cta {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px #f59e0b4d;
}

.primary-cta:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.primary-cta:hover:before {
  left: 100%;
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px #f59e0b66;
}

.secondary-cta {
  color: #fff;
  cursor: pointer;
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 2px solid #ffffff4d;
  border-radius: 50px;
  padding: 14px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
}

.secondary-cta:hover {
  background: #fff3;
  border-color: #ffffff80;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #ffffff1a;
}

.availability-text {
  color: #ffffffb3;
  text-shadow: 0 2px 10px #0000004d;
  margin: 0;
  font-size: .95rem;
}

@media (width <= 1024px) {
  .main-title {
    font-size: 3.5rem;
  }

  .subtitle {
    font-size: 1.125rem;
  }

  .dotted-line-svg {
    width: 70%;
  }
}

@media (width <= 768px) {
  .start-flowing-section {
    min-height: 90vh;
    padding: 60px 0;
  }

  .start-flowing-container {
    padding: 0 20px;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    margin-bottom: 24px;
    font-size: 1rem;
  }

  .feature-highlights {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .primary-cta, .secondary-cta {
    width: 100%;
    max-width: 280px;
    padding: 16px 24px;
    font-size: 1rem;
  }

  .dotted-line-svg {
    width: 80%;
  }

  .content-wrapper {
    max-width: 500px;
  }

  .floating-elements {
    display: none;
  }
}

@media (width <= 480px) {
  .start-flowing-container {
    padding: 0 15px;
  }

  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: .95rem;
  }

  .primary-cta, .secondary-cta {
    padding: 14px 20px;
    font-size: .95rem;
  }

  .availability-text {
    font-size: .875rem;
  }

  .dotted-line-svg {
    display: none;
  }
}


/* [project]/src/Style/HomeSection.css [app-client] (css) */
.home-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
}

.home-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.content-grid {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  display: grid;
}

.text-content {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.section-badge {
  color: #92400e;
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border: 1px solid #f59e0b33;
  border-radius: 20px;
  align-items: center;
  width: fit-content;
  padding: 8px 16px;
  font-size: .875rem;
  font-weight: 600;
  display: inline-flex;
}

.section-title {
  color: #000;
  text-shadow: 0 2px 4px #0000001a;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 500;
}

.section-description {
  color: #4b5563;
  text-shadow: 0 1px 2px #0000001a;
  max-width: 500px;
  margin: 0;
  font-size: 1.125rem;
  line-height: 1.6;
}

.feature-list {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.feature-item {
  color: #374151;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  display: flex;
}

.feature-icon {
  background: #fff;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-size: 1.25rem;
  display: flex;
  box-shadow: 0 2px 8px #0000001a;
}

.visual-content {
  flex-direction: column;
  gap: 32px;
  display: flex;
}

.stats-grid {
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  display: grid;
}

.stat-card {
  text-align: center;
  background: #fff;
  border: 1px solid #f59e0b1a;
  border-radius: 16px;
  padding: 24px;
  transition: all .3s;
  box-shadow: 0 4px 20px #0000001a;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px #00000026;
}

.stat-number {
  color: #1f2937;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-bottom: 8px;
  font-size: 2rem;
  font-weight: 700;
}

.stat-label {
  color: #6b7280;
  font-size: .875rem;
  font-weight: 500;
}

.demo-card {
  background: #fff;
  border: 1px solid #f59e0b1a;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 30px #0000001a;
}

.demo-header {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  display: flex;
}

.demo-title {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.demo-status {
  color: #10b981;
  align-items: center;
  gap: 8px;
  font-size: .875rem;
  font-weight: 500;
  display: flex;
}

.status-dot {
  background: #10b981;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  animation: 2s ease-in-out infinite pulse;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: .7;
    transform: scale(1.1);
  }
}

.demo-content {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.waveform {
  justify-content: center;
  align-items: center;
  gap: 4px;
  height: 60px;
  display: flex;
}

.wave-bar {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 2px;
  width: 4px;
  animation: 1.5s ease-in-out infinite wave;
}

.wave-bar:first-child {
  height: 20px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 35px;
  animation-delay: .1s;
}

.wave-bar:nth-child(3) {
  height: 50px;
  animation-delay: .2s;
}

.wave-bar:nth-child(4) {
  height: 30px;
  animation-delay: .3s;
}

.wave-bar:nth-child(5) {
  height: 45px;
  animation-delay: .4s;
}

.wave-bar:nth-child(6) {
  height: 25px;
  animation-delay: .5s;
}

.wave-bar:nth-child(7) {
  height: 40px;
  animation-delay: .6s;
}

.wave-bar:nth-child(8) {
  height: 20px;
  animation-delay: .7s;
}

@keyframes wave {
  0%, 100% {
    opacity: .7;
    transform: scaleY(.3);
  }

  50% {
    opacity: 1;
    transform: scaleY(1);
  }
}

.demo-text {
  color: #6b7280;
  text-align: center;
  background: #f9fafb;
  border-left: 4px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
  font-size: .95rem;
  font-style: italic;
}

@media (width <= 1024px) {
  .home-section {
    padding: 100px 20px;
  }

  .content-grid {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }
}

@media (width <= 768px) {
  .home-section {
    min-height: auto;
    padding: 80px 20px;
  }

  .content-grid {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-description {
    max-width: none;
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .feature-list {
    align-items: center;
  }

  .feature-item {
    justify-content: center;
  }
}

@media (width <= 480px) {
  .home-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: .95rem;
  }

  .demo-card, .stat-card {
    padding: 20px;
  }

  .stat-number {
    font-size: 1.75rem;
  }

  .waveform {
    height: 50px;
  }

  .wave-bar {
    width: 3px;
  }
}


/* [project]/src/Style/Marquee.css [app-client] (css) */
.marquee-container {
  color: #fff;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  align-items: center;
  width: 100%;
  min-height: 120px;
  padding: 30px 0;
  font-family: Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 50px #0000004d;
}

.marquee-container:before {
  content: "";
  pointer-events: none;
  z-index: 2;
  background: linear-gradient(90deg, #00000026 0%, #0000 15% 85%, #00000026 100%);
  position: absolute;
  inset: 0;
}

.marquee-wrapper {
  white-space: nowrap;
  will-change: transform;
  align-items: center;
  display: flex;
}

.marquee-item {
  backdrop-filter: blur(10px);
  border: 1px solid #ffffff1a;
  border-radius: 50px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  min-width: fit-content;
  margin-right: 24px;
  transition: all .3s;
  display: inline-flex;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px #0003;
}

.marquee-content {
  color: #fff;
  text-shadow: 0 2px 4px #0000004d;
  white-space: nowrap;
  letter-spacing: .5px;
  user-select: none;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
}

@media (width <= 1024px) {
  .marquee-content {
    padding: 11px 18px;
    font-size: 15px;
  }

  .marquee-item {
    margin-right: 18px;
  }
}

@media (width <= 768px) {
  .marquee-container {
    min-height: 80px;
    padding: 15px 0;
  }

  .marquee-content {
    padding: 10px 16px;
    font-size: 14px;
  }

  .marquee-item {
    margin-right: 15px;
  }
}

@media (width <= 480px) {
  .marquee-container {
    min-height: 70px;
    padding: 12px 0;
  }

  .marquee-content {
    letter-spacing: .3px;
    padding: 8px 14px;
    font-size: 13px;
  }

  .marquee-item {
    margin-right: 12px;
  }
}

@media (width <= 320px) {
  .marquee-container {
    min-height: 60px;
    padding: 10px 0;
  }

  .marquee-content {
    padding: 6px 12px;
    font-size: 12px;
  }

  .marquee-item {
    margin-right: 10px;
  }
}

.MarqueeBox {
  color: #fff;
  text-align: center;
  background: #111;
  justify-content: center;
  align-items: center;
  height: 20vh;
  font-family: system-ui;
  display: flex;
}

.carousel {
  background: #00f;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
}

.box {
  color: #000;
  cursor: pointer;
  background: green;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  font-size: 121px;
  display: flex;
  position: relative;
}

.test {
  padding: 20px;
}

.test-2 {
  padding: 20px 10px;
}


/* [project]/src/Style/FlowSection.css [app-client] (css) */
.flow-section {
  color: #fff;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.flow-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 30% 20%, #f59e0b1a 0%, #0000 50%), radial-gradient(circle at 70% 80%, #10b9811a 0%, #0000 50%);
  position: absolute;
  inset: 0;
}

.flow-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.flow-content {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 60px;
  max-width: 100%;
  display: grid;
}

.flow-left {
  flex-direction: column;
  gap: 40px;
  display: flex;
}

.flow-title {
  word-wrap: break-word;
  text-shadow: 0 4px 20px #0000004d;
  z-index: 1;
  max-width: 100%;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
  position: relative;
}

.profession-tags {
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  max-width: 100%;
  display: flex;
}

.profession-tag {
  color: #fff;
  cursor: pointer;
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 25px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.profession-tag:hover {
  background: #fff3;
  border-color: #fff6;
  transform: translateY(-2px);
}

.flow-right {
  flex-direction: column;
  align-items: flex-end;
  gap: 40px;
  display: flex;
}

.flow-accessibility {
  text-align: left;
  max-width: 400px;
}

.accessibility-title {
  color: #fff;
  margin-bottom: 16px;
  font-size: 2rem;
  font-weight: 600;
}

.accessibility-description {
  color: #fffc;
  margin-bottom: 24px;
  font-size: 1.1rem;
  line-height: 1.6;
}

.get-started-btn {
  color: #fff;
  cursor: pointer;
  background: #6366f1;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.get-started-btn:hover {
  background: #5855eb;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #6366f14d;
}

.flow-illustration {
  width: 300px;
  height: 300px;
  position: relative;
}

.character-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.character {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.character-head {
  background: #ff9ff3;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  margin: 0 auto 10px;
  position: relative;
}

.character-face {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.eye {
  background: #2a2a2a;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  position: absolute;
}

.left-eye {
  top: -2px;
  left: -12px;
}

.right-eye {
  top: -2px;
  right: -12px;
}

.mouth {
  border: 2px solid #2a2a2a;
  border-top: none;
  border-radius: 0 0 12px 12px;
  width: 12px;
  height: 6px;
  position: absolute;
  top: 8px;
  left: -6px;
}

.character-ears {
  width: 100%;
  position: absolute;
  top: 10px;
}

.ear {
  background: #ff9ff3;
  border-radius: 50%;
  width: 20px;
  height: 25px;
  position: absolute;
}

.left-ear {
  left: -10px;
  transform: rotate(-20deg);
}

.right-ear {
  right: -10px;
  transform: rotate(20deg);
}

.character-body {
  background: #ff9ff3;
  border-radius: 30px;
  width: 60px;
  height: 80px;
  margin: 0 auto;
  position: relative;
}

.character-arms {
  width: 100%;
  position: absolute;
  top: 20px;
}

.arm {
  background: #ff9ff3;
  border-radius: 4px;
  width: 30px;
  height: 8px;
  position: absolute;
}

.left-arm {
  left: -25px;
  transform: rotate(-30deg);
}

.right-arm {
  right: -25px;
  transform: rotate(30deg);
}

.speech-bubble {
  background: #fff;
  border-radius: 15px;
  width: 120px;
  height: 80px;
  padding: 15px;
  position: absolute;
  top: -20px;
  right: -80px;
  box-shadow: 0 4px 20px #0000001a;
}

.speech-bubble:before {
  content: "";
  border: 10px solid #0000;
  border-left-width: 0;
  border-right-color: #fff;
  width: 0;
  height: 0;
  position: absolute;
  top: 30px;
  left: -10px;
}

.bubble-content {
  flex-direction: column;
  justify-content: center;
  height: 100%;
  display: flex;
}

.text-lines {
  flex-direction: column;
  gap: 6px;
  display: flex;
}

.text-line {
  background: #ff9500;
  border-radius: 2px;
  height: 3px;
}

.text-line.short {
  width: 60%;
}

@media (width <= 1024px) {
  .flow-content {
    gap: 60px;
  }

  .flow-title {
    font-size: 3.5rem;
  }

  .flow-illustration {
    width: 250px;
    height: 250px;
  }
}

@media (width <= 768px) {
  .flow-section {
    padding: 80px 20px;
  }

  .flow-content {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .flow-title {
    font-size: 3rem;
  }

  .flow-right {
    align-items: center;
  }

  .flow-accessibility {
    text-align: center;
  }

  .profession-tags {
    justify-content: center;
  }

  .flow-illustration {
    width: 200px;
    height: 200px;
  }

  .speech-bubble {
    width: 100px;
    height: 60px;
    padding: 10px;
    right: -60px;
  }
}

@media (width <= 480px) {
  .flow-section {
    padding: 60px 15px;
  }

  .flow-title {
    font-size: 2.5rem;
  }

  .accessibility-title {
    font-size: 1.5rem;
  }

  .accessibility-description {
    font-size: 1rem;
  }

  .profession-tag {
    padding: 6px 12px;
    font-size: 12px;
  }

  .flow-illustration {
    width: 180px;
    height: 180px;
  }

  .character-head {
    width: 60px;
    height: 60px;
  }

  .character-body {
    width: 45px;
    height: 60px;
  }
}


/* [project]/src/Style/ContentImageSection.css [app-client] (css) */
.content-image-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
}

.content-image-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
}

.content-side {
  flex-direction: column;
  gap: 40px;
  display: flex;
}

.content-header {
  flex-direction: column;
  gap: 20px;
  display: flex;
}

.content-badge {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  width: fit-content;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
}

.content-title {
  color: #1a202c;
  margin: 0;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.content-description {
  color: #4a5568;
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-grid {
  grid-template-columns: 1fr;
  gap: 24px;
  display: grid;
}

.feature-item {
  background: #fff;
  border-radius: 12px;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 6px #0000000d;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px #0000001a;
}

.feature-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 2rem;
  display: flex;
}

.feature-content {
  flex: 1;
}

.feature-title {
  color: #1a202c;
  margin: 0 0 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.feature-description {
  color: #4a5568;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.content-actions {
  flex-wrap: wrap;
  gap: 16px;
  display: flex;
}

.primary-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 14px 28px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 4px 15px #667eea4d;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #667eea66;
}

.secondary-btn {
  color: #667eea;
  cursor: pointer;
  background: none;
  border: 2px solid #667eea;
  border-radius: 8px;
  padding: 12px 26px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.secondary-btn:hover {
  color: #fff;
  background: #667eea;
  transform: translateY(-2px);
}

.image-side {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.image-container {
  width: 100%;
  max-width: 500px;
  position: relative;
}

.main-device {
  z-index: 2;
  background: #1a202c;
  border-radius: 20px;
  padding: 20px;
  position: relative;
  box-shadow: 0 20px 40px #0003;
}

.device-screen {
  background: #2d3748;
  border-radius: 12px;
  overflow: hidden;
}

.screen-header {
  background: #4a5568;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  display: flex;
}

.screen-dots {
  gap: 6px;
  display: flex;
}

.dot {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.dot.red {
  background: #f56565;
}

.dot.yellow {
  background: #ed8936;
}

.dot.green {
  background: #48bb78;
}

.screen-title {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.screen-content {
  flex-direction: column;
  align-items: center;
  gap: 30px;
  padding: 30px 20px;
  display: flex;
}

.voice-wave {
  justify-content: center;
  align-items: center;
  gap: 4px;
  display: flex;
}

.wave-bar {
  background: #667eea;
  border-radius: 2px;
  width: 4px;
  animation: 1.5s ease-in-out infinite wave;
}

.wave-bar:first-child {
  height: 20px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 35px;
  animation-delay: .1s;
}

.wave-bar:nth-child(3) {
  height: 25px;
  animation-delay: .2s;
}

.wave-bar:nth-child(4) {
  height: 40px;
  animation-delay: .3s;
}

.wave-bar:nth-child(5) {
  height: 30px;
  animation-delay: .4s;
}

.wave-bar:nth-child(6) {
  height: 35px;
  animation-delay: .5s;
}

.wave-bar:nth-child(7) {
  height: 20px;
  animation-delay: .6s;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(.3);
  }
}

.text-output {
  background: #4a5568;
  border-radius: 8px;
  align-items: center;
  width: 100%;
  min-height: 60px;
  padding: 16px;
  display: flex;
}

.typing-text {
  color: #e2e8f0;
  font-family: Courier New, monospace;
  font-size: 14px;
}

.cursor {
  color: #667eea;
  animation: 1s infinite blink;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }

  51%, 100% {
    opacity: 0;
  }
}

.floating-elements {
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.floating-card {
  color: #1a202c;
  background: #fff;
  border-radius: 12px;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  animation: 3s ease-in-out infinite float;
  display: flex;
  position: absolute;
  box-shadow: 0 8px 25px #00000026;
}

.card-1 {
  animation-delay: 0s;
  top: 20%;
  left: -10%;
}

.card-2 {
  animation-delay: 1s;
  top: 60%;
  right: -15%;
}

.card-3 {
  animation-delay: 2s;
  bottom: 20%;
  left: -5%;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

.card-icon {
  font-size: 18px;
}

@media (width <= 1024px) {
  .content-image-container {
    gap: 60px;
  }

  .content-title {
    font-size: 3rem;
  }
}

@media (width <= 768px) {
  .content-image-section {
    padding: 80px 20px;
  }

  .content-image-container {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .content-title {
    text-align: center;
    font-size: 2.5rem;
  }

  .content-header {
    text-align: center;
  }

  .content-badge {
    align-self: center;
  }

  .content-actions {
    justify-content: center;
  }

  .floating-card {
    display: none;
  }
}

@media (width <= 480px) {
  .content-image-section {
    padding: 60px 15px;
  }

  .content-title {
    font-size: 2rem;
  }

  .content-description {
    font-size: 1rem;
  }

  .feature-item {
    padding: 16px;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .main-device {
    padding: 15px;
  }

  .screen-content {
    padding: 20px 15px;
  }
}


/* [project]/src/Style/AIAutoEditsSection.css [app-client] (css) */
.ai-auto-edits-section {
  background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 50%, #fdba74 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.ai-auto-edits-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 70% 30%, #f59e0b1a 0%, #0000 50%), radial-gradient(circle at 30% 70%, #fbbf241a 0%, #0000 50%);
  position: absolute;
  inset: 0;
}

.ai-auto-edits-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: grid;
}

.phone-mockup-container {
  justify-content: center;
  align-items: center;
  display: flex;
}

.phone-device {
  background: linear-gradient(145deg, #1f2937, #374151);
  border-radius: 30px;
  width: 320px;
  height: 640px;
  padding: 8px;
  position: relative;
  box-shadow: 0 25px 50px #0000004d;
}

.phone-device:before {
  content: "";
  background: #6b7280;
  border-radius: 2px;
  width: 60px;
  height: 4px;
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}

.phone-screen {
  background: #111827;
  border-radius: 22px;
  flex-direction: column;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.screen-header {
  background: #1f2937;
  padding: 12px 16px 8px;
}

.status-bar {
  color: #fff;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.status-icons {
  gap: 4px;
  display: flex;
}

.app-header h3 {
  color: #fff;
  text-align: center;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.screen-content {
  flex: 1;
  padding: 20px 16px;
  overflow-y: auto;
}

.text-editor {
  flex-direction: column;
  gap: 20px;
  height: 100%;
  display: flex;
}

.original-text {
  background: #374151;
  border-left: 4px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
}

.original-text p {
  color: #e5e7eb;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.ai-suggestions {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 12px;
  padding: 16px;
}

.suggestion-header {
  color: #60a5fa;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.suggestion-items {
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  display: flex;
}

.suggestion-item {
  color: #d1d5db;
  opacity: .6;
  background: #374151;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 13px;
  transition: all .3s;
}

.suggestion-item.active {
  color: #fff;
  opacity: 1;
  background: #3b82f6;
  transform: scale(1.02);
}

.action-buttons {
  gap: 8px;
  display: flex;
}

.apply-btn, .preview-btn {
  cursor: pointer;
  border: none;
  border-radius: 6px;
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  transition: all .3s;
}

.apply-btn {
  color: #fff;
  background: #10b981;
}

.apply-btn:hover {
  background: #059669;
}

.preview-btn {
  color: #d1d5db;
  background: #374151;
}

.preview-btn:hover {
  background: #4b5563;
}

.improved-text {
  background: #065f46;
  border-left: 4px solid #10b981;
  border-radius: 12px;
  padding: 16px;
}

.improved-header {
  color: #6ee7b7;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.typing-text {
  color: #d1fae5;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  animation: 4s steps(40, end) infinite typewriter;
}

@keyframes typewriter {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 100%;
  }
}

.content-section {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.content-badge {
  color: #92400e;
  background: #fbbf2433;
  border: 1px solid #fbbf244d;
  border-radius: 20px;
  align-items: center;
  gap: 8px;
  width: fit-content;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.sparkle {
  animation: 2s ease-in-out infinite sparkle;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1)rotate(0);
  }

  50% {
    transform: scale(1.2)rotate(180deg);
  }
}

.section-title {
  color: #92400e;
  text-shadow: 0 2px 4px #0000001a;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.section-description {
  color: #78350f;
  text-shadow: 0 1px 2px #0000001a;
  max-width: 500px;
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-list {
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin: 20px 0;
  display: grid;
}

.feature-item {
  color: #92400e;
  backdrop-filter: blur(10px);
  background: #ffffff80;
  border: 1px solid #ffffff4d;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.feature-icon {
  font-size: 18px;
}

.cta-button {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  width: fit-content;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
  box-shadow: 0 8px 25px #f59e0b4d;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px #f59e0b66;
}

.arrow {
  transition: transform .3s;
}

.cta-button:hover .arrow {
  transform: translateX(4px);
}

@media (width <= 1024px) {
  .ai-auto-edits-container {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .phone-device {
    width: 280px;
    height: 560px;
  }
}

@media (width <= 768px) {
  .ai-auto-edits-section {
    padding: 80px 20px;
  }

  .ai-auto-edits-container {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .phone-device {
    width: 260px;
    height: 520px;
  }
}

@media (width <= 480px) {
  .ai-auto-edits-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .phone-device {
    width: 240px;
    height: 480px;
  }

  .screen-content {
    padding: 16px 12px;
  }
}


/* [project]/src/Style/MultiDeviceSection.css [app-client] (css) */
.multi-device-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #fed7aa 100%);
  min-height: auto;
  padding: 120px 20px;
  position: relative;
  overflow: hidden;
}

.multi-device-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 20% 30%, #f59e0b1a 0%, #0000 50%), radial-gradient(circle at 80% 70%, #fbbf241a 0%, #0000 50%);
  position: absolute;
  inset: 0;
}

.multi-device-container {
  flex-direction: column;
  gap: 120px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: flex;
}

.device-row {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  display: grid;
}

.device-row.reverse {
  grid-template-columns: 1fr 1fr;
}

.device-row.reverse .device-mockup {
  order: 2;
}

.device-row.reverse .device-content {
  order: 1;
}

.device-mockup {
  background: #1f2937;
  border-radius: 20px;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  box-shadow: 0 25px 50px #0000004d;
}

.device-screen {
  background: #111827;
  border-radius: 12px;
  min-height: 300px;
  overflow: hidden;
}

.screen-header {
  color: #fff;
  background: #374151;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  display: flex;
}

.screen-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  cursor: pointer;
  opacity: .7;
  font-size: 20px;
}

.device-content {
  flex-direction: column;
  gap: 20px;
  max-width: 500px;
  display: flex;
}

.device-content h3 {
  color: #92400e;
  text-shadow: 0 2px 4px #0000001a;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 2.5rem;
  font-weight: 400;
  line-height: 1.2;
}

.device-content p {
  color: #78350f;
  text-shadow: 0 1px 2px #0000001a;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.dictionary-content {
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  display: flex;
}

.dict-item {
  color: #60a5fa;
  background: #374151;
  border-left: 3px solid #3b82f6;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.dict-item:hover {
  background: #4b5563;
  transform: translateX(4px);
}

.app-selector {
  background: #374151;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  display: flex;
}

.app-icon {
  cursor: pointer;
  background: #4b5563;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 20px;
  transition: all .3s;
  display: flex;
}

.app-icon.active {
  background: #3b82f6;
  transform: scale(1.1);
}

.tones-content {
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  display: flex;
}

.tone-option {
  color: #d1d5db;
  text-align: center;
  cursor: pointer;
  background: #374151;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.tone-option.active {
  color: #fff;
  background: #10b981;
  transform: scale(1.02);
}

.language-wheel {
  width: 200px;
  height: 200px;
  margin: 40px auto;
  position: relative;
}

.language-item {
  color: #fff;
  transform-origin: center;
  background: #3b82f6;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 12px;
  font-weight: 600;
  animation: 20s linear infinite rotate;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
}

@keyframes rotate {
  from {
    transform: rotate(0)translateY(-80px)rotate(0);
  }

  to {
    transform: rotate(360deg)translateY(-80px)rotate(-360deg);
  }
}

.wheel-center {
  background: #1f2937;
  border: 3px solid #3b82f6;
  border-radius: 50%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.lang-count {
  color: #60a5fa;
  font-size: 18px;
  font-weight: 700;
}

.lang-text {
  color: #9ca3af;
  font-size: 10px;
  font-weight: 500;
}

.desktop-device {
  max-width: 500px;
}

.desktop-screen {
  background: #f3f4f6;
  border-radius: 12px;
  min-height: 400px;
  position: relative;
  overflow: hidden;
}

.desktop-header {
  background: #e5e7eb;
  border-bottom: 1px solid #d1d5db;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  display: flex;
}

.window-controls {
  gap: 6px;
  display: flex;
}

.control {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.control.red {
  background: #ef4444;
}

.control.yellow {
  background: #f59e0b;
}

.control.green {
  background: #10b981;
}

.window-title {
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

.desktop-content {
  height: 300px;
  display: flex;
}

.sidebar {
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  flex-direction: column;
  gap: 8px;
  width: 120px;
  padding: 16px 8px;
  display: flex;
}

.sidebar-item {
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  transition: all .3s;
}

.sidebar-item.active {
  color: #fff;
  background: #3b82f6;
}

.main-content {
  background: #fff;
  flex: 1;
  padding: 20px;
}

.document-header {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  display: flex;
}

.document-header h4 {
  color: #1f2937;
  margin: 0;
  font-size: 16px;
}

.doc-status {
  color: #10b981;
  background: #dcfce7;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
}

.document-body {
  flex-direction: column;
  gap: 12px;
  display: flex;
}

.text-line {
  background: #d1d5db;
  border-radius: 2px;
  height: 4px;
}

.text-line.short {
  width: 60%;
}

.text-line.medium {
  width: 80%;
}

.mobile-preview {
  background: #1f2937;
  border-radius: 12px;
  width: 80px;
  height: 120px;
  padding: 8px;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.mobile-screen {
  background: #111827;
  border-radius: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  height: 100%;
  display: flex;
}

.mobile-header {
  color: #fff;
  font-size: 8px;
  font-weight: 600;
}

.voice-indicator {
  align-items: center;
  gap: 2px;
  display: flex;
}

.voice-wave {
  background: #3b82f6;
  border-radius: 1px;
  width: 2px;
  animation: 1s ease-in-out infinite wave;
}

.voice-wave:first-child {
  height: 8px;
  animation-delay: 0s;
}

.voice-wave:nth-child(2) {
  height: 12px;
  animation-delay: .1s;
}

.voice-wave:nth-child(3) {
  height: 6px;
  animation-delay: .2s;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(.3);
  }
}

.cta-button {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 8px;
  width: fit-content;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 4px 15px #f59e0b4d;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #f59e0b66;
}

@media (width <= 1024px) {
  .multi-device-container {
    gap: 100px;
  }

  .device-row {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 2rem;
  }

  .device-mockup {
    max-width: 350px;
  }
}

@media (width <= 768px) {
  .multi-device-section {
    padding: 80px 20px;
  }

  .multi-device-container {
    gap: 80px;
  }

  .device-row {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .device-row.reverse .device-mockup, .device-row.reverse .device-content {
    order: unset;
  }

  .device-content {
    align-items: center;
    max-width: none;
  }

  .device-content h3 {
    font-size: 1.8rem;
  }

  .device-mockup {
    max-width: 320px;
  }

  .language-wheel {
    width: 160px;
    height: 160px;
  }

  .language-item {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }

  .wheel-center {
    width: 60px;
    height: 60px;
  }

  .lang-count {
    font-size: 14px;
  }

  .lang-text {
    font-size: 8px;
  }
}

@media (width <= 480px) {
  .multi-device-section {
    padding: 60px 15px;
  }

  .multi-device-container {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 1.5rem;
  }

  .device-content p {
    font-size: 1rem;
  }

  .device-mockup {
    max-width: 280px;
    padding: 15px;
  }

  .device-screen {
    min-height: 250px;
  }

  .desktop-content {
    height: 250px;
  }

  .sidebar {
    width: 100px;
    padding: 12px 6px;
  }

  .sidebar-item {
    padding: 6px 8px;
    font-size: 10px;
  }

  .main-content {
    padding: 15px;
  }

  .mobile-preview {
    width: 60px;
    height: 90px;
    bottom: 15px;
    right: 15px;
  }

  .language-wheel {
    width: 140px;
    height: 140px;
  }

  .language-item {
    width: 28px;
    height: 28px;
    font-size: 9px;
  }
}


/* [project]/src/Style/CrossPlatformSection.css [app-client] (css) */
.cross-platform-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
}

.cross-platform-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  display: grid;
}

.content-section {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.platform-badges {
  gap: 12px;
  margin-bottom: 8px;
  display: flex;
}

.platform-badge {
  color: #374151;
  backdrop-filter: blur(10px);
  background: #fffc;
  border: 1px solid #f59e0b33;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: .875rem;
  font-weight: 500;
}

.section-title {
  color: #1f2937;
  text-shadow: 0 2px 4px #0000001a;
  letter-spacing: -.02em;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.section-description {
  color: #374151;
  text-shadow: 0 1px 2px #0000001a;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.get-started-btn {
  color: #1f2937;
  cursor: pointer;
  background: none;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  width: fit-content;
  padding: 16px 32px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.get-started-btn:hover {
  color: #f59e0b;
  border-color: #f59e0b;
  box-shadow: 0 4px 15px #f59e0b33;
}

.devices-section {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.device-stack {
  width: 100%;
  max-width: 500px;
  height: 600px;
  position: relative;
}

.desktop-mockup {
  z-index: 3;
  background: #1f2937;
  border-radius: 12px;
  width: 400px;
  height: 280px;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  box-shadow: 0 20px 40px #0000004d;
}

.desktop-header {
  background: #374151;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  display: flex;
}

.window-controls {
  gap: 8px;
  display: flex;
}

.control {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.control.red {
  background: #ef4444;
}

.control.yellow {
  background: #f59e0b;
}

.control.green {
  background: #10b981;
}

.desktop-content {
  height: calc(100% - 32px);
  display: flex;
}

.sidebar {
  background: #2d3748;
  flex-direction: column;
  gap: 8px;
  width: 120px;
  padding: 16px 8px;
  display: flex;
}

.sidebar-item {
  color: #d1d5db;
  cursor: pointer;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: .75rem;
  transition: background .2s;
}

.sidebar-item.active {
  color: #fff;
  background: #f59e0b;
}

.main-area {
  background: #1f2937;
  flex: 1;
  padding: 16px;
  position: relative;
}

.document-title {
  color: #f59e0b;
  margin-bottom: 16px;
  font-size: .875rem;
  font-weight: 600;
}

.document-section h4 {
  color: #e5e7eb;
  margin: 0 0 8px;
  font-size: .75rem;
  font-weight: 600;
}

.idea-item {
  color: #d1d5db;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: .7rem;
  display: flex;
}

.bullet {
  color: #f59e0b;
}

.voice-indicator-desktop {
  background: #f59e0b33;
  border-radius: 20px;
  padding: 8px 12px;
  position: absolute;
  bottom: 16px;
  right: 16px;
}

.voice-waves {
  align-items: center;
  gap: 2px;
  display: flex;
}

.wave {
  background: #f59e0b;
  border-radius: 1px;
  width: 2px;
  height: 12px;
  animation: 1s ease-in-out infinite wave;
}

.wave:first-child {
  animation-delay: 0s;
}

.wave:nth-child(2) {
  animation-delay: .2s;
}

.wave:nth-child(3) {
  animation-delay: .4s;
}

.wave:nth-child(4) {
  animation-delay: .6s;
}

.mobile-mockup {
  z-index: 2;
  background: #1f2937;
  border-radius: 20px;
  width: 180px;
  height: 320px;
  position: absolute;
  top: 200px;
  right: 50px;
  overflow: hidden;
  box-shadow: 0 15px 30px #0000004d;
}

.mobile-header {
  background: #374151;
  padding: 12px 16px 8px;
}

.status-bar {
  color: #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: .7rem;
  display: flex;
}

.app-title {
  color: #f59e0b;
  text-align: center;
  font-size: .875rem;
  font-weight: 600;
}

.mobile-content {
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 20px;
  display: flex;
}

.mobile-item {
  color: #e5e7eb;
  align-items: center;
  gap: 8px;
  font-size: .875rem;
  display: flex;
}

.recording-area {
  flex-direction: column;
  align-items: center;
  gap: 16px;
  display: flex;
}

.recording-circle {
  border: 2px solid #f59e0b;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  display: flex;
}

.record-button {
  background: #ef4444;
  border-radius: 50%;
  width: 20px;
  height: 20px;
}

.recording-waves {
  align-items: center;
  gap: 3px;
  display: flex;
}

.mobile-wave {
  background: #3b82f6;
  border-radius: 2px;
  width: 3px;
  height: 20px;
  animation: 1.2s ease-in-out infinite wave;
}

.mobile-wave:first-child {
  animation-delay: 0s;
}

.mobile-wave:nth-child(2) {
  animation-delay: .15s;
}

.mobile-wave:nth-child(3) {
  animation-delay: .3s;
}

.mobile-wave:nth-child(4) {
  animation-delay: .45s;
}

.mobile-wave:nth-child(5) {
  animation-delay: .6s;
}

.voice-widget {
  z-index: 1;
  background: #1f2937;
  border-radius: 16px;
  width: 120px;
  height: 80px;
  padding: 12px;
  position: absolute;
  bottom: 50px;
  left: 80px;
  box-shadow: 0 10px 25px #0000004d;
}

.widget-header {
  justify-content: center;
  margin-bottom: 8px;
  display: flex;
}

.widget-icon {
  font-size: 1.2rem;
}

.widget-waves {
  justify-content: center;
  align-items: center;
  gap: 2px;
  display: flex;
}

.widget-wave {
  background: #10b981;
  border-radius: 1px;
  width: 2px;
  height: 16px;
  animation: .8s ease-in-out infinite wave;
}

.widget-wave:first-child {
  animation-delay: 0s;
}

.widget-wave:nth-child(2) {
  animation-delay: .1s;
}

.widget-wave:nth-child(3) {
  animation-delay: .2s;
}

.widget-wave:nth-child(4) {
  animation-delay: .3s;
}

.widget-wave:nth-child(5) {
  animation-delay: .4s;
}

.widget-wave:nth-child(6) {
  animation-delay: .5s;
}

@keyframes wave {
  0%, 100% {
    opacity: .7;
    transform: scaleY(.3);
  }

  50% {
    opacity: 1;
    transform: scaleY(1);
  }
}

@media (width <= 1024px) {
  .cross-platform-container {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .desktop-mockup {
    width: 350px;
    height: 240px;
  }

  .mobile-mockup {
    width: 160px;
    height: 280px;
  }
}

@media (width <= 768px) {
  .cross-platform-section {
    min-height: auto;
    padding: 80px 20px;
  }

  .cross-platform-container {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .device-stack {
    max-width: 400px;
    height: 500px;
    margin: 0 auto;
  }

  .desktop-mockup {
    width: 300px;
    height: 200px;
    left: 50%;
    transform: translateX(-50%);
  }

  .mobile-mockup {
    width: 140px;
    height: 250px;
    top: 150px;
    right: 20px;
  }

  .voice-widget {
    width: 100px;
    height: 70px;
    bottom: 30px;
    left: 20px;
  }
}

@media (width <= 480px) {
  .cross-platform-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .platform-badges {
    flex-wrap: wrap;
    justify-content: center;
  }

  .platform-badge {
    padding: 6px 12px;
    font-size: .75rem;
  }

  .device-stack {
    max-width: 320px;
    height: 400px;
  }

  .desktop-mockup {
    width: 280px;
    height: 180px;
  }

  .mobile-mockup {
    width: 120px;
    height: 220px;
    top: 120px;
    right: 10px;
  }

  .voice-widget {
    width: 90px;
    height: 60px;
    bottom: 20px;
    left: 10px;
  }

  .get-started-btn {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }
}


/* [project]/src/Style/TestimonialsSection.css [app-client] (css) */
.testimonials-section {
  background: #1a1a1a;
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
}

.section-title {
  color: #fff;
  text-shadow: 0 4px 20px #0000004d;
  margin: 0 0 16px;
  font-family: Georgia, serif;
  font-size: 4rem;
  font-weight: 400;
  line-height: 1.1;
}

.section-subtitle {
  color: #fffc;
  text-align: center;
  text-shadow: 0 2px 10px #0000004d;
  margin: 0;
  font-size: 1.25rem;
}

.carousel-container {
  margin-top: 60px;
  position: relative;
}

.carousel-wrapper {
  border-radius: 20px;
  overflow: hidden;
}

.carousel-track {
  gap: 24px;
  transition: transform .5s ease-in-out;
  display: flex;
}

.carousel-controls {
  pointer-events: none;
  z-index: 10;
  justify-content: space-between;
  width: 100%;
  display: flex;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.carousel-btn {
  color: #1f2937;
  cursor: pointer;
  pointer-events: all;
  backdrop-filter: blur(10px);
  background: #ffffffe6;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 1.5rem;
  font-weight: bold;
  transition: all .3s;
  box-shadow: 0 4px 20px #0003;
}

.carousel-btn:hover {
  background: #fff;
  transform: scale(1.1);
  box-shadow: 0 6px 25px #0000004d;
}

.carousel-btn:active {
  transform: scale(.95);
}

.prev-btn {
  margin-left: -25px;
}

.next-btn {
  margin-right: -25px;
}

.carousel-dots {
  justify-content: center;
  gap: 12px;
  margin-top: 40px;
  display: flex;
}

.dot {
  cursor: pointer;
  background: #ffffff4d;
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s;
}

.dot.active {
  background: #f59e0b;
  transform: scale(1.2);
}

.dot:hover {
  background: #fff9;
}

.testimonial-card {
  background: #fff;
  border-radius: 20px;
  flex-shrink: 0;
  min-width: calc(33.333% - 16px);
  margin-right: 24px;
  padding: 32px;
  transition: all .3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 30px #00000026;
}

.testimonial-card:before {
  content: "";
  opacity: 0;
  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);
  height: 3px;
  transition: opacity .3s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px #00000026;
}

.testimonial-card:hover:before {
  opacity: 1;
}

.card-header {
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  display: flex;
}

.avatar {
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  font-size: 1.5rem;
  display: flex;
}

.user-info {
  flex: 1;
}

.user-name {
  color: #1f2937;
  margin: 0 0 4px;
  font-size: 1rem;
  font-weight: 600;
}

.user-role {
  color: #6b7280;
  margin: 0 0 4px;
  font-size: .875rem;
}

.user-company {
  color: #9ca3af;
  margin: 0;
  font-size: .75rem;
  font-style: italic;
}

.testimonial-content {
  margin-bottom: 16px;
}

.testimonial-text {
  color: #374151;
  margin: 0;
  font-size: .95rem;
  font-style: italic;
  line-height: 1.6;
}

.rating {
  gap: 2px;
  display: flex;
}

.star {
  opacity: .3;
  font-size: 1rem;
  transition: opacity .2s;
}

.star.filled {
  opacity: 1;
}

.card-1, .card-2, .card-3, .card-4, .card-5, .card-6, .card-7 {
  grid-row: span 1;
}

@media (width <= 1024px) {
  .testimonials-section {
    padding: 100px 20px;
  }

  .section-title {
    font-size: 3.5rem;
  }

  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .decorative-lines {
    width: 250px;
    height: 250px;
  }
}

@media (width <= 768px) {
  .testimonials-section {
    min-height: auto;
    padding: 80px 20px;
  }

  .testimonials-container {
    padding: 0 20px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .testimonial-card {
    min-width: calc(100% - 16px);
    padding: 24px;
  }

  .carousel-controls {
    display: none;
  }

  .carousel-dots {
    margin-top: 24px;
  }
}

@media (width <= 480px) {
  .testimonials-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .testimonial-card {
    padding: 16px;
  }

  .card-header {
    gap: 12px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .user-name {
    font-size: .9rem;
  }

  .user-role {
    font-size: .8rem;
  }

  .testimonial-text {
    font-size: .9rem;
  }

  .decorative-lines {
    display: none;
  }
}


/* [project]/src/Style/Footer.css [app-client] (css) */
.footer-section {
  color: #fff;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  padding: 80px 0 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.footer-content {
  flex-direction: column;
  display: flex;
}

.footer-main {
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  margin-bottom: 60px;
  display: grid;
}

.footer-brand {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.brand-logo {
  margin-bottom: 16px;
}

.brand-name {
  color: #fff;
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin: 0 0 8px;
  font-size: 2.5rem;
  font-weight: 700;
}

.brand-tagline {
  color: #9ca3af;
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.brand-description {
  color: #d1d5db;
  max-width: 400px;
  margin: 0 0 32px;
  font-size: 1rem;
  line-height: 1.6;
}

.newsletter-signup {
  margin-bottom: 32px;
}

.newsletter-title {
  color: #fff;
  margin: 0 0 8px;
  font-size: 1.125rem;
  font-weight: 600;
}

.newsletter-description {
  color: #9ca3af;
  margin: 0 0 16px;
  font-size: .875rem;
}

.newsletter-form {
  gap: 12px;
  max-width: 400px;
  display: flex;
}

.newsletter-input {
  color: #fff;
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 8px;
  flex: 1;
  padding: 12px 16px;
  font-size: .875rem;
  transition: all .3s;
}

.newsletter-input::placeholder {
  color: #fff9;
}

.newsletter-input:focus {
  background: #ffffff26;
  border-color: #f59e0b;
  outline: none;
}

.newsletter-btn {
  color: #fff;
  cursor: pointer;
  white-space: nowrap;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: .875rem;
  font-weight: 600;
  transition: all .3s;
}

.newsletter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px #f59e0b4d;
}

.social-links {
  gap: 16px;
  display: flex;
}

.social-link {
  color: #d1d5db;
  background: #ffffff1a;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.social-link:hover {
  color: #fff;
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  transform: translateY(-2px);
}

.footer-links {
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  display: grid;
}

.link-group {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.link-title {
  color: #fff;
  margin: 0 0 8px;
  font-size: 1.125rem;
  font-weight: 600;
}

.link-list {
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.footer-link {
  color: #9ca3af;
  font-size: .95rem;
  text-decoration: none;
  transition: color .3s;
  position: relative;
}

.footer-link:hover {
  color: #f59e0b;
}

.footer-link:after {
  content: "";
  background: #f59e0b;
  width: 0;
  height: 1px;
  transition: width .3s;
  position: absolute;
  bottom: -2px;
  left: 0;
}

.footer-link:hover:after {
  width: 100%;
}

.footer-bottom {
  border-top: 1px solid #ffffff1a;
  padding: 32px 0;
}

.footer-divider {
  background: linear-gradient(90deg, #0000 0%, #f59e0b4d 50%, #0000 100%);
  width: 100%;
  height: 1px;
  margin-bottom: 32px;
}

.footer-bottom-content {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.copyright {
  color: #9ca3af;
  margin: 0;
  font-size: .875rem;
}

.footer-bottom-links {
  gap: 24px;
  display: flex;
}

.footer-bottom-link {
  color: #9ca3af;
  font-size: .875rem;
  text-decoration: none;
  transition: color .3s;
}

.footer-bottom-link:hover {
  color: #f59e0b;
}

@media (width <= 1024px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }
}

@media (width <= 768px) {
  .footer-section {
    padding: 60px 0 0;
  }

  .footer-container {
    padding: 0 20px;
  }

  .footer-main {
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }

  .brand-name {
    font-size: 2rem;
  }

  .brand-description {
    font-size: .95rem;
  }

  .footer-bottom-content {
    text-align: center;
    flex-direction: column;
    gap: 16px;
  }

  .footer-bottom-links {
    gap: 16px;
  }
}

@media (width <= 480px) {
  .footer-section {
    padding: 40px 0 0;
  }

  .footer-container {
    padding: 0 15px;
  }

  .footer-main {
    gap: 32px;
    margin-bottom: 32px;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .brand-name {
    font-size: 1.75rem;
  }

  .brand-description {
    font-size: .9rem;
  }

  .social-links {
    gap: 12px;
  }

  .social-link {
    width: 40px;
    height: 40px;
  }

  .link-title {
    font-size: 1rem;
  }

  .footer-link {
    font-size: .9rem;
  }

  .footer-bottom {
    padding: 24px 0;
  }

  .copyright, .footer-bottom-link {
    font-size: .8rem;
  }

  .footer-bottom-links {
    gap: 12px;
  }
}


/*# sourceMappingURL=src_Style_cffb9cfd._.css.map*/