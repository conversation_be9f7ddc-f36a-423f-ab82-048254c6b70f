{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/TestimonialsSection.css"], "sourcesContent": ["/* Testimonials Section */\n.testimonials-section {\n  background: #1a1a1a;\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.testimonials-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: 80px;\n  position: relative;\n}\n\n.section-title {\n  font-size: 4rem;\n  font-weight: 400;\n  color: white;\n  line-height: 1.1;\n  margin: 0;\n  font-family: 'Georgia', serif;\n}\n\n.decorative-lines {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 300px;\n  height: 300px;\n  pointer-events: none;\n}\n\n.line {\n  position: absolute;\n  background: linear-gradient(90deg, transparent 0%, #10b981 50%, transparent 100%);\n  border-radius: 2px;\n}\n\n.line-1 {\n  width: 60px;\n  height: 2px;\n  top: 20%;\n  left: 10%;\n  transform: rotate(25deg);\n  animation: glow 3s ease-in-out infinite;\n}\n\n.line-2 {\n  width: 40px;\n  height: 2px;\n  top: 30%;\n  right: 15%;\n  transform: rotate(-15deg);\n  animation: glow 3s ease-in-out infinite 0.5s;\n}\n\n.line-3 {\n  width: 50px;\n  height: 2px;\n  bottom: 25%;\n  left: 20%;\n  transform: rotate(45deg);\n  animation: glow 3s ease-in-out infinite 1s;\n}\n\n.line-4 {\n  width: 35px;\n  height: 2px;\n  bottom: 35%;\n  right: 10%;\n  transform: rotate(-30deg);\n  animation: glow 3s ease-in-out infinite 1.5s;\n}\n\n.line-5 {\n  width: 45px;\n  height: 2px;\n  top: 50%;\n  left: 5%;\n  transform: rotate(60deg);\n  animation: glow 3s ease-in-out infinite 2s;\n}\n\n.line-6 {\n  width: 55px;\n  height: 2px;\n  top: 60%;\n  right: 5%;\n  transform: rotate(-45deg);\n  animation: glow 3s ease-in-out infinite 2.5s;\n}\n\n@keyframes glow {\n  0%, 100% { \n    opacity: 0.3;\n    transform: scale(1);\n  }\n  50% { \n    opacity: 1;\n    transform: scale(1.1);\n  }\n}\n\n.testimonials-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n  gap: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.testimonial-card {\n  background: white;\n  border-radius: 20px;\n  padding: 24px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.testimonial-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.testimonial-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n\n.testimonial-card:hover::before {\n  opacity: 1;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.avatar {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 4px 0;\n}\n\n.user-role {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n.testimonial-content {\n  margin-bottom: 16px;\n}\n\n.testimonial-text {\n  font-size: 0.95rem;\n  line-height: 1.6;\n  color: #374151;\n  margin: 0;\n  font-style: italic;\n}\n\n.rating {\n  display: flex;\n  gap: 2px;\n}\n\n.star {\n  font-size: 1rem;\n  opacity: 0.3;\n  transition: opacity 0.2s ease;\n}\n\n.star.filled {\n  opacity: 1;\n}\n\n/* Masonry-like positioning for visual interest */\n.card-1 { grid-row: span 1; }\n.card-2 { grid-row: span 1; }\n.card-3 { grid-row: span 1; }\n.card-4 { grid-row: span 1; }\n.card-5 { grid-row: span 1; }\n.card-6 { grid-row: span 1; }\n.card-7 { grid-row: span 1; }\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .testimonials-section {\n    padding: 100px 20px;\n  }\n  \n  .section-title {\n    font-size: 3.5rem;\n  }\n  \n  .testimonials-grid {\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: 20px;\n  }\n  \n  .decorative-lines {\n    width: 250px;\n    height: 250px;\n  }\n}\n\n@media (max-width: 768px) {\n  .testimonials-section {\n    padding: 80px 20px;\n    min-height: auto;\n  }\n  \n  .section-title {\n    font-size: 2.5rem;\n  }\n  \n  .section-header {\n    margin-bottom: 60px;\n  }\n  \n  .testimonials-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n  \n  .testimonial-card {\n    padding: 20px;\n  }\n  \n  .decorative-lines {\n    width: 200px;\n    height: 200px;\n  }\n  \n  .line-1, .line-2, .line-3, .line-4, .line-5, .line-6 {\n    width: 30px;\n  }\n}\n\n@media (max-width: 480px) {\n  .testimonials-section {\n    padding: 60px 15px;\n  }\n  \n  .section-title {\n    font-size: 2rem;\n  }\n  \n  .testimonial-card {\n    padding: 16px;\n  }\n  \n  .card-header {\n    gap: 12px;\n  }\n  \n  .avatar {\n    width: 40px;\n    height: 40px;\n    font-size: 1.2rem;\n  }\n  \n  .user-name {\n    font-size: 0.9rem;\n  }\n  \n  .user-role {\n    font-size: 0.8rem;\n  }\n  \n  .testimonial-text {\n    font-size: 0.9rem;\n  }\n  \n  .decorative-lines {\n    display: none;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AAKA;;;;AASA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAMF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}