{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/TestimonialsSection.css"], "sourcesContent": ["/* Testimonials Section */\n.testimonials-section {\n  background: #1a1a1a;\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n}\n\n.testimonials-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  padding: 0 40px;\n}\n\n.section-header {\n  text-align: center;\n  margin-bottom: 80px;\n  position: relative;\n}\n\n.section-title {\n  font-size: 4rem;\n  font-weight: 400;\n  color: white;\n  line-height: 1.1;\n  margin: 0 0 16px 0;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n.section-subtitle {\n  font-size: 1.25rem;\n  color: rgba(255, 255, 255, 0.8);\n  margin: 0;\n  text-align: center;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n}\n\n.carousel-container {\n  position: relative;\n  margin-top: 60px;\n}\n\n.carousel-wrapper {\n  overflow: hidden;\n  border-radius: 20px;\n}\n\n.carousel-track {\n  display: flex;\n  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  gap: 24px;\n  will-change: transform;\n}\n\n.carousel-controls {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n  pointer-events: none;\n  z-index: 10;\n}\n\n.carousel-btn {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.9);\n  border: none;\n  color: #1f2937;\n  font-size: 1.5rem;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  pointer-events: all;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n}\n\n.carousel-btn:hover {\n  background: white;\n  transform: scale(1.1);\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);\n}\n\n.carousel-btn:active {\n  transform: scale(0.95);\n}\n\n.prev-btn {\n  margin-left: -25px;\n}\n\n.next-btn {\n  margin-right: -25px;\n}\n\n.carousel-dots {\n  display: flex;\n  justify-content: center;\n  gap: 12px;\n  margin-top: 40px;\n}\n\n.dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.3);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.dot.active {\n  background: #f59e0b;\n  transform: scale(1.2);\n}\n\n.dot:hover {\n  background: rgba(255, 255, 255, 0.6);\n}\n\n.testimonial-card {\n  background: white;\n  border-radius: 20px;\n  padding: 32px;\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  flex-shrink: 0;\n  margin-right: 24px;\n  height: auto;\n  min-height: 300px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.testimonial-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.testimonial-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n\n.testimonial-card:hover::before {\n  opacity: 1;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.avatar {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 4px 0;\n}\n\n.user-role {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0 0 4px 0;\n}\n\n.user-company {\n  font-size: 0.75rem;\n  color: #9ca3af;\n  margin: 0;\n  font-style: italic;\n}\n\n.testimonial-content {\n  margin-bottom: 16px;\n}\n\n.testimonial-text {\n  font-size: 0.95rem;\n  line-height: 1.6;\n  color: #374151;\n  margin: 0;\n  font-style: italic;\n}\n\n.rating {\n  display: flex;\n  gap: 2px;\n}\n\n.star {\n  font-size: 1rem;\n  opacity: 0.3;\n  transition: opacity 0.2s ease;\n}\n\n.star.filled {\n  opacity: 1;\n}\n\n/* Masonry-like positioning for visual interest */\n.card-1 { grid-row: span 1; }\n.card-2 { grid-row: span 1; }\n.card-3 { grid-row: span 1; }\n.card-4 { grid-row: span 1; }\n.card-5 { grid-row: span 1; }\n.card-6 { grid-row: span 1; }\n.card-7 { grid-row: span 1; }\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .testimonials-section {\n    padding: 100px 20px;\n  }\n  \n  .section-title {\n    font-size: 3.5rem;\n  }\n  \n  .testimonials-grid {\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n    gap: 20px;\n  }\n  \n  .decorative-lines {\n    width: 250px;\n    height: 250px;\n  }\n}\n\n@media (max-width: 1024px) {\n  .testimonial-card {\n    padding: 28px;\n    min-height: 280px;\n  }\n\n  .carousel-btn {\n    width: 45px;\n    height: 45px;\n    font-size: 1.3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .testimonials-section {\n    padding: 80px 20px;\n    min-height: auto;\n  }\n\n  .testimonials-container {\n    padding: 0 20px;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n  }\n\n  .section-header {\n    margin-bottom: 40px;\n  }\n\n  .testimonial-card {\n    padding: 24px;\n    min-height: 250px;\n    margin-right: 16px;\n  }\n\n  .carousel-controls {\n    display: none;\n  }\n\n  .carousel-dots {\n    margin-top: 24px;\n  }\n\n  .carousel-track {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .testimonial-card {\n    padding: 20px;\n    min-height: 220px;\n  }\n\n  .section-title {\n    font-size: 2rem;\n  }\n\n  .section-subtitle {\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .testimonials-section {\n    padding: 60px 15px;\n  }\n  \n  .section-title {\n    font-size: 2rem;\n  }\n  \n  .testimonial-card {\n    padding: 16px;\n  }\n  \n  .card-header {\n    gap: 12px;\n  }\n  \n  .avatar {\n    width: 40px;\n    height: 40px;\n    font-size: 1.2rem;\n  }\n  \n  .user-name {\n    font-size: 0.9rem;\n  }\n  \n  .user-role {\n    font-size: 0.8rem;\n  }\n  \n  .testimonial-text {\n    font-size: 0.9rem;\n  }\n  \n  .decorative-lines {\n    display: none;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AAKA;;;;AASA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAOA;;;;;EAKA;;;;;;;AAOF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EASA;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}