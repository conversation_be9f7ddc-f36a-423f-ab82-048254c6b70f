"use client";
import React, { useState, useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/SavingsCalculator.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const SavingsCalculator = () => {
    const [hoursPerDay, setHoursPerDay] = useState(2);
    const [hourlyRate, setHourlyRate] = useState(50);
    const sectionRef = useRef(null);
    const calculatorRef = useRef(null);
    const illustrationRef = useRef(null);

    // Calculate savings
    const wordsPerHour = 1200; // Average typing speed
    const dailyWords = hoursPerDay * wordsPerHour;
    const monthlyWords = dailyWords * 22; // Working days
    const timeSavedHours = hoursPerDay * 0.6; // 60% time saved with Flow
    const monthlySavings = timeSavedHours * 22 * hourlyRate;
    const flowMonthlyCost = 15; // Pro plan cost
    const netSavings = monthlySavings - flowMonthlyCost;

    useEffect(() => {
        const section = sectionRef.current;
        const calculator = calculatorRef.current;
        const illustration = illustrationRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([calculator, illustration], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(illustration, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(calculator, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4");

        // Floating animation for money icons
        const moneyIcons = document.querySelectorAll('.money-icon');
        moneyIcons.forEach((icon, index) => {
            gsap.to(icon, {
                y: -10,
                duration: 2 + index * 0.3,
                ease: "power2.inOut",
                yoyo: true,
                repeat: -1,
                delay: index * 0.2
            });
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="savings-calculator-section" ref={sectionRef}>
            <div className="savings-container">
                <div className="content-section">
                    <div className="text-content">
                        <h2 className="section-title">Calculate your savings</h2>
                        <div className="scenario-text">
                            <p className="scenario-main">
                                I spend <strong>{hoursPerDay} hours</strong> typing a day ({dailyWords.toLocaleString()} words)
                            </p>
                            <p className="scenario-detail">
                                On average, people type 2 hours a day (that's {monthlyWords.toLocaleString()} words/month)
                            </p>
                            <p className="value-statement">
                                and, my time is worth <strong>${hourlyRate}/hour</strong>
                            </p>
                        </div>

                        <div className="input-controls">
                            <div className="input-group">
                                <label htmlFor="hours">Hours per day:</label>
                                <input
                                    type="range"
                                    id="hours"
                                    min="1"
                                    max="8"
                                    step="0.5"
                                    value={hoursPerDay}
                                    onChange={(e) => setHoursPerDay(parseFloat(e.target.value))}
                                    className="slider"
                                />
                                <span className="slider-value">{hoursPerDay}h</span>
                            </div>
                            
                            <div className="input-group">
                                <label htmlFor="rate">Hourly rate:</label>
                                <input
                                    type="range"
                                    id="rate"
                                    min="25"
                                    max="200"
                                    step="5"
                                    value={hourlyRate}
                                    onChange={(e) => setHourlyRate(parseInt(e.target.value))}
                                    className="slider"
                                />
                                <span className="slider-value">${hourlyRate}</span>
                            </div>
                        </div>
                    </div>

                    <div className="illustration-section" ref={illustrationRef}>
                        <div className="character-illustration">
                            <div className="character">
                                <div className="character-head">
                                    <div className="face">😊</div>
                                    <div className="hat">🎩</div>
                                </div>
                                <div className="character-body">
                                    <div className="arms">
                                        <div className="arm left">🤲</div>
                                        <div className="arm right">🤲</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="money-animation">
                                <div className="money-icon" style={{top: '10%', left: '20%'}}>💰</div>
                                <div className="money-icon" style={{top: '30%', right: '15%'}}>💵</div>
                                <div className="money-icon" style={{top: '50%', left: '10%'}}>💸</div>
                                <div className="money-icon" style={{top: '70%', right: '25%'}}>💰</div>
                                <div className="money-icon" style={{top: '20%', right: '35%'}}>💵</div>
                                <div className="money-icon" style={{top: '80%', left: '30%'}}>💸</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="calculator-section" ref={calculatorRef}>
                    <div className="calculator-card">
                        <div className="calculator-header">
                            <h3>Monthly, you'll save</h3>
                            <div className="savings-amount">
                                ${netSavings.toLocaleString()}<span className="period">/mo</span>
                            </div>
                        </div>

                        <div className="breakdown">
                            <div className="breakdown-item">
                                <span className="label">Hours spent typing</span>
                                <span className="value">{(hoursPerDay * 22).toFixed(0)}h</span>
                            </div>
                            <div className="breakdown-item">
                                <span className="label">Hours saved monthly</span>
                                <span className="value">{(timeSavedHours * 22).toFixed(0)}h</span>
                            </div>
                            <div className="breakdown-item">
                                <span className="label">Time value saved</span>
                                <span className="value">${monthlySavings.toLocaleString()}</span>
                            </div>
                            <div className="breakdown-item">
                                <span className="label">Flow Pro monthly cost</span>
                                <span className="value negative">-${flowMonthlyCost}</span>
                            </div>
                        </div>

                        <div className="calculator-footer">
                            <button className="get-started-btn">
                                Start saving time today
                                <span className="arrow">→</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default SavingsCalculator;
