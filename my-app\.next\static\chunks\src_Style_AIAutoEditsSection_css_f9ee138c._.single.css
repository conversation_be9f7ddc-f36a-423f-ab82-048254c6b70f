/* [project]/src/Style/AIAutoEditsSection.css [app-client] (css) */
.ai-auto-edits-section {
  background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 50%, #fdba74 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.ai-auto-edits-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 70% 30%, #f59e0b1a 0%, #0000 50%), radial-gradient(circle at 30% 70%, #fbbf241a 0%, #0000 50%);
  position: absolute;
  inset: 0;
}

.ai-auto-edits-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
}

.phone-mockup-container {
  justify-content: center;
  align-items: center;
  display: flex;
}

.phone-device {
  background: linear-gradient(145deg, #1f2937, #374151);
  border-radius: 30px;
  width: 320px;
  height: 640px;
  padding: 8px;
  position: relative;
  box-shadow: 0 25px 50px #0000004d;
}

.phone-device:before {
  content: "";
  background: #6b7280;
  border-radius: 2px;
  width: 60px;
  height: 4px;
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}

.phone-screen {
  background: #111827;
  border-radius: 22px;
  flex-direction: column;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.screen-header {
  background: #1f2937;
  padding: 12px 16px 8px;
}

.status-bar {
  color: #fff;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.status-icons {
  gap: 4px;
  display: flex;
}

.app-header h3 {
  color: #fff;
  text-align: center;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.screen-content {
  flex: 1;
  padding: 20px 16px;
  overflow-y: auto;
}

.text-editor {
  flex-direction: column;
  gap: 20px;
  height: 100%;
  display: flex;
}

.original-text {
  background: #374151;
  border-left: 4px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
}

.original-text p {
  color: #e5e7eb;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.ai-suggestions {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 12px;
  padding: 16px;
}

.suggestion-header {
  color: #60a5fa;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.suggestion-items {
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  display: flex;
}

.suggestion-item {
  color: #d1d5db;
  opacity: .6;
  background: #374151;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 13px;
  transition: all .3s;
}

.suggestion-item.active {
  color: #fff;
  opacity: 1;
  background: #3b82f6;
  transform: scale(1.02);
}

.action-buttons {
  gap: 8px;
  display: flex;
}

.apply-btn, .preview-btn {
  cursor: pointer;
  border: none;
  border-radius: 6px;
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  transition: all .3s;
}

.apply-btn {
  color: #fff;
  background: #10b981;
}

.apply-btn:hover {
  background: #059669;
}

.preview-btn {
  color: #d1d5db;
  background: #374151;
}

.preview-btn:hover {
  background: #4b5563;
}

.improved-text {
  background: #065f46;
  border-left: 4px solid #10b981;
  border-radius: 12px;
  padding: 16px;
}

.improved-header {
  color: #6ee7b7;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.typing-text {
  color: #d1fae5;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  animation: 4s steps(40, end) infinite typewriter;
}

@keyframes typewriter {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 100%;
  }
}

.content-section {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.content-badge {
  color: #92400e;
  background: #fbbf2433;
  border: 1px solid #fbbf244d;
  border-radius: 20px;
  align-items: center;
  gap: 8px;
  width: fit-content;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.sparkle {
  animation: 2s ease-in-out infinite sparkle;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1)rotate(0);
  }

  50% {
    transform: scale(1.2)rotate(180deg);
  }
}

.section-title {
  color: #92400e;
  text-shadow: 0 2px 4px #0000001a;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.section-description {
  color: #78350f;
  text-shadow: 0 1px 2px #0000001a;
  max-width: 500px;
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-list {
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin: 20px 0;
  display: grid;
}

.feature-item {
  color: #92400e;
  backdrop-filter: blur(10px);
  background: #ffffff80;
  border: 1px solid #ffffff4d;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.feature-icon {
  font-size: 18px;
}

.cta-button {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  width: fit-content;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
  box-shadow: 0 8px 25px #f59e0b4d;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px #f59e0b66;
}

.arrow {
  transition: transform .3s;
}

.cta-button:hover .arrow {
  transform: translateX(4px);
}

@media (width <= 1024px) {
  .ai-auto-edits-container {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .phone-device {
    width: 280px;
    height: 560px;
  }
}

@media (width <= 768px) {
  .ai-auto-edits-section {
    padding: 80px 20px;
  }

  .ai-auto-edits-container {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .phone-device {
    width: 260px;
    height: 520px;
  }
}

@media (width <= 480px) {
  .ai-auto-edits-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .phone-device {
    width: 240px;
    height: 480px;
  }

  .screen-content {
    padding: 16px 12px;
  }
}

/*# sourceMappingURL=src_Style_AIAutoEditsSection_css_f9ee138c._.single.css.map*/