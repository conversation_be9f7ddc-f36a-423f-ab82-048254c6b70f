"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import "@/Style/VoiceHeroSection.css";

const VoiceHeroSection = () => {
    const sectionRef = useRef(null);
    const curvedTextRef = useRef(null);
    const voiceIndicatorRef = useRef(null);
    const mainTextRef = useRef(null);

    useEffect(() => {
        const section = sectionRef.current;
        const curvedText = curvedTextRef.current;
        const voiceIndicator = voiceIndicatorRef.current;
        const mainText = mainTextRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([curvedText, voiceIndicator, mainText], { opacity: 0, y: 30 });

        // Create entrance timeline
        const tl = gsap.timeline({ delay: 0.5 });

        tl.to(curvedText, {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out"
        })
        .to(voiceIndicator, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.5")
        .to(mainText, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4");

        // Animate curved text continuously
        gsap.to(curvedText, {
            rotation: 360,
            duration: 20,
            ease: "none",
            repeat: -1
        });

        // Voice indicator animation
        const waves = voiceIndicator?.querySelectorAll('.voice-wave');
        if (waves) {
            waves.forEach((wave, index) => {
                gsap.to(wave, {
                    scaleY: Math.random() * 0.5 + 0.5,
                    duration: 0.5 + Math.random() * 0.5,
                    ease: "power2.inOut",
                    yoyo: true,
                    repeat: -1,
                    delay: index * 0.1
                });
            });
        }

        return () => {
            gsap.killTweensOf([curvedText, voiceIndicator, mainText]);
        };
    }, []);

    return (
        <section className="voice-hero-section" ref={sectionRef}>
            <div className="voice-hero-container">
                <div className="curved-text-container">
                    <div className="curved-text" ref={curvedTextRef}>
                        <svg viewBox="0 0 200 200" className="curved-svg">
                            <defs>
                                <path
                                    id="circle"
                                    d="M 100, 100 m -75, 0 a 75,75 0 1,1 150,0 a 75,75 0 1,1 -150,0"
                                />
                            </defs>
                            <text className="curved-text-path">
                                <textPath href="#circle" startOffset="0%">
                                    their meeting were sent out, or mentioned it but didn't capture it •
                                </textPath>
                            </text>
                        </svg>
                    </div>
                </div>

                <div className="voice-indicator-container" ref={voiceIndicatorRef}>
                    <div className="voice-indicator">
                        <div className="voice-wave"></div>
                        <div className="voice-wave"></div>
                        <div className="voice-wave"></div>
                        <div className="voice-wave"></div>
                        <div className="voice-wave"></div>
                        <div className="voice-wave"></div>
                        <div className="voice-wave"></div>
                        <div className="voice-wave"></div>
                    </div>
                    <div className="voice-label">meeting were sent out, or</div>
                </div>

                <div className="main-content" ref={mainTextRef}>
                    <h1 className="hero-title">
                        Don't type, <span className="highlight">just speak</span>
                    </h1>
                    
                    <p className="hero-subtitle">
                        Effortless voice dictation in every application:<br />
                        4x faster than typing, AI commands and auto-edits.
                    </p>

                    <div className="cta-buttons">
                        <button className="primary-btn">
                            🎤 Try Flow
                        </button>
                        <button className="secondary-btn">
                            Download
                        </button>
                    </div>

                    <p className="availability">
                        Available on Mac, Windows and iPhone
                    </p>
                </div>
            </div>
        </section>
    );
};

export default VoiceHeroSection;
