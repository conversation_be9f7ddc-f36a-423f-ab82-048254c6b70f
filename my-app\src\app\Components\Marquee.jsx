"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import "@/Style/Marquee.css";


const Marquee = () => {
    const wrapperRef = useRef(null);
    const loopRef = useRef(null);

    const colors = ["#f38630", "#6fb936", "#ccc", "#6fb936"];

    useEffect(() => {
        const boxes = gsap.utils.toArray(".box");

        // Apply background color
        gsap.set(boxes, {
            backgroundColor: gsap.utils.wrap(colors),
        });

        // Call horizontalLoop with the boxes
        loopRef.current = horizontalLoop(boxes, {
            paused: false,
            repeat: -1,
            speed: 1,
        });

        return () => {
            loopRef.current?.kill();
        };
    }, []);
    function horizontalLoop(items, config) {
        items = gsap.utils.toArray(items);
        config = config || {};
        let tl = gsap.timeline({
            repeat: config.repeat,
            paused: config.paused,
            defaults: { ease: "none" },
            onReverseComplete: () => tl.totalTime(tl.rawTime() + tl.duration() * 100),
        }),
            length = items.length,
            startX = items[0].offsetLeft,
            times = [],
            widths = [],
            xPercents = [],
            curIndex = 0,
            pixelsPerSecond = (config.speed || 1) * 100,
            snap = config.snap === false
                ? (v) => v
                : gsap.utils.snap(config.snap || 1),
            totalWidth,
            curX,
            distanceToStart,
            distanceToLoop,
            item,
            i;

        gsap.set(items, {
            xPercent: (i, el) => {
                let w = (widths[i] = parseFloat(gsap.getProperty(el, "width", "px")));
                xPercents[i] = snap(
                    (parseFloat(gsap.getProperty(el, "x", "px")) / w) * 100 +
                    gsap.getProperty(el, "xPercent")
                );
                return xPercents[i];
            },
        });
        gsap.set(items, { x: 0 });
        totalWidth =
            items[length - 1].offsetLeft +
            (xPercents[length - 1] / 100) * widths[length - 1] -
            startX +
            items[length - 1].offsetWidth * gsap.getProperty(items[length - 1], "scaleX") +
            (parseFloat(config.paddingRight) || 0);

        for (i = 0; i < length; i++) {
            item = items[i];
            curX = (xPercents[i] / 100) * widths[i];
            distanceToStart = item.offsetLeft + curX - startX;
            distanceToLoop = distanceToStart + widths[i] * gsap.getProperty(item, "scaleX");

            tl.to(
                item,
                {
                    xPercent: snap(((curX - distanceToLoop) / widths[i]) * 100),
                    duration: distanceToLoop / pixelsPerSecond,
                },
                0
            )
                .fromTo(
                    item,
                    {
                        xPercent: snap(
                            ((curX - distanceToLoop + totalWidth) / widths[i]) * 100
                        ),
                    },
                    {
                        xPercent: xPercents[i],
                        duration:
                            (curX - distanceToLoop + totalWidth - curX) / pixelsPerSecond,
                        immediateRender: false,
                    },
                    distanceToLoop / pixelsPerSecond
                )
                .add("label" + i, distanceToStart / pixelsPerSecond);

            times[i] = distanceToStart / pixelsPerSecond;
        }

        function toIndex(index, vars) {
            vars = vars || {};
            if (Math.abs(index - curIndex) > length / 2) {
                index += index > curIndex ? -length : length;
            }
            let newIndex = gsap.utils.wrap(0, length, index),
                time = times[newIndex];
            if ((time > tl.time()) !== index > curIndex) {
                vars.modifiers = { time: gsap.utils.wrap(0, tl.duration()) };
                time += tl.duration() * (index > curIndex ? 1 : -1);
            }
            curIndex = newIndex;
            vars.overwrite = true;
            return tl.tweenTo(time, vars);
        }

        tl.next = (vars) => toIndex(curIndex + 1, vars);
        tl.previous = (vars) => toIndex(curIndex - 1, vars);
        tl.current = () => curIndex;
        tl.toIndex = (index, vars) => toIndex(index, vars);
        tl.times = times;
        tl.progress(1, true).progress(0, true);
        if (config.reversed) {
            tl.vars.onReverseComplete();
            tl.reverse();
        }
        return tl;
    }



    return (
        <div style={styles.body}>
            <div className="wrapper" style={styles.wrapper} ref={wrapperRef}>
                {[...Array(11).keys()].map((i) => (
                    <div key={i} className="box" style={styles.box}>
                        <div
                            className={i + 1 === 8 ? "test-2" : "test"}
                            style={i + 1 === 8 ? styles.test2 : styles.test}
                        >
                            {i + 1}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Marquee;