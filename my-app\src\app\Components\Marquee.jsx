"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import "@/Style/Marquee.css";

const Marquee = ({
    items = [
        "🎤 Voice-First Productivity",
        "⚡ 4x Faster Than Typing",
        "🤖 AI-Powered Accuracy",
        "🌍 50+ Languages Supported",
        "📱 Cross-Platform Ready",
        "🔄 Real-Time Corrections",
        "✨ Smart Auto-Formatting",
        "🎯 99.9% Accuracy Rate"
    ],
    speed = 1, // speed multiplier
    colors = ["#f59e0b", "#10b981", "#3b82f6", "#8b5cf6", "#ef4444", "#06b6d4", "#84cc16", "#f97316"]
}) => {
    const wrapperRef = useRef(null);
    const animationRef = useRef(null);

    useEffect(() => {
        const wrapper = wrapperRef.current;
        if (!wrapper) return;

        const marqueeItems = wrapper.querySelectorAll(".marquee-item");

        // Apply background colors
        gsap.set(marqueeItems, {
            backgroundColor: gsap.utils.wrap(colors),
        });

        // Create infinite scroll animation
        const createAnimation = () => {
            if (marqueeItems.length === 0) return;

            // Calculate the total width of one set of items
            let totalWidth = 0;
            const itemCount = items.length; // Original items count

            for (let i = 0; i < itemCount; i++) {
                if (marqueeItems[i]) {
                    totalWidth += marqueeItems[i].offsetWidth + 20; // 20px margin
                }
            }

            // Animate the wrapper to move left infinitely
            animationRef.current = gsap.to(wrapper, {
                x: -totalWidth,
                duration: totalWidth / (50 * speed), // Adjust duration based on speed
                ease: "none",
                repeat: -1,
            });
        };

        // Wait for layout to be ready
        const timer = setTimeout(createAnimation, 200);

        return () => {
            clearTimeout(timer);
            if (animationRef.current) {
                animationRef.current.kill();
            }
        };
    }, [items, speed, colors]);
    return (
        <div className="marquee-container">
            <div className="marquee-wrapper" ref={wrapperRef}>
                {/* Duplicate items for seamless infinite loop */}
                {[...items, ...items].map((item, index) => (
                    <div key={index} className="marquee-item">
                        <div className="marquee-content">
                            {item}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Marquee;