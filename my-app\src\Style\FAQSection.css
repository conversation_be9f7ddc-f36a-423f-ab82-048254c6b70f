/* FAQ Section */
.faq-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  padding: 120px 20px;
  min-height: 100vh;
}

.faq-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.faq-header {
  text-align: center;
  margin-bottom: 60px;
}

.faq-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #92400e;
  margin: 0;
  line-height: 1.1;
  font-family: 'Georgia', serif;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 80px;
}

.faq-item {
  background: white;
  border-radius: 16px;
  border: 2px solid transparent;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.faq-item:hover {
  border-color: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.faq-item.active {
  border-color: #f59e0b;
  box-shadow: 0 8px 30px rgba(245, 158, 11, 0.2);
}

.faq-question {
  padding: 24px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  transition: background-color 0.3s ease;
}

.faq-question:hover {
  background-color: #fef3c7;
}

.faq-item.active .faq-question {
  background-color: #fef3c7;
  border-bottom: 1px solid #fde68a;
}

.faq-question h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.faq-icon {
  color: #f59e0b;
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(245, 158, 11, 0.1);
}

.faq-answer {
  height: 0;
  opacity: 0;
  overflow: hidden;
  transition: all 0.4s ease;
}

.faq-item.active .faq-answer {
  height: auto;
  opacity: 1;
}

.faq-answer p {
  padding: 0 24px 24px 24px;
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  color: #4b5563;
}

.faq-footer {
  text-align: center;
  background: rgba(255, 255, 255, 0.6);
  padding: 48px 32px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.contact-support h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #92400e;
  margin: 0 0 16px 0;
}

.contact-support p {
  font-size: 1.125rem;
  color: #78350f;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.contact-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.arrow {
  transition: transform 0.3s ease;
}

.contact-btn:hover .arrow {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .faq-title {
    font-size: 3rem;
  }
  
  .faq-grid {
    gap: 20px;
  }
  
  .contact-support h3 {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .faq-section {
    padding: 80px 20px;
  }
  
  .faq-title {
    font-size: 2.5rem;
  }
  
  .faq-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .faq-question {
    padding: 20px;
  }
  
  .faq-question h3 {
    font-size: 1rem;
  }
  
  .faq-answer p {
    padding: 0 20px 20px 20px;
    font-size: 0.875rem;
  }
  
  .faq-footer {
    padding: 32px 24px;
  }
  
  .contact-support h3 {
    font-size: 1.5rem;
  }
  
  .contact-support p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .faq-section {
    padding: 60px 15px;
  }
  
  .faq-title {
    font-size: 2rem;
  }
  
  .faq-question {
    padding: 16px;
  }
  
  .faq-question h3 {
    font-size: 0.875rem;
  }
  
  .faq-icon {
    width: 28px;
    height: 28px;
  }
  
  .faq-answer p {
    padding: 0 16px 16px 16px;
    font-size: 0.8rem;
  }
  
  .faq-footer {
    padding: 24px 20px;
  }
  
  .contact-support h3 {
    font-size: 1.25rem;
  }
  
  .contact-support p {
    font-size: 0.875rem;
  }
  
  .contact-btn {
    padding: 14px 24px;
    font-size: 1rem;
  }
}
