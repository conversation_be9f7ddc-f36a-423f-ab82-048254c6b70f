"use client";
import React, { useState, useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/FAQSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const FAQSection = () => {
    const [openFAQ, setOpenFAQ] = useState(null);
    const sectionRef = useRef(null);
    const faqItemsRef = useRef([]);

    const faqs = [
        {
            question: "How do I upgrade?",
            answer: "You can upgrade your plan at any time from your account settings. Simply go to the billing section, select your desired plan, and follow the payment process. Your upgrade will take effect immediately, and you'll have access to all the new features right away."
        },
        {
            question: "Is there a free trial or free plan available?",
            answer: "Yes! We offer a free plan that includes 2,000 words per month with basic writing assistance. You can also start a 14-day free trial of our Pro plan to experience all premium features before committing to a paid subscription."
        },
        {
            question: "Can I change or cancel my subscription at any time?",
            answer: "Absolutely! You have full control over your subscription. You can upgrade, downgrade, or cancel at any time from your account settings. If you cancel, you'll continue to have access to your paid features until the end of your current billing period."
        },
        {
            question: "Do you offer discounts for specific groups?",
            answer: "Yes, we offer special discounts for students, educators, non-profit organizations, and startups. We also provide volume discounts for teams of 10 or more users. Contact our sales team to learn more about available discounts and how to apply them."
        },
        {
            question: "What integrations are included in each plan?",
            answer: "Our Free plan includes basic integrations with Google Docs and Microsoft Word. The Pro plan adds integrations with Slack, Notion, and popular email clients. Team and Enterprise plans include all integrations plus custom API access and priority support for new integration requests."
        },
        {
            question: "How does the word count limit work?",
            answer: "Word count limits reset monthly on your billing date. Unused words don't roll over to the next month. If you exceed your limit, you can either upgrade your plan or purchase additional word packs. We'll notify you when you're approaching your limit."
        }
    ];

    const toggleFAQ = (index) => {
        setOpenFAQ(openFAQ === index ? null : index);
    };

    useEffect(() => {
        const section = sectionRef.current;
        const faqItems = faqItemsRef.current;

        if (!section) return;

        // Initial setup
        gsap.set(faqItems, { opacity: 0, y: 30 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        // Animate FAQ items in sequence
        tl.to(faqItems, {
            opacity: 1,
            y: 0,
            duration: 0.6,
            ease: "power3.out",
            stagger: 0.1
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    useEffect(() => {
        // Animate FAQ content when opened/closed
        faqItemsRef.current.forEach((item, index) => {
            if (!item) return;
            
            const content = item.querySelector('.faq-answer');
            const icon = item.querySelector('.faq-icon');
            
            if (openFAQ === index) {
                gsap.to(content, {
                    height: 'auto',
                    opacity: 1,
                    duration: 0.4,
                    ease: "power3.out"
                });
                gsap.to(icon, {
                    rotation: 180,
                    duration: 0.3,
                    ease: "power3.out"
                });
            } else {
                gsap.to(content, {
                    height: 0,
                    opacity: 0,
                    duration: 0.3,
                    ease: "power3.in"
                });
                gsap.to(icon, {
                    rotation: 0,
                    duration: 0.3,
                    ease: "power3.out"
                });
            }
        });
    }, [openFAQ]);

    return (
        <section className="faq-section" ref={sectionRef}>
            <div className="faq-container">
                <div className="faq-header">
                    <h2 className="faq-title">Frequently asked questions</h2>
                </div>

                <div className="faq-grid">
                    {faqs.map((faq, index) => (
                        <div 
                            key={index}
                            className={`faq-item ${openFAQ === index ? 'active' : ''}`}
                            ref={el => faqItemsRef.current[index] = el}
                        >
                            <div 
                                className="faq-question"
                                onClick={() => toggleFAQ(index)}
                            >
                                <h3>{faq.question}</h3>
                                <div className="faq-icon">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                        <path 
                                            d="M5 7.5L10 12.5L15 7.5" 
                                            stroke="currentColor" 
                                            strokeWidth="2" 
                                            strokeLinecap="round" 
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                </div>
                            </div>
                            <div className="faq-answer">
                                <p>{faq.answer}</p>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="faq-footer">
                    <div className="contact-support">
                        <h3>Still have questions?</h3>
                        <p>Can't find the answer you're looking for? Please chat with our friendly team.</p>
                        <button className="contact-btn">
                            Get in touch
                            <span className="arrow">→</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default FAQSection;
