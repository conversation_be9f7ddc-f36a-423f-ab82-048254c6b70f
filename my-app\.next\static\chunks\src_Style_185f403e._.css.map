{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/Marquee.css"], "sourcesContent": ["/* Marquee Container */\r\n.marquee-container {\r\n  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  width: 100%;\r\n  min-height: 100px;\r\n  padding: 20px 0;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.marquee-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(90deg,\r\n    rgba(0,0,0,0.15) 0%,\r\n    transparent 15%,\r\n    transparent 85%,\r\n    rgba(0,0,0,0.15) 100%);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n.marquee-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: nowrap;\r\n  will-change: transform;\r\n}\r\n\r\n/* Marquee Items */\r\n.marquee-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  flex-shrink: 0;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  min-width: fit-content;\r\n}\r\n\r\n.marquee-content {\r\n  padding: 12px 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: white;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  white-space: nowrap;\r\n  letter-spacing: 0.5px;\r\n  user-select: none;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .marquee-content {\r\n    font-size: 15px;\r\n    padding: 11px 18px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .marquee-container {\r\n    min-height: 80px;\r\n    padding: 15px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 14px;\r\n    padding: 10px 16px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 15px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .marquee-container {\r\n    min-height: 70px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 13px;\r\n    padding: 8px 14px;\r\n    letter-spacing: 0.3px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 320px) {\r\n  .marquee-container {\r\n    min-height: 60px;\r\n    padding: 10px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 10px;\r\n  }\r\n}\r\n\r\n/* Legacy styles for backward compatibility */\r\n.MarqueeBox {\r\n  font-family: system-ui;\r\n  background: #111;\r\n  color: white;\r\n  text-align: center;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 20vh;\r\n}\r\n\r\n.carousel {\r\n  background: blue;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: green;\r\n  margin: 0;\r\n  padding: 0;\r\n  position: relative;\r\n  color: black;\r\n  font-size: 121px;\r\n  cursor: pointer;\r\n}\r\n\r\n.test {\r\n  padding: 20px;\r\n}\r\n\r\n.test-2 {\r\n  padding: 20px 10px;\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;AAaA;;;;;;;;;AAgBA;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;;;;;;;AAYA;EACE;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;;EAMA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAMF;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;AAIA", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/FeaturesShowcase.css"], "sourcesContent": ["/* Features Showcase Styles */\n.features-showcase {\n  padding: 80px 20px;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  min-height: 100vh;\n}\n\n.features-container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.features-header {\n  text-align: center;\n  margin-bottom: 60px;\n}\n\n.features-title {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 16px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.features-subtitle {\n  font-size: 1.2rem;\n  color: #718096;\n  max-width: 600px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 30px;\n  align-items: start;\n}\n\n.feature-card {\n  border-radius: 20px;\n  padding: 30px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.feature-large {\n  grid-column: span 2;\n  min-height: 400px;\n}\n\n.feature-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 20px;\n}\n\n.feature-image {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n}\n\n.phone-mockup {\n  width: 200px;\n  height: 300px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 25px;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n}\n\n/* AI Interface */\n.ai-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.ai-suggestions {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.suggestion-item {\n  background: rgba(255, 255, 255, 0.1);\n  padding: 8px 12px;\n  border-radius: 8px;\n  font-size: 12px;\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.ai-controls {\n  display: flex;\n  justify-content: center;\n}\n\n.ai-btn {\n  background: #4facfe;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.ai-btn:hover {\n  background: #3d8bfe;\n  transform: scale(1.05);\n}\n\n/* Dictionary Interface */\n.dictionary-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.dict-header {\n  color: #4facfe;\n  font-weight: 600;\n  text-align: center;\n  font-size: 14px;\n}\n\n.dict-items {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.dict-item {\n  background: rgba(79, 172, 254, 0.2);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 15px;\n  font-size: 11px;\n  text-align: center;\n  border: 1px solid rgba(79, 172, 254, 0.3);\n}\n\n/* Tones Interface */\n.tones-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  justify-content: center;\n}\n\n.app-selector {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n}\n\n.app-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 10px;\n  background: rgba(255, 255, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.app-icon.active {\n  background: rgba(79, 172, 254, 0.3);\n  border: 2px solid #4facfe;\n}\n\n.tone-options {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.tone-btn {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 12px;\n  font-size: 11px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tone-btn.active {\n  background: rgba(79, 172, 254, 0.3);\n  border: 1px solid #4facfe;\n}\n\n/* Languages Interface */\n.languages-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.lang-wheel {\n  position: relative;\n  width: 120px;\n  height: 120px;\n  border-radius: 50%;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n}\n\n.lang-item {\n  position: absolute;\n  width: 30px;\n  height: 30px;\n  background: rgba(67, 233, 123, 0.3);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: 600;\n  color: white;\n  border: 1px solid rgba(67, 233, 123, 0.5);\n}\n\n.lang-item:nth-child(1) { top: -15px; left: 50%; transform: translateX(-50%); }\n.lang-item:nth-child(2) { top: 15px; right: -15px; }\n.lang-item:nth-child(3) { bottom: 15px; right: -15px; }\n.lang-item:nth-child(4) { bottom: -15px; left: 50%; transform: translateX(-50%); }\n.lang-item:nth-child(5) { bottom: 15px; left: -15px; }\n.lang-item:nth-child(6) { top: 15px; left: -15px; }\n\n.lang-center {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(67, 233, 123, 0.5);\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  color: white;\n  font-size: 12px;\n  border: 2px solid rgba(67, 233, 123, 0.7);\n}\n\n.feature-text {\n  text-align: center;\n}\n\n.feature-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 12px;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.feature-description {\n  font-size: 1rem;\n  line-height: 1.6;\n  opacity: 0.9;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .features-showcase {\n    padding: 60px 15px;\n  }\n  \n  .features-title {\n    font-size: 2.5rem;\n  }\n  \n  .features-grid {\n    grid-template-columns: 1fr;\n    gap: 20px;\n  }\n  \n  .feature-large {\n    grid-column: span 1;\n  }\n  \n  .feature-card {\n    padding: 20px;\n  }\n  \n  .phone-mockup {\n    width: 150px;\n    height: 220px;\n    padding: 15px;\n  }\n}\n\n@media (max-width: 480px) {\n  .features-title {\n    font-size: 2rem;\n  }\n  \n  .features-subtitle {\n    font-size: 1rem;\n  }\n  \n  .phone-mockup {\n    width: 120px;\n    height: 180px;\n    padding: 10px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;;;AAeA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;;AACA;;;;;AACA;;;;;AACA;;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;;;;AAOA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA", "debugId": null}}]}