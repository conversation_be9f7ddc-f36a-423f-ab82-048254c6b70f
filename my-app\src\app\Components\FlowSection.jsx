"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/FlowSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const FlowSection = () => {
    const sectionRef = useRef(null);
    const titleRef = useRef(null);
    const tagsRef = useRef([]);
    const contentRef = useRef(null);
    const illustrationRef = useRef(null);

    const professionTags = [
        "Accessibility", "Consultants", "Creators", "Customer Support",
        "Designers", "HR", "Managers", "Publishers", "Ergonomics",
        "Freelancers", "Government", "Healthcare", "Individuals",
        "Journalists", "Lawyers", "Multilingual", "Product", "Sales",
        "Slower Typists", "Students", "Teams", "Writers"
    ];

    useEffect(() => {
        const section = sectionRef.current;
        const title = titleRef.current;
        const tags = tagsRef.current;
        const content = contentRef.current;
        const illustration = illustrationRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([title, content, illustration], { opacity: 0, y: 50 });
        gsap.set(tags, { opacity: 0, scale: 0.8 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 80%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(title, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(tags, {
            opacity: 1,
            scale: 1,
            duration: 0.6,
            stagger: 0.05,
            ease: "back.out(1.7)"
        }, "-=0.4")
        .to([content, illustration], {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.2,
            ease: "power3.out"
        }, "-=0.3");

        // Floating animation for illustration
        gsap.to(illustration, {
            y: -10,
            duration: 2,
            ease: "power2.inOut",
            yoyo: true,
            repeat: -1
        });

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="flow-section" ref={sectionRef}>
            <div className="flow-container">
                <div className="flow-content">
                    <div className="flow-left">
                        <h1 className="flow-title" ref={titleRef}>
                            Flow is made<br />for you
                        </h1>
                        
                        <div className="profession-tags">
                            {professionTags.map((tag, index) => (
                                <span 
                                    key={index}
                                    className="profession-tag"
                                    ref={el => tagsRef.current[index] = el}
                                >
                                    {tag}
                                </span>
                            ))}
                        </div>
                    </div>

                    <div className="flow-right">
                        <div className="flow-accessibility" ref={contentRef}>
                            <h2 className="accessibility-title">Flow for Accessibility</h2>
                            <p className="accessibility-description">
                                Your voice deserves a shortcut. Flow supports anyone who 
                                feels slowed down by a keyboard by turning speech into 
                                structured, polished text—quickly, reliably, naturally.
                            </p>
                            <button className="get-started-btn">Get started</button>
                        </div>

                        <div className="flow-illustration" ref={illustrationRef}>
                            <div className="character-container">
                                <div className="character">
                                    <div className="character-head">
                                        <div className="character-face">
                                            <div className="eye left-eye"></div>
                                            <div className="eye right-eye"></div>
                                            <div className="mouth"></div>
                                        </div>
                                        <div className="character-ears">
                                            <div className="ear left-ear"></div>
                                            <div className="ear right-ear"></div>
                                        </div>
                                    </div>
                                    <div className="character-body">
                                        <div className="character-arms">
                                            <div className="arm left-arm"></div>
                                            <div className="arm right-arm"></div>
                                        </div>
                                    </div>
                                </div>
                                <div className="speech-bubble">
                                    <div className="bubble-content">
                                        <div className="text-lines">
                                            <div className="text-line"></div>
                                            <div className="text-line"></div>
                                            <div className="text-line short"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default FlowSection;
