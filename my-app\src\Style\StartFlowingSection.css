/* Start Flowing Section */
.start-flowing-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-attachment: fixed;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.background-blur {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(1px);
  z-index: 2;
}

.dotted-line-svg {
  position: absolute;
  top: 0;
  right: 0;
  width: 60%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

.dotted-line {
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.start-flowing-container {
  position: relative;
  z-index: 4;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 20%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 15%;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation: float 7s ease-in-out infinite;
}

.floating-triangle {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.triangle-1 {
  border-left: 25px solid transparent;
  border-right: 25px solid transparent;
  border-bottom: 43px solid rgba(255, 255, 255, 0.1);
  top: 30%;
  right: 20%;
  animation: rotate 10s linear infinite;
}

.triangle-2 {
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 26px solid rgba(255, 255, 255, 0.1);
  bottom: 30%;
  right: 10%;
  animation: rotate 12s linear infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content-wrapper {
  max-width: 700px;
  text-align: center;
  margin: 0 auto;
  position: relative;
  z-index: 5;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.main-title {
  font-size: 4.5rem;
  font-weight: 400;
  color: white;
  margin: 0 0 24px 0;
  font-family: 'Georgia', serif;
  line-height: 1.1;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
}

.title-dots {
  color: rgba(255, 255, 255, 0.7);
  animation: dots-fade 2s ease-in-out infinite;
}

@keyframes dots-fade {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.3; }
}

.subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0 0 32px 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  font-weight: 400;
}

.feature-highlights {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.feature-highlight {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-highlight:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 1.1rem;
}

.cta-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.primary-cta {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
  position: relative;
  overflow: hidden;
}

.primary-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.primary-cta:hover::before {
  left: 100%;
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);
}

.secondary-cta {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 14px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.secondary-cta:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

.availability-text {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-title {
    font-size: 3.5rem;
  }
  
  .subtitle {
    font-size: 1.125rem;
  }
  
  .dotted-line-svg {
    width: 70%;
  }
}

@media (max-width: 768px) {
  .start-flowing-section {
    min-height: 90vh;
    padding: 60px 0;
  }

  .start-flowing-container {
    padding: 0 20px;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1rem;
    margin-bottom: 24px;
  }

  .feature-highlights {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .primary-cta,
  .secondary-cta {
    width: 100%;
    max-width: 280px;
    padding: 16px 24px;
    font-size: 1rem;
  }

  .dotted-line-svg {
    width: 80%;
  }

  .content-wrapper {
    max-width: 500px;
  }

  .floating-elements {
    display: none;
  }
}

@media (max-width: 480px) {
  .start-flowing-container {
    padding: 0 15px;
  }
  
  .main-title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 0.95rem;
  }
  
  .primary-cta,
  .secondary-cta {
    font-size: 0.95rem;
    padding: 14px 20px;
  }
  
  .availability-text {
    font-size: 0.875rem;
  }
  
  .dotted-line-svg {
    display: none;
  }
}
