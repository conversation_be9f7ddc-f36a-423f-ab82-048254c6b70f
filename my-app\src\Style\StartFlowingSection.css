/* Start Flowing Section */
.start-flowing-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 50%, #fdba74 100%);
  overflow: hidden;
}

.start-flowing-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  min-height: 80vh;
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 400;
  color: #92400e;
  margin: 0;
  font-family: 'Georgia', serif;
  line-height: 1.1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.highlight-text {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
}

.subtitle {
  font-size: 1.25rem;
  color: #78350f;
  line-height: 1.6;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-section {
  display: flex;
  gap: 16px;
  align-items: center;
}

.primary-cta {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.primary-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.secondary-cta {
  background: transparent;
  color: #92400e;
  border: 2px solid #d97706;
  padding: 14px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-cta:hover {
  background: #d97706;
  color: white;
  transform: translateY(-2px);
}

.trust-indicators {
  display: flex;
  gap: 40px;
  align-items: center;
}

.trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.trust-number {
  font-size: 2rem;
  font-weight: 700;
  color: #92400e;
  line-height: 1;
  margin-bottom: 4px;
}

.trust-label {
  font-size: 0.875rem;
  color: #78350f;
  font-weight: 500;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.demo-window {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  width: 100%;
  max-width: 500px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.window-header {
  background: #f3f4f6;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.window-controls {
  display: flex;
  gap: 8px;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.window-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.window-content {
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
}

.voice-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 3px solid #f59e0b;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.microphone-icon {
  font-size: 2rem;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.typing-demo {
  background: white;
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
}

.demo-text {
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  font-style: italic;
}

.cursor-blink {
  display: inline-block;
  width: 2px;
  height: 20px;
  background: #f59e0b;
  animation: blink 1s infinite;
  margin-left: 4px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-wrapper {
    gap: 60px;
  }

  .main-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .start-flowing-section {
    min-height: auto;
    padding: 80px 0;
  }

  .start-flowing-container {
    padding: 0 20px;
  }

  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.125rem;
  }

  .cta-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .trust-indicators {
    justify-content: center;
    gap: 24px;
  }

  .demo-window {
    max-width: 400px;
  }

  .window-content {
    padding: 32px 24px;
  }
}

@media (max-width: 480px) {
  .start-flowing-container {
    padding: 0 15px;
  }

  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .cta-section {
    flex-direction: column;
    gap: 12px;
  }

  .primary-cta,
  .secondary-cta {
    width: 100%;
    max-width: 280px;
  }

  .trust-indicators {
    gap: 16px;
  }

  .trust-number {
    font-size: 1.5rem;
  }

  .demo-window {
    max-width: 320px;
  }

  .window-content {
    padding: 24px 16px;
    gap: 24px;
  }
}
