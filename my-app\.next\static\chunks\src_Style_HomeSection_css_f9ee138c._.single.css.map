{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/HomeSection.css"], "sourcesContent": ["/* Home Section */\n.home-section {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  position: relative;\n}\n\n.home-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  padding: 0 40px;\n}\n\n.content-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.text-content {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.section-badge {\n  display: inline-flex;\n  align-items: center;\n  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);\n  color: #92400e;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  width: fit-content;\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n.section-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  color: black;\n  line-height: 1.1;\n  margin: 0;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.gradient-text {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 500;\n}\n\n.section-description {\n  font-size: 1.125rem;\n  color: #4b5563;\n  line-height: 1.6;\n  margin: 0;\n  max-width: 500px;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.feature-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 1rem;\n  color: #374151;\n}\n\n.feature-icon {\n  font-size: 1.25rem;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.visual-content {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n}\n\n.stat-card {\n  background: white;\n  padding: 24px;\n  border-radius: 16px;\n  text-align: center;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  border: 1px solid rgba(245, 158, 11, 0.1);\n}\n\n.stat-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n\n.stat-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 8px;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.stat-label {\n  font-size: 0.875rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.demo-card {\n  background: white;\n  border-radius: 20px;\n  padding: 24px;\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(245, 158, 11, 0.1);\n}\n\n.demo-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.demo-title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.demo-status {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.875rem;\n  color: #10b981;\n  font-weight: 500;\n}\n\n.status-dot {\n  width: 8px;\n  height: 8px;\n  background: #10b981;\n  border-radius: 50%;\n  animation: pulse 2s ease-in-out infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { \n    opacity: 1;\n    transform: scale(1);\n  }\n  50% { \n    opacity: 0.7;\n    transform: scale(1.1);\n  }\n}\n\n.demo-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.waveform {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 4px;\n  height: 60px;\n}\n\n.wave-bar {\n  width: 4px;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  border-radius: 2px;\n  animation: wave 1.5s ease-in-out infinite;\n}\n\n.wave-bar:nth-child(1) { height: 20px; animation-delay: 0s; }\n.wave-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }\n.wave-bar:nth-child(3) { height: 50px; animation-delay: 0.2s; }\n.wave-bar:nth-child(4) { height: 30px; animation-delay: 0.3s; }\n.wave-bar:nth-child(5) { height: 45px; animation-delay: 0.4s; }\n.wave-bar:nth-child(6) { height: 25px; animation-delay: 0.5s; }\n.wave-bar:nth-child(7) { height: 40px; animation-delay: 0.6s; }\n.wave-bar:nth-child(8) { height: 20px; animation-delay: 0.7s; }\n\n@keyframes wave {\n  0%, 100% { \n    transform: scaleY(0.3);\n    opacity: 0.7;\n  }\n  50% { \n    transform: scaleY(1);\n    opacity: 1;\n  }\n}\n\n.demo-text {\n  font-size: 0.95rem;\n  color: #6b7280;\n  font-style: italic;\n  text-align: center;\n  padding: 16px;\n  background: #f9fafb;\n  border-radius: 12px;\n  border-left: 4px solid #f59e0b;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .home-section {\n    padding: 100px 20px;\n  }\n  \n  .content-grid {\n    gap: 60px;\n  }\n  \n  .section-title {\n    font-size: 3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .home-section {\n    padding: 80px 20px;\n    min-height: auto;\n  }\n  \n  .content-grid {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n  \n  .section-title {\n    font-size: 2.5rem;\n  }\n  \n  .section-description {\n    font-size: 1rem;\n    max-width: none;\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n  \n  .feature-list {\n    align-items: center;\n  }\n  \n  .feature-item {\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .home-section {\n    padding: 60px 15px;\n  }\n  \n  .section-title {\n    font-size: 2rem;\n  }\n  \n  .section-description {\n    font-size: 0.95rem;\n  }\n  \n  .demo-card {\n    padding: 20px;\n  }\n  \n  .stat-card {\n    padding: 20px;\n  }\n  \n  .stat-number {\n    font-size: 1.75rem;\n  }\n  \n  .waveform {\n    height: 50px;\n  }\n  \n  .wave-bar {\n    width: 3px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;;;AAWA;;;;;;;;;;;AAYA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAQA;;;;EAIA;;;;EAIA"}}]}