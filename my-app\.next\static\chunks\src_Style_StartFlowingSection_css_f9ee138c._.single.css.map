{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/StartFlowingSection.css"], "sourcesContent": ["/* Start Flowing Section */\n.start-flowing-section {\n  position: relative;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 25%, #7c3aed 50%, #db2777 75%, #dc2626 100%);\n}\n\n.background-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 1;\n}\n\n.background-blur {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  backdrop-filter: blur(1px);\n  z-index: 2;\n}\n\n.dotted-line-svg {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 60%;\n  height: 100%;\n  z-index: 3;\n  pointer-events: none;\n}\n\n.dotted-line {\n  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));\n}\n\n.start-flowing-container {\n  position: relative;\n  z-index: 4;\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 20px;\n  width: 100%;\n}\n\n.content-wrapper {\n  max-width: 600px;\n  text-align: center;\n  margin: 0 auto;\n}\n\n.main-title {\n  font-size: 4.5rem;\n  font-weight: 400;\n  color: white;\n  margin: 0 0 24px 0;\n  font-family: 'Georgia', serif;\n  line-height: 1.1;\n  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n  position: relative;\n}\n\n.title-dots {\n  color: rgba(255, 255, 255, 0.7);\n  animation: dots-fade 2s ease-in-out infinite;\n}\n\n@keyframes dots-fade {\n  0%, 100% { opacity: 0.7; }\n  50% { opacity: 0.3; }\n}\n\n.subtitle {\n  font-size: 1.25rem;\n  color: rgba(255, 255, 255, 0.9);\n  line-height: 1.6;\n  margin: 0 0 40px 0;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n  font-weight: 400;\n}\n\n.cta-buttons {\n  display: flex;\n  gap: 16px;\n  justify-content: center;\n  margin-bottom: 24px;\n  flex-wrap: wrap;\n}\n\n.primary-cta {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);\n  position: relative;\n  overflow: hidden;\n}\n\n.primary-cta::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.primary-cta:hover::before {\n  left: 100%;\n}\n\n.primary-cta:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);\n}\n\n.secondary-cta {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  padding: 14px 32px;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n}\n\n.secondary-cta:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);\n}\n\n.availability-text {\n  font-size: 0.95rem;\n  color: rgba(255, 255, 255, 0.7);\n  margin: 0;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .main-title {\n    font-size: 3.5rem;\n  }\n  \n  .subtitle {\n    font-size: 1.125rem;\n  }\n  \n  .dotted-line-svg {\n    width: 70%;\n  }\n}\n\n@media (max-width: 768px) {\n  .start-flowing-section {\n    min-height: 90vh;\n    padding: 60px 0;\n  }\n  \n  .main-title {\n    font-size: 2.5rem;\n  }\n  \n  .subtitle {\n    font-size: 1rem;\n    margin-bottom: 32px;\n  }\n  \n  .cta-buttons {\n    flex-direction: column;\n    align-items: center;\n    gap: 12px;\n  }\n  \n  .primary-cta,\n  .secondary-cta {\n    width: 100%;\n    max-width: 280px;\n    padding: 16px 24px;\n    font-size: 1rem;\n  }\n  \n  .dotted-line-svg {\n    width: 80%;\n  }\n  \n  .content-wrapper {\n    max-width: 500px;\n  }\n}\n\n@media (max-width: 480px) {\n  .start-flowing-container {\n    padding: 0 15px;\n  }\n  \n  .main-title {\n    font-size: 2rem;\n  }\n  \n  .subtitle {\n    font-size: 0.95rem;\n  }\n  \n  .primary-cta,\n  .secondary-cta {\n    font-size: 0.95rem;\n    padding: 14px 20px;\n  }\n  \n  .availability-text {\n    font-size: 0.875rem;\n  }\n  \n  .dotted-line-svg {\n    display: none;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;AAUA;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;;EAQA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAMA;;;;EAIA"}}]}