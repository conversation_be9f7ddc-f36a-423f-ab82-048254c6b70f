{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/StartFlowingSection.css"], "sourcesContent": ["/* Start Flowing Section */\n.start-flowing-section {\n  position: relative;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 50%, #fdba74 100%);\n  overflow: hidden;\n}\n\n.start-flowing-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 40px;\n  width: 100%;\n}\n\n.content-wrapper {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n  min-height: 80vh;\n}\n\n.hero-content {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.main-title {\n  font-size: 3.5rem;\n  font-weight: 400;\n  color: #92400e;\n  margin: 0;\n  font-family: 'Georgia', serif;\n  line-height: 1.1;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.highlight-text {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 500;\n}\n\n.subtitle {\n  font-size: 1.25rem;\n  color: #78350f;\n  line-height: 1.6;\n  margin: 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.cta-section {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n}\n\n.primary-cta {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n}\n\n.primary-cta:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);\n}\n\n.secondary-cta {\n  background: transparent;\n  color: #92400e;\n  border: 2px solid #d97706;\n  padding: 14px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.secondary-cta:hover {\n  background: #d97706;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.trust-indicators {\n  display: flex;\n  gap: 40px;\n  align-items: center;\n}\n\n.trust-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.trust-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #92400e;\n  line-height: 1;\n  margin-bottom: 4px;\n}\n\n.trust-label {\n  font-size: 0.875rem;\n  color: #78350f;\n  font-weight: 500;\n}\n\n.hero-visual {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.demo-window {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  width: 100%;\n  max-width: 500px;\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n.window-header {\n  background: #f3f4f6;\n  padding: 16px 20px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.window-controls {\n  display: flex;\n  gap: 8px;\n}\n\n.control {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.control.red { background: #ef4444; }\n.control.yellow { background: #f59e0b; }\n.control.green { background: #10b981; }\n\n.window-title {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.window-content {\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 32px;\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);\n}\n\n.voice-indicator {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.pulse-ring {\n  position: absolute;\n  width: 80px;\n  height: 80px;\n  border: 3px solid #f59e0b;\n  border-radius: 50%;\n  animation: pulse 2s ease-in-out infinite;\n}\n\n.microphone-icon {\n  font-size: 2rem;\n  z-index: 1;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1.5);\n    opacity: 0;\n  }\n}\n\n.typing-demo {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  width: 100%;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.demo-text {\n  font-size: 1rem;\n  color: #374151;\n  line-height: 1.6;\n  margin: 0;\n  font-style: italic;\n}\n\n.cursor-blink {\n  display: inline-block;\n  width: 2px;\n  height: 20px;\n  background: #f59e0b;\n  animation: blink 1s infinite;\n  margin-left: 4px;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0; }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .content-wrapper {\n    gap: 60px;\n  }\n\n  .main-title {\n    font-size: 3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .start-flowing-section {\n    min-height: auto;\n    padding: 80px 0;\n  }\n\n  .start-flowing-container {\n    padding: 0 20px;\n  }\n\n  .content-wrapper {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .main-title {\n    font-size: 2.5rem;\n  }\n\n  .subtitle {\n    font-size: 1.125rem;\n  }\n\n  .cta-section {\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n\n  .trust-indicators {\n    justify-content: center;\n    gap: 24px;\n  }\n\n  .demo-window {\n    max-width: 400px;\n  }\n\n  .window-content {\n    padding: 32px 24px;\n  }\n}\n\n@media (max-width: 480px) {\n  .start-flowing-container {\n    padding: 0 15px;\n  }\n\n  .main-title {\n    font-size: 2rem;\n  }\n\n  .subtitle {\n    font-size: 1rem;\n  }\n\n  .cta-section {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .primary-cta,\n  .secondary-cta {\n    width: 100%;\n    max-width: 280px;\n  }\n\n  .trust-indicators {\n    gap: 16px;\n  }\n\n  .trust-number {\n    font-size: 1.5rem;\n  }\n\n  .demo-window {\n    max-width: 320px;\n  }\n\n  .window-content {\n    padding: 24px 16px;\n    gap: 24px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;;AAMA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}