/* Savings Calculator Section */
.savings-calculator-section {
  background: linear-gradient(135deg, #064e3b 0%, #047857 100%);
  padding: 120px 20px;
  min-height: 100vh;
  color: white;
}

.savings-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 60px;
}

.content-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.1;
  font-family: 'Georgia', serif;
}

.scenario-text {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.scenario-main {
  font-size: 1.5rem;
  color: #d1fae5;
  margin: 0;
  line-height: 1.4;
}

.scenario-main strong {
  color: #6ee7b7;
  font-weight: 700;
}

.scenario-detail {
  font-size: 1rem;
  color: #a7f3d0;
  margin: 0;
  line-height: 1.5;
}

.value-statement {
  font-size: 1.25rem;
  color: #d1fae5;
  margin: 0;
  line-height: 1.4;
}

.value-statement strong {
  color: #6ee7b7;
  font-weight: 700;
}

.input-controls {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-group label {
  font-size: 1rem;
  font-weight: 600;
  color: #a7f3d0;
}

.slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #065f46;
  outline: none;
  cursor: pointer;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.slider-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #6ee7b7;
  text-align: center;
  background: rgba(16, 185, 129, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  width: fit-content;
  align-self: center;
}

.illustration-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.character-illustration {
  position: relative;
  width: 300px;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.character {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  z-index: 2;
}

.character-head {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.face {
  font-size: 4rem;
  animation: bounce 2s ease-in-out infinite;
}

.hat {
  font-size: 2rem;
  position: absolute;
  top: -20px;
  animation: tilt 3s ease-in-out infinite;
}

.character-body {
  display: flex;
  justify-content: center;
}

.arms {
  display: flex;
  gap: 40px;
}

.arm {
  font-size: 2rem;
  animation: juggle 1.5s ease-in-out infinite;
}

.arm.left {
  animation-delay: 0s;
}

.arm.right {
  animation-delay: 0.75s;
}

.money-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.money-icon {
  position: absolute;
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes tilt {
  0%, 100% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
}

@keyframes juggle {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(10deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.8; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

.calculator-section {
  display: flex;
  justify-content: center;
}

.calculator-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.calculator-header {
  text-align: center;
  margin-bottom: 32px;
}

.calculator-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #a7f3d0;
  margin: 0 0 16px 0;
}

.savings-amount {
  font-size: 3rem;
  font-weight: 700;
  color: #6ee7b7;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8px;
}

.period {
  font-size: 1.5rem;
  color: #a7f3d0;
  font-weight: 500;
}

.breakdown {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.breakdown-item .label {
  font-size: 1rem;
  color: #d1fae5;
}

.breakdown-item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #6ee7b7;
}

.breakdown-item .value.negative {
  color: #fca5a5;
}

.calculator-footer {
  text-align: center;
}

.get-started-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 auto;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.get-started-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.arrow {
  transition: transform 0.3s ease;
}

.get-started-btn:hover .arrow {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-section {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .character-illustration {
    width: 250px;
    height: 250px;
  }

  .face {
    font-size: 3rem;
  }

  .calculator-card {
    padding: 32px;
  }
}

@media (max-width: 768px) {
  .savings-calculator-section {
    padding: 80px 20px;
  }

  .content-section {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .scenario-main {
    font-size: 1.25rem;
  }

  .value-statement {
    font-size: 1.125rem;
  }

  .character-illustration {
    width: 200px;
    height: 200px;
  }

  .face {
    font-size: 2.5rem;
  }

  .hat {
    font-size: 1.5rem;
  }

  .arm {
    font-size: 1.5rem;
  }

  .money-icon {
    font-size: 1.5rem;
  }

  .calculator-card {
    padding: 24px;
    margin: 0 20px;
  }

  .savings-amount {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .savings-calculator-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .scenario-main {
    font-size: 1.125rem;
  }

  .scenario-detail {
    font-size: 0.875rem;
  }

  .value-statement {
    font-size: 1rem;
  }

  .character-illustration {
    width: 150px;
    height: 150px;
  }

  .face {
    font-size: 2rem;
  }

  .calculator-card {
    padding: 20px;
    margin: 0 10px;
  }

  .savings-amount {
    font-size: 2rem;
  }

  .calculator-header h3 {
    font-size: 1.25rem;
  }

  .breakdown-item .label,
  .breakdown-item .value {
    font-size: 0.875rem;
  }

  .get-started-btn {
    padding: 14px 24px;
    font-size: 1rem;
  }
}
