{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/Marquee.css"], "sourcesContent": ["/* Marquee Container */\r\n.marquee-container {\r\n  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  width: 100%;\r\n  min-height: 100px;\r\n  padding: 20px 0;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.marquee-container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(90deg,\r\n    rgba(0,0,0,0.15) 0%,\r\n    transparent 15%,\r\n    transparent 85%,\r\n    rgba(0,0,0,0.15) 100%);\r\n  pointer-events: none;\r\n  z-index: 2;\r\n}\r\n\r\n.marquee-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: nowrap;\r\n  will-change: transform;\r\n}\r\n\r\n/* Marquee Items */\r\n.marquee-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  flex-shrink: 0;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  min-width: fit-content;\r\n}\r\n\r\n.marquee-content {\r\n  padding: 12px 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: white;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  white-space: nowrap;\r\n  letter-spacing: 0.5px;\r\n  user-select: none;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .marquee-content {\r\n    font-size: 15px;\r\n    padding: 11px 18px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .marquee-container {\r\n    min-height: 80px;\r\n    padding: 15px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 14px;\r\n    padding: 10px 16px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 15px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .marquee-container {\r\n    min-height: 70px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 13px;\r\n    padding: 8px 14px;\r\n    letter-spacing: 0.3px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 320px) {\r\n  .marquee-container {\r\n    min-height: 60px;\r\n    padding: 10px 0;\r\n  }\r\n\r\n  .marquee-content {\r\n    font-size: 12px;\r\n    padding: 6px 12px;\r\n  }\r\n\r\n  .marquee-item {\r\n    margin-right: 10px;\r\n  }\r\n}\r\n\r\n/* Legacy styles for backward compatibility */\r\n.MarqueeBox {\r\n  font-family: system-ui;\r\n  background: #111;\r\n  color: white;\r\n  text-align: center;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 20vh;\r\n}\r\n\r\n.carousel {\r\n  background: blue;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: green;\r\n  margin: 0;\r\n  padding: 0;\r\n  position: relative;\r\n  color: black;\r\n  font-size: 121px;\r\n  cursor: pointer;\r\n}\r\n\r\n.test {\r\n  padding: 20px;\r\n}\r\n\r\n.test-2 {\r\n  padding: 20px 10px;\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;AAaA;;;;;;;;;AAgBA;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;;;;;;;AAYA;EACE;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;;EAKA;;;;;;EAMA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA;;;;;AAMF;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;AAIA", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/FeaturesShowcase.css"], "sourcesContent": ["/* Features Showcase Styles */\n.features-showcase {\n  padding: 80px 20px;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  min-height: 100vh;\n}\n\n.features-container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.features-header {\n  text-align: center;\n  margin-bottom: 60px;\n}\n\n.features-title {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 16px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.features-subtitle {\n  font-size: 1.2rem;\n  color: #718096;\n  max-width: 600px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 30px;\n  align-items: start;\n}\n\n.feature-card {\n  border-radius: 20px;\n  padding: 30px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.feature-large {\n  grid-column: span 2;\n  min-height: 400px;\n}\n\n.feature-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 20px;\n}\n\n.feature-image {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n}\n\n.phone-mockup {\n  width: 200px;\n  height: 300px;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 25px;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n}\n\n/* AI Interface */\n.ai-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.ai-suggestions {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.suggestion-item {\n  background: rgba(255, 255, 255, 0.1);\n  padding: 8px 12px;\n  border-radius: 8px;\n  font-size: 12px;\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.ai-controls {\n  display: flex;\n  justify-content: center;\n}\n\n.ai-btn {\n  background: #4facfe;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 20px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.ai-btn:hover {\n  background: #3d8bfe;\n  transform: scale(1.05);\n}\n\n/* Dictionary Interface */\n.dictionary-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.dict-header {\n  color: #4facfe;\n  font-weight: 600;\n  text-align: center;\n  font-size: 14px;\n}\n\n.dict-items {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.dict-item {\n  background: rgba(79, 172, 254, 0.2);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 15px;\n  font-size: 11px;\n  text-align: center;\n  border: 1px solid rgba(79, 172, 254, 0.3);\n}\n\n/* Tones Interface */\n.tones-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  justify-content: center;\n}\n\n.app-selector {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n}\n\n.app-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 10px;\n  background: rgba(255, 255, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.app-icon.active {\n  background: rgba(79, 172, 254, 0.3);\n  border: 2px solid #4facfe;\n}\n\n.tone-options {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.tone-btn {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 12px;\n  font-size: 11px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tone-btn.active {\n  background: rgba(79, 172, 254, 0.3);\n  border: 1px solid #4facfe;\n}\n\n/* Languages Interface */\n.languages-interface {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.lang-wheel {\n  position: relative;\n  width: 120px;\n  height: 120px;\n  border-radius: 50%;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n}\n\n.lang-item {\n  position: absolute;\n  width: 30px;\n  height: 30px;\n  background: rgba(67, 233, 123, 0.3);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: 600;\n  color: white;\n  border: 1px solid rgba(67, 233, 123, 0.5);\n}\n\n.lang-item:nth-child(1) { top: -15px; left: 50%; transform: translateX(-50%); }\n.lang-item:nth-child(2) { top: 15px; right: -15px; }\n.lang-item:nth-child(3) { bottom: 15px; right: -15px; }\n.lang-item:nth-child(4) { bottom: -15px; left: 50%; transform: translateX(-50%); }\n.lang-item:nth-child(5) { bottom: 15px; left: -15px; }\n.lang-item:nth-child(6) { top: 15px; left: -15px; }\n\n.lang-center {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(67, 233, 123, 0.5);\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  color: white;\n  font-size: 12px;\n  border: 2px solid rgba(67, 233, 123, 0.7);\n}\n\n.feature-text {\n  text-align: center;\n}\n\n.feature-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 12px;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.feature-description {\n  font-size: 1rem;\n  line-height: 1.6;\n  opacity: 0.9;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .features-showcase {\n    padding: 60px 15px;\n  }\n  \n  .features-title {\n    font-size: 2.5rem;\n  }\n  \n  .features-grid {\n    grid-template-columns: 1fr;\n    gap: 20px;\n  }\n  \n  .feature-large {\n    grid-column: span 1;\n  }\n  \n  .feature-card {\n    padding: 20px;\n  }\n  \n  .phone-mockup {\n    width: 150px;\n    height: 220px;\n    padding: 15px;\n  }\n}\n\n@media (max-width: 480px) {\n  .features-title {\n    font-size: 2rem;\n  }\n  \n  .features-subtitle {\n    font-size: 1rem;\n  }\n  \n  .phone-mockup {\n    width: 120px;\n    height: 180px;\n    padding: 10px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;;;AAeA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;;AACA;;;;;AACA;;;;;AACA;;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;;;;AAOA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/FlowSection.css"], "sourcesContent": ["/* Flow Section Styles */\n.flow-section {\n  background: #2a2a2a;\n  color: white;\n  padding: 100px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n}\n\n.flow-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.flow-content {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.flow-left {\n  display: flex;\n  flex-direction: column;\n  gap: 40px;\n}\n\n.flow-title {\n  font-size: 4.5rem;\n  font-weight: 400;\n  line-height: 1.1;\n  margin: 0;\n  font-family: 'Georgia', serif;\n}\n\n.profession-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n  max-width: 600px;\n}\n\n.profession-tag {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 25px;\n  padding: 8px 16px;\n  font-size: 14px;\n  font-weight: 500;\n  color: white;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  backdrop-filter: blur(10px);\n}\n\n.profession-tag:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.4);\n  transform: translateY(-2px);\n}\n\n.flow-right {\n  display: flex;\n  flex-direction: column;\n  gap: 40px;\n  align-items: flex-end;\n}\n\n.flow-accessibility {\n  max-width: 400px;\n  text-align: left;\n}\n\n.accessibility-title {\n  font-size: 2rem;\n  font-weight: 600;\n  margin-bottom: 16px;\n  color: white;\n}\n\n.accessibility-description {\n  font-size: 1.1rem;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 24px;\n}\n\n.get-started-btn {\n  background: #6366f1;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.get-started-btn:hover {\n  background: #5855eb;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);\n}\n\n/* Character Illustration */\n.flow-illustration {\n  position: relative;\n  width: 300px;\n  height: 300px;\n}\n\n.character-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.character {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.character-head {\n  width: 80px;\n  height: 80px;\n  background: #ff9ff3;\n  border-radius: 50%;\n  position: relative;\n  margin: 0 auto 10px;\n}\n\n.character-face {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.eye {\n  width: 8px;\n  height: 8px;\n  background: #2a2a2a;\n  border-radius: 50%;\n  position: absolute;\n}\n\n.left-eye {\n  left: -12px;\n  top: -2px;\n}\n\n.right-eye {\n  right: -12px;\n  top: -2px;\n}\n\n.mouth {\n  width: 12px;\n  height: 6px;\n  border: 2px solid #2a2a2a;\n  border-top: none;\n  border-radius: 0 0 12px 12px;\n  position: absolute;\n  left: -6px;\n  top: 8px;\n}\n\n.character-ears {\n  position: absolute;\n  top: 10px;\n  width: 100%;\n}\n\n.ear {\n  width: 20px;\n  height: 25px;\n  background: #ff9ff3;\n  border-radius: 50%;\n  position: absolute;\n}\n\n.left-ear {\n  left: -10px;\n  transform: rotate(-20deg);\n}\n\n.right-ear {\n  right: -10px;\n  transform: rotate(20deg);\n}\n\n.character-body {\n  width: 60px;\n  height: 80px;\n  background: #ff9ff3;\n  border-radius: 30px;\n  margin: 0 auto;\n  position: relative;\n}\n\n.character-arms {\n  position: absolute;\n  top: 20px;\n  width: 100%;\n}\n\n.arm {\n  width: 30px;\n  height: 8px;\n  background: #ff9ff3;\n  border-radius: 4px;\n  position: absolute;\n}\n\n.left-arm {\n  left: -25px;\n  transform: rotate(-30deg);\n}\n\n.right-arm {\n  right: -25px;\n  transform: rotate(30deg);\n}\n\n.speech-bubble {\n  position: absolute;\n  top: -20px;\n  right: -80px;\n  width: 120px;\n  height: 80px;\n  background: white;\n  border-radius: 15px;\n  padding: 15px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.speech-bubble::before {\n  content: '';\n  position: absolute;\n  left: -10px;\n  top: 30px;\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 10px 10px 10px 0;\n  border-color: transparent white transparent transparent;\n}\n\n.bubble-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.text-lines {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.text-line {\n  height: 3px;\n  background: #ff9500;\n  border-radius: 2px;\n}\n\n.text-line.short {\n  width: 60%;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .flow-content {\n    gap: 60px;\n  }\n  \n  .flow-title {\n    font-size: 3.5rem;\n  }\n  \n  .flow-illustration {\n    width: 250px;\n    height: 250px;\n  }\n}\n\n@media (max-width: 768px) {\n  .flow-section {\n    padding: 80px 20px;\n  }\n  \n  .flow-content {\n    grid-template-columns: 1fr;\n    gap: 60px;\n    text-align: center;\n  }\n  \n  .flow-title {\n    font-size: 3rem;\n  }\n  \n  .flow-right {\n    align-items: center;\n  }\n  \n  .flow-accessibility {\n    text-align: center;\n  }\n  \n  .profession-tags {\n    justify-content: center;\n  }\n  \n  .flow-illustration {\n    width: 200px;\n    height: 200px;\n  }\n  \n  .speech-bubble {\n    right: -60px;\n    width: 100px;\n    height: 60px;\n    padding: 10px;\n  }\n}\n\n@media (max-width: 480px) {\n  .flow-section {\n    padding: 60px 15px;\n  }\n  \n  .flow-title {\n    font-size: 2.5rem;\n  }\n  \n  .accessibility-title {\n    font-size: 1.5rem;\n  }\n  \n  .accessibility-description {\n    font-size: 1rem;\n  }\n  \n  .profession-tag {\n    font-size: 12px;\n    padding: 6px 12px;\n  }\n  \n  .flow-illustration {\n    width: 180px;\n    height: 180px;\n  }\n  \n  .character-head {\n    width: 60px;\n    height: 60px;\n  }\n  \n  .character-body {\n    width: 45px;\n    height: 60px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;;;AAQF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/Style/ContentImageSection.css"], "sourcesContent": ["/* Content Image Section Styles */\n.content-image-section {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n}\n\n.content-image-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.content-side {\n  display: flex;\n  flex-direction: column;\n  gap: 40px;\n}\n\n.content-header {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.content-badge {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 14px;\n  font-weight: 600;\n  width: fit-content;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.content-title {\n  font-size: 3.5rem;\n  font-weight: 700;\n  line-height: 1.1;\n  color: #1a202c;\n  margin: 0;\n}\n\n.highlight {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.content-description {\n  font-size: 1.2rem;\n  line-height: 1.6;\n  color: #4a5568;\n  margin: 0;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 24px;\n}\n\n.feature-item {\n  display: flex;\n  gap: 16px;\n  align-items: flex-start;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n}\n\n.feature-item:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  font-size: 2rem;\n  width: 50px;\n  height: 50px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  flex-shrink: 0;\n}\n\n.feature-content {\n  flex: 1;\n}\n\n.feature-title {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0 0 8px 0;\n}\n\n.feature-description {\n  font-size: 1rem;\n  line-height: 1.5;\n  color: #4a5568;\n  margin: 0;\n}\n\n.content-actions {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.primary-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 14px 28px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n}\n\n.primary-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n}\n\n.secondary-btn {\n  background: transparent;\n  color: #667eea;\n  border: 2px solid #667eea;\n  padding: 12px 26px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.secondary-btn:hover {\n  background: #667eea;\n  color: white;\n  transform: translateY(-2px);\n}\n\n/* Image Side Styles */\n.image-side {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.image-container {\n  position: relative;\n  width: 100%;\n  max-width: 500px;\n}\n\n.main-device {\n  background: #1a202c;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);\n  position: relative;\n  z-index: 2;\n}\n\n.device-screen {\n  background: #2d3748;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.screen-header {\n  background: #4a5568;\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.screen-dots {\n  display: flex;\n  gap: 6px;\n}\n\n.dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.dot.red { background: #f56565; }\n.dot.yellow { background: #ed8936; }\n.dot.green { background: #48bb78; }\n\n.screen-title {\n  color: #e2e8f0;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.screen-content {\n  padding: 30px 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n  align-items: center;\n}\n\n.voice-wave {\n  display: flex;\n  gap: 4px;\n  align-items: center;\n  justify-content: center;\n}\n\n.wave-bar {\n  width: 4px;\n  background: #667eea;\n  border-radius: 2px;\n  animation: wave 1.5s ease-in-out infinite;\n}\n\n.wave-bar:nth-child(1) { height: 20px; animation-delay: 0s; }\n.wave-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }\n.wave-bar:nth-child(3) { height: 25px; animation-delay: 0.2s; }\n.wave-bar:nth-child(4) { height: 40px; animation-delay: 0.3s; }\n.wave-bar:nth-child(5) { height: 30px; animation-delay: 0.4s; }\n.wave-bar:nth-child(6) { height: 35px; animation-delay: 0.5s; }\n.wave-bar:nth-child(7) { height: 20px; animation-delay: 0.6s; }\n\n@keyframes wave {\n  0%, 100% { transform: scaleY(1); }\n  50% { transform: scaleY(0.3); }\n}\n\n.text-output {\n  background: #4a5568;\n  border-radius: 8px;\n  padding: 16px;\n  width: 100%;\n  min-height: 60px;\n  display: flex;\n  align-items: center;\n}\n\n.typing-text {\n  color: #e2e8f0;\n  font-size: 14px;\n  font-family: 'Courier New', monospace;\n}\n\n.cursor {\n  animation: blink 1s infinite;\n  color: #667eea;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0; }\n}\n\n.floating-elements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.floating-card {\n  position: absolute;\n  background: white;\n  border-radius: 12px;\n  padding: 12px 16px;\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #1a202c;\n  animation: float 3s ease-in-out infinite;\n}\n\n.card-1 {\n  top: 20%;\n  left: -10%;\n  animation-delay: 0s;\n}\n\n.card-2 {\n  top: 60%;\n  right: -15%;\n  animation-delay: 1s;\n}\n\n.card-3 {\n  bottom: 20%;\n  left: -5%;\n  animation-delay: 2s;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.card-icon {\n  font-size: 18px;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .content-image-container {\n    gap: 60px;\n  }\n  \n  .content-title {\n    font-size: 3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .content-image-section {\n    padding: 80px 20px;\n  }\n  \n  .content-image-container {\n    grid-template-columns: 1fr;\n    gap: 60px;\n  }\n  \n  .content-title {\n    font-size: 2.5rem;\n    text-align: center;\n  }\n  \n  .content-header {\n    text-align: center;\n  }\n  \n  .content-badge {\n    align-self: center;\n  }\n  \n  .content-actions {\n    justify-content: center;\n  }\n  \n  .floating-card {\n    display: none;\n  }\n}\n\n@media (max-width: 480px) {\n  .content-image-section {\n    padding: 60px 15px;\n  }\n  \n  .content-title {\n    font-size: 2rem;\n  }\n  \n  .content-description {\n    font-size: 1rem;\n  }\n  \n  .feature-item {\n    padding: 16px;\n  }\n  \n  .feature-icon {\n    width: 40px;\n    height: 40px;\n    font-size: 1.5rem;\n  }\n  \n  .main-device {\n    padding: 15px;\n  }\n  \n  .screen-content {\n    padding: 20px 15px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;AAKA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAKA;;;;AAKA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA", "debugId": null}}]}