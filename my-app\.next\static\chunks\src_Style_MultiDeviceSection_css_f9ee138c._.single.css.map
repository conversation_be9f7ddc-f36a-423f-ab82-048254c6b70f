{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/MultiDeviceSection.css"], "sourcesContent": ["/* Multi Device Section */\n.multi-device-section {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n  padding: 120px 20px;\n  min-height: 200vh;\n}\n\n.multi-device-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 120px;\n}\n\n.device-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.device-row.reverse {\n  grid-template-columns: 1fr 1fr;\n}\n\n.device-row.reverse .device-mockup {\n  order: 2;\n}\n\n.device-row.reverse .device-content {\n  order: 1;\n}\n\n.device-mockup {\n  background: #1f2937;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n.device-screen {\n  background: #111827;\n  border-radius: 12px;\n  overflow: hidden;\n  min-height: 300px;\n}\n\n.screen-header {\n  background: #374151;\n  padding: 16px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: white;\n}\n\n.screen-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.close-btn {\n  font-size: 20px;\n  cursor: pointer;\n  opacity: 0.7;\n}\n\n.device-content {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  max-width: 500px;\n}\n\n.device-content h3 {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #92400e;\n  margin: 0;\n  line-height: 1.2;\n}\n\n.device-content p {\n  font-size: 1.1rem;\n  line-height: 1.6;\n  color: #78350f;\n  margin: 0;\n}\n\n/* Dictionary Device */\n.dictionary-content {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.dict-item {\n  background: #374151;\n  color: #60a5fa;\n  padding: 12px 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  border-left: 3px solid #3b82f6;\n  transition: all 0.3s ease;\n}\n\n.dict-item:hover {\n  background: #4b5563;\n  transform: translateX(4px);\n}\n\n/* Tones Device */\n.app-selector {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  padding: 20px;\n  background: #374151;\n}\n\n.app-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 12px;\n  background: #4b5563;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.app-icon.active {\n  background: #3b82f6;\n  transform: scale(1.1);\n}\n\n.tones-content {\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.tone-option {\n  background: #374151;\n  color: #d1d5db;\n  padding: 12px 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tone-option.active {\n  background: #10b981;\n  color: white;\n  transform: scale(1.02);\n}\n\n/* Languages Device */\n.language-wheel {\n  position: relative;\n  width: 200px;\n  height: 200px;\n  margin: 40px auto;\n}\n\n.language-item {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 40px;\n  height: 40px;\n  background: #3b82f6;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: 600;\n  transform-origin: center;\n  animation: rotate 20s linear infinite;\n}\n\n@keyframes rotate {\n  from { transform: rotate(0deg) translateY(-80px) rotate(0deg); }\n  to { transform: rotate(360deg) translateY(-80px) rotate(-360deg); }\n}\n\n.wheel-center {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 80px;\n  height: 80px;\n  background: #1f2937;\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid #3b82f6;\n}\n\n.lang-count {\n  color: #60a5fa;\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.lang-text {\n  color: #9ca3af;\n  font-size: 10px;\n  font-weight: 500;\n}\n\n/* Desktop Device */\n.desktop-device {\n  max-width: 500px;\n}\n\n.desktop-screen {\n  background: #f3f4f6;\n  border-radius: 12px;\n  overflow: hidden;\n  min-height: 400px;\n  position: relative;\n}\n\n.desktop-header {\n  background: #e5e7eb;\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  border-bottom: 1px solid #d1d5db;\n}\n\n.window-controls {\n  display: flex;\n  gap: 6px;\n}\n\n.control {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.control.red { background: #ef4444; }\n.control.yellow { background: #f59e0b; }\n.control.green { background: #10b981; }\n\n.window-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n}\n\n.desktop-content {\n  display: flex;\n  height: 300px;\n}\n\n.sidebar {\n  width: 120px;\n  background: #f9fafb;\n  border-right: 1px solid #e5e7eb;\n  padding: 16px 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.sidebar-item {\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.sidebar-item.active {\n  background: #3b82f6;\n  color: white;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  background: white;\n}\n\n.document-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.document-header h4 {\n  margin: 0;\n  font-size: 16px;\n  color: #1f2937;\n}\n\n.doc-status {\n  font-size: 12px;\n  color: #10b981;\n  background: #dcfce7;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n.document-body {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.text-line {\n  height: 4px;\n  background: #d1d5db;\n  border-radius: 2px;\n}\n\n.text-line.short { width: 60%; }\n.text-line.medium { width: 80%; }\n\n.mobile-preview {\n  position: absolute;\n  bottom: 20px;\n  right: 20px;\n  width: 80px;\n  height: 120px;\n  background: #1f2937;\n  border-radius: 12px;\n  padding: 8px;\n}\n\n.mobile-screen {\n  background: #111827;\n  border-radius: 8px;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n}\n\n.mobile-header {\n  color: white;\n  font-size: 8px;\n  font-weight: 600;\n}\n\n.voice-indicator {\n  display: flex;\n  gap: 2px;\n  align-items: center;\n}\n\n.voice-wave {\n  width: 2px;\n  background: #3b82f6;\n  border-radius: 1px;\n  animation: wave 1s ease-in-out infinite;\n}\n\n.voice-wave:nth-child(1) { height: 8px; animation-delay: 0s; }\n.voice-wave:nth-child(2) { height: 12px; animation-delay: 0.1s; }\n.voice-wave:nth-child(3) { height: 6px; animation-delay: 0.2s; }\n\n@keyframes wave {\n  0%, 100% { transform: scaleY(1); }\n  50% { transform: scaleY(0.3); }\n}\n\n.cta-button {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: fit-content;\n  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n}\n\n.cta-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .multi-device-container {\n    gap: 100px;\n  }\n\n  .device-row {\n    gap: 60px;\n  }\n\n  .device-content h3 {\n    font-size: 2rem;\n  }\n\n  .device-mockup {\n    max-width: 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .multi-device-section {\n    padding: 80px 20px;\n  }\n\n  .multi-device-container {\n    gap: 80px;\n  }\n\n  .device-row {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .device-row.reverse .device-mockup,\n  .device-row.reverse .device-content {\n    order: unset;\n  }\n\n  .device-content {\n    max-width: none;\n    align-items: center;\n  }\n\n  .device-content h3 {\n    font-size: 1.8rem;\n  }\n\n  .device-mockup {\n    max-width: 320px;\n  }\n\n  .language-wheel {\n    width: 160px;\n    height: 160px;\n  }\n\n  .language-item {\n    width: 32px;\n    height: 32px;\n    font-size: 10px;\n  }\n\n  .wheel-center {\n    width: 60px;\n    height: 60px;\n  }\n\n  .lang-count {\n    font-size: 14px;\n  }\n\n  .lang-text {\n    font-size: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .multi-device-section {\n    padding: 60px 15px;\n  }\n\n  .multi-device-container {\n    gap: 60px;\n  }\n\n  .device-content h3 {\n    font-size: 1.5rem;\n  }\n\n  .device-content p {\n    font-size: 1rem;\n  }\n\n  .device-mockup {\n    max-width: 280px;\n    padding: 15px;\n  }\n\n  .device-screen {\n    min-height: 250px;\n  }\n\n  .desktop-content {\n    height: 250px;\n  }\n\n  .sidebar {\n    width: 100px;\n    padding: 12px 6px;\n  }\n\n  .sidebar-item {\n    font-size: 10px;\n    padding: 6px 8px;\n  }\n\n  .main-content {\n    padding: 15px;\n  }\n\n  .mobile-preview {\n    width: 60px;\n    height: 90px;\n    bottom: 15px;\n    right: 15px;\n  }\n\n  .language-wheel {\n    width: 140px;\n    height: 140px;\n  }\n\n  .language-item {\n    width: 28px;\n    height: 28px;\n    font-size: 9px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;AACA;;;;AACA;;;;AAEA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;AACA;;;;AAEA;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;;;EAOA;;;;;EAKA"}}]}