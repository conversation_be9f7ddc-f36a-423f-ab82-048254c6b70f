"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/TestimonialsSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const TestimonialsSection = () => {
    const sectionRef = useRef(null);
    const titleRef = useRef(null);
    const cardsRef = useRef(null);

    const testimonials = [
        {
            id: 1,
            avatar: "👨‍💼",
            name: "<PERSON>",
            role: "Product Manager",
            text: "I have Parkinson's and this app has just made my life so much easier using my Mac. I can't type anymore but I can still dictate and <PERSON> has provided for me.",
            rating: 5
        },
        {
            id: 2,
            avatar: "👩‍💻",
            name: "<PERSON>",
            role: "Software Developer",
            text: "Hey! <PERSON> is currently blowing my mind with how fast and intuitive it is.",
            rating: 5
        },
        {
            id: 3,
            avatar: "👨‍🎨",
            name: "<PERSON>",
            role: "Creative Director",
            text: "I was able to dictate ~70% of our latest project with <PERSON><PERSON>. It was massive time saver!",
            rating: 5
        },
        {
            id: 4,
            avatar: "👩‍🏫",
            name: "<PERSON> <PERSON>",
            role: "Teacher",
            text: "Flow is that single little part of your brain that helps you remember all the sentences and have a pretty response.",
            rating: 5
        },
        {
            id: 5,
            avatar: "👨‍⚕️",
            name: "Dr. Robert Smith",
            role: "Medical Professional",
            text: "Stuttering a bit left works really well there's really a back, Flow works really well.",
            rating: 5
        },
        {
            id: 6,
            avatar: "👩‍💼",
            name: "Lisa Anderson",
            role: "Business Analyst",
            text: "I'm afraid I'm getting addicted to this great platform. It allows me to get into a flow state and not worry about the typing or dictation stop and interrupt me.",
            rating: 5
        },
        {
            id: 7,
            avatar: "👨‍🔬",
            name: "David Wilson",
            role: "Researcher",
            text: "You're making texting actually fun again and I feel like I'm becoming a can't live without it tool. I'm now faster than ever because Flow is not on there.",
            rating: 5
        }
    ];

    useEffect(() => {
        const section = sectionRef.current;
        const title = titleRef.current;
        const cards = cardsRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([title, cards], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(title, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(cards, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4");

        // Stagger animation for individual cards
        const testimonialCards = cards?.querySelectorAll('.testimonial-card');
        if (testimonialCards) {
            gsap.fromTo(testimonialCards, 
                { opacity: 0, y: 30 },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    stagger: 0.1,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: cards,
                        start: "top 80%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        }

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    const renderStars = (rating) => {
        return Array.from({ length: 5 }, (_, index) => (
            <span key={index} className={`star ${index < rating ? 'filled' : ''}`}>
                ⭐
            </span>
        ));
    };

    return (
        <section className="testimonials-section" ref={sectionRef}>
            <div className="testimonials-container">
                <div className="section-header" ref={titleRef}>
                    <h2 className="section-title">
                        Love letters<br />
                        to Flow
                    </h2>
                    <div className="decorative-lines">
                        <div className="line line-1"></div>
                        <div className="line line-2"></div>
                        <div className="line line-3"></div>
                        <div className="line line-4"></div>
                        <div className="line line-5"></div>
                        <div className="line line-6"></div>
                    </div>
                </div>

                <div className="testimonials-grid" ref={cardsRef}>
                    {testimonials.map((testimonial, index) => (
                        <div 
                            key={testimonial.id} 
                            className={`testimonial-card card-${index + 1}`}
                        >
                            <div className="card-header">
                                <div className="avatar">
                                    {testimonial.avatar}
                                </div>
                                <div className="user-info">
                                    <h4 className="user-name">{testimonial.name}</h4>
                                    <p className="user-role">{testimonial.role}</p>
                                </div>
                            </div>
                            
                            <div className="testimonial-content">
                                <p className="testimonial-text">
                                    "{testimonial.text}"
                                </p>
                            </div>
                            
                            <div className="rating">
                                {renderStars(testimonial.rating)}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default TestimonialsSection;
