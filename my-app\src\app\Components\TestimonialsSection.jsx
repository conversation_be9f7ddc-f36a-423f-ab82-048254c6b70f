"use client";
import React, { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/TestimonialsSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const TestimonialsSection = () => {
    const sectionRef = useRef(null);
    const titleRef = useRef(null);
    const carouselRef = useRef(null);
    const [currentSlide, setCurrentSlide] = useState(0);

    const testimonials = [
        {
            id: 1,
            avatar: "👨‍💼",
            name: "<PERSON>",
            role: "Product Manager",
            company: "TechCorp",
            text: "I have Parkinson's and this app has just made my life so much easier using my Mac. I can't type anymore but I can still dictate and Flow has provided for me.",
            rating: 5
        },
        {
            id: 2,
            avatar: "👩‍💻",
            name: "<PERSON>",
            role: "Software Developer",
            company: "StartupXYZ",
            text: "Hey! <PERSON> is currently blowing my mind with how fast and intuitive it is. The AI corrections are spot-on every time.",
            rating: 5
        },
        {
            id: 3,
            avatar: "👨‍🎨",
            name: "<PERSON>",
            role: "Creative Director",
            company: "Design Studio",
            text: "I was able to dictate ~70% of our latest project with Flow. It was a massive time saver and the quality is incredible!",
            rating: 5
        },
        {
            id: 4,
            avatar: "👩‍🏫",
            name: "Jennifer Kim",
            role: "Teacher",
            company: "Education Plus",
            text: "Flow is that single little part of your brain that helps you remember all the sentences and have a pretty response. Perfect for lesson planning!",
            rating: 5
        },
        {
            id: 5,
            avatar: "👨‍⚕️",
            name: "Dr. Robert Smith",
            role: "Medical Professional",
            company: "Health Center",
            text: "As someone who struggles with typing, Flow works really well. It understands medical terminology perfectly.",
            rating: 5
        },
        {
            id: 6,
            avatar: "👩‍💼",
            name: "Lisa Anderson",
            role: "Business Analyst",
            company: "Corp Solutions",
            text: "I'm afraid I'm getting addicted to this great platform. It allows me to get into a flow state and not worry about typing interruptions.",
            rating: 5
        }
    ];

    const slidesToShow = 3;
    const maxSlide = Math.max(0, testimonials.length - slidesToShow);

    useEffect(() => {
        const section = sectionRef.current;
        const title = titleRef.current;
        const carousel = carouselRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([title, carousel], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(title, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(carousel, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4");

        // Auto-play carousel
        const autoPlay = setInterval(() => {
            setCurrentSlide(prev => (prev >= maxSlide ? 0 : prev + 1));
        }, 5000);

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
            clearInterval(autoPlay);
        };
    }, [maxSlide]);

    const nextSlide = () => {
        setCurrentSlide(prev => (prev >= maxSlide ? 0 : prev + 1));
    };

    const prevSlide = () => {
        setCurrentSlide(prev => (prev <= 0 ? maxSlide : prev - 1));
    };

    const goToSlide = (index) => {
        setCurrentSlide(index);
    };

    const renderStars = (rating) => {
        return Array.from({ length: 5 }, (_, index) => (
            <span key={index} className={`star ${index < rating ? 'filled' : ''}`}>
                ⭐
            </span>
        ));
    };

    return (
        <section className="testimonials-section" ref={sectionRef}>
            <div className="testimonials-container">
                <div className="section-header" ref={titleRef}>
                    <h2 className="section-title">
                        Love letters<br />
                        to Flow
                    </h2>
                    <p className="section-subtitle">
                        See what our users are saying about their Flow experience
                    </p>
                </div>

                <div className="carousel-container" ref={carouselRef}>
                    <div className="carousel-wrapper">
                        <div
                            className="carousel-track"
                            style={{
                                transform: `translateX(-${currentSlide * (100 / slidesToShow)}%)`
                            }}
                        >
                            {testimonials.map((testimonial, index) => (
                                <div
                                    key={testimonial.id}
                                    className="testimonial-card"
                                >
                                    <div className="card-header">
                                        <div className="avatar">
                                            {testimonial.avatar}
                                        </div>
                                        <div className="user-info">
                                            <h4 className="user-name">{testimonial.name}</h4>
                                            <p className="user-role">{testimonial.role}</p>
                                            <p className="user-company">{testimonial.company}</p>
                                        </div>
                                    </div>

                                    <div className="testimonial-content">
                                        <p className="testimonial-text">
                                            "{testimonial.text}"
                                        </p>
                                    </div>

                                    <div className="rating">
                                        {renderStars(testimonial.rating)}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="carousel-controls">
                        <button
                            className="carousel-btn prev-btn"
                            onClick={prevSlide}
                            aria-label="Previous testimonials"
                        >
                            ←
                        </button>
                        <button
                            className="carousel-btn next-btn"
                            onClick={nextSlide}
                            aria-label="Next testimonials"
                        >
                            →
                        </button>
                    </div>

                    <div className="carousel-dots">
                        {Array.from({ length: maxSlide + 1 }, (_, index) => (
                            <button
                                key={index}
                                className={`dot ${index === currentSlide ? 'active' : ''}`}
                                onClick={() => goToSlide(index)}
                                aria-label={`Go to slide ${index + 1}`}
                            />
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default TestimonialsSection;
