{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/SavingsCalculator.css"], "sourcesContent": ["/* Savings Calculator Section */\n.savings-calculator-section {\n  background: linear-gradient(135deg, #064e3b 0%, #047857 100%);\n  padding: 120px 20px;\n  min-height: 100vh;\n  color: white;\n}\n\n.savings-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 60px;\n}\n\n.content-section {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 80px;\n  align-items: center;\n}\n\n.text-content {\n  display: flex;\n  flex-direction: column;\n  gap: 32px;\n}\n\n.section-title {\n  font-size: 3.5rem;\n  font-weight: 700;\n  color: white;\n  margin: 0;\n  line-height: 1.1;\n  font-family: 'Georgia', serif;\n}\n\n.scenario-text {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.scenario-main {\n  font-size: 1.5rem;\n  color: #d1fae5;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.scenario-main strong {\n  color: #6ee7b7;\n  font-weight: 700;\n}\n\n.scenario-detail {\n  font-size: 1rem;\n  color: #a7f3d0;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.value-statement {\n  font-size: 1.25rem;\n  color: #d1fae5;\n  margin: 0;\n  line-height: 1.4;\n}\n\n.value-statement strong {\n  color: #6ee7b7;\n  font-weight: 700;\n}\n\n.input-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.input-group {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.input-group label {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #a7f3d0;\n}\n\n.slider {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 100%;\n  height: 8px;\n  border-radius: 4px;\n  background: #065f46;\n  outline: none;\n  cursor: pointer;\n}\n\n.slider::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  appearance: none;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  cursor: pointer;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n  transition: all 0.3s ease;\n}\n\n.slider::-webkit-slider-thumb:hover {\n  transform: scale(1.1);\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\n}\n\n.slider::-moz-range-thumb {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  cursor: pointer;\n  border: none;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n}\n\n.slider-value {\n  font-size: 1.125rem;\n  font-weight: 700;\n  color: #6ee7b7;\n  text-align: center;\n  background: rgba(16, 185, 129, 0.2);\n  padding: 8px 16px;\n  border-radius: 20px;\n  width: fit-content;\n  align-self: center;\n}\n\n.illustration-section {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.character-illustration {\n  position: relative;\n  width: 300px;\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.character {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 20px;\n  z-index: 2;\n}\n\n.character-head {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.face {\n  font-size: 4rem;\n  animation: bounce 2s ease-in-out infinite;\n}\n\n.hat {\n  font-size: 2rem;\n  position: absolute;\n  top: -20px;\n  animation: tilt 3s ease-in-out infinite;\n}\n\n.character-body {\n  display: flex;\n  justify-content: center;\n}\n\n.arms {\n  display: flex;\n  gap: 40px;\n}\n\n.arm {\n  font-size: 2rem;\n  animation: juggle 1.5s ease-in-out infinite;\n}\n\n.arm.left {\n  animation-delay: 0s;\n}\n\n.arm.right {\n  animation-delay: 0.75s;\n}\n\n.money-animation {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.money-icon {\n  position: absolute;\n  font-size: 2rem;\n  animation: float 3s ease-in-out infinite;\n}\n\n@keyframes bounce {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-10px); }\n}\n\n@keyframes tilt {\n  0%, 100% { transform: rotate(-5deg); }\n  50% { transform: rotate(5deg); }\n}\n\n@keyframes juggle {\n  0%, 100% { transform: translateY(0) rotate(0deg); }\n  50% { transform: translateY(-15px) rotate(10deg); }\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.8; }\n  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }\n}\n\n.calculator-section {\n  display: flex;\n  justify-content: center;\n}\n\n.calculator-card {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 24px;\n  padding: 40px;\n  max-width: 500px;\n  width: 100%;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n}\n\n.calculator-header {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.calculator-header h3 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #a7f3d0;\n  margin: 0 0 16px 0;\n}\n\n.savings-amount {\n  font-size: 3rem;\n  font-weight: 700;\n  color: #6ee7b7;\n  display: flex;\n  align-items: baseline;\n  justify-content: center;\n  gap: 8px;\n}\n\n.period {\n  font-size: 1.5rem;\n  color: #a7f3d0;\n  font-weight: 500;\n}\n\n.breakdown {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  margin-bottom: 32px;\n  padding: 24px 0;\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.breakdown-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.breakdown-item .label {\n  font-size: 1rem;\n  color: #d1fae5;\n}\n\n.breakdown-item .value {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #6ee7b7;\n}\n\n.breakdown-item .value.negative {\n  color: #fca5a5;\n}\n\n.calculator-footer {\n  text-align: center;\n}\n\n.get-started-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin: 0 auto;\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n}\n\n.get-started-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);\n}\n\n.arrow {\n  transition: transform 0.3s ease;\n}\n\n.get-started-btn:hover .arrow {\n  transform: translateX(4px);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .content-section {\n    gap: 60px;\n  }\n\n  .section-title {\n    font-size: 3rem;\n  }\n\n  .character-illustration {\n    width: 250px;\n    height: 250px;\n  }\n\n  .face {\n    font-size: 3rem;\n  }\n\n  .calculator-card {\n    padding: 32px;\n  }\n}\n\n@media (max-width: 768px) {\n  .savings-calculator-section {\n    padding: 80px 20px;\n  }\n\n  .content-section {\n    grid-template-columns: 1fr;\n    gap: 40px;\n    text-align: center;\n  }\n\n  .section-title {\n    font-size: 2.5rem;\n  }\n\n  .scenario-main {\n    font-size: 1.25rem;\n  }\n\n  .value-statement {\n    font-size: 1.125rem;\n  }\n\n  .character-illustration {\n    width: 200px;\n    height: 200px;\n  }\n\n  .face {\n    font-size: 2.5rem;\n  }\n\n  .hat {\n    font-size: 1.5rem;\n  }\n\n  .arm {\n    font-size: 1.5rem;\n  }\n\n  .money-icon {\n    font-size: 1.5rem;\n  }\n\n  .calculator-card {\n    padding: 24px;\n    margin: 0 20px;\n  }\n\n  .savings-amount {\n    font-size: 2.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .savings-calculator-section {\n    padding: 60px 15px;\n  }\n\n  .section-title {\n    font-size: 2rem;\n  }\n\n  .scenario-main {\n    font-size: 1.125rem;\n  }\n\n  .scenario-detail {\n    font-size: 0.875rem;\n  }\n\n  .value-statement {\n    font-size: 1rem;\n  }\n\n  .character-illustration {\n    width: 150px;\n    height: 150px;\n  }\n\n  .face {\n    font-size: 2rem;\n  }\n\n  .calculator-card {\n    padding: 20px;\n    margin: 0 10px;\n  }\n\n  .savings-amount {\n    font-size: 2rem;\n  }\n\n  .calculator-header h3 {\n    font-size: 1.25rem;\n  }\n\n  .breakdown-item .label,\n  .breakdown-item .value {\n    font-size: 0.875rem;\n  }\n\n  .get-started-btn {\n    padding: 14px 24px;\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;;;AAKA;;;;;;;;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;AAiBA;;;;;AAKA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAYA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAKA"}}]}