/* [project]/src/Style/PricingSection.css [app-client] (css) */
.pricing-section {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  min-height: 100vh;
  padding: 120px 20px;
}

.pricing-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.pricing-header {
  text-align: center;
  margin-bottom: 60px;
}

.pricing-title {
  color: #581c87;
  margin: 0 0 16px;
  font-family: Georgia, serif;
  font-size: 4rem;
  font-weight: 700;
}

.pricing-subtitle {
  color: #7c3aed;
  margin: 0 0 40px;
  font-size: 1.2rem;
  line-height: 1.6;
}

.billing-toggle {
  backdrop-filter: blur(10px);
  background: #fffc;
  border: 1px solid #8b5cf633;
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  gap: 20px;
  width: fit-content;
  margin: 0 auto;
  padding: 8px;
  display: flex;
}

.toggle-label {
  color: #6b7280;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 600;
  transition: color .3s;
  display: flex;
}

.toggle-label.active {
  color: #7c3aed;
}

.discount-badge {
  color: #fff;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: .75rem;
  font-weight: 600;
}

.toggle-switch {
  cursor: pointer;
  background: #e5e7eb;
  border-radius: 16px;
  width: 60px;
  height: 32px;
  transition: background .3s;
  position: relative;
}

.toggle-switch:hover {
  background: #d1d5db;
}

.toggle-slider {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  transition: transform .3s;
  position: absolute;
  top: 2px;
  left: 2px;
  box-shadow: 0 2px 8px #7c3aed4d;
}

.toggle-slider.yearly {
  transform: translateX(28px);
}

.pricing-cards {
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 60px;
  display: grid;
}

.pricing-card {
  background: #fff;
  border: 2px solid #0000;
  border-radius: 20px;
  padding: 32px 24px;
  transition: all .3s;
  position: relative;
  box-shadow: 0 4px 20px #00000014;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px #0000001f;
}

.pricing-card.popular {
  border-color: #7c3aed;
  transform: scale(1.05);
  box-shadow: 0 8px 30px #7c3aed33;
}

.popular-badge {
  color: #fff;
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border-radius: 20px;
  padding: 6px 20px;
  font-size: .875rem;
  font-weight: 600;
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
}

.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.plan-title {
  color: #1f2937;
  margin: 0 0 16px;
  font-size: 1.5rem;
  font-weight: 700;
}

.price-container {
  flex-direction: column;
  align-items: center;
  gap: 8px;
  display: flex;
}

.price-main {
  align-items: baseline;
  gap: 4px;
  display: flex;
}

.price {
  color: #7c3aed;
  font-size: 2.5rem;
  font-weight: 700;
}

.period {
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
}

.price-details {
  align-items: center;
  gap: 8px;
  display: flex;
}

.original-price {
  color: #9ca3af;
  font-size: .875rem;
  text-decoration: line-through;
}

.savings {
  color: #166534;
  background: #dcfce7;
  border-radius: 8px;
  padding: 2px 8px;
  font-size: .75rem;
  font-weight: 600;
}

.card-features {
  margin-bottom: 32px;
}

.features-list {
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.feature-item {
  color: #4b5563;
  align-items: flex-start;
  gap: 12px;
  font-size: .875rem;
  line-height: 1.5;
  display: flex;
}

.check-icon {
  color: #10b981;
  flex-shrink: 0;
  margin-top: 2px;
  font-size: 1rem;
  font-weight: 700;
}

.card-footer {
  text-align: center;
}

.cta-button {
  cursor: pointer;
  border: 2px solid #0000;
  border-radius: 12px;
  width: 100%;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.cta-button.primary {
  color: #fff;
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border-color: #7c3aed;
}

.cta-button.primary:hover {
  background: linear-gradient(135deg, #6d28d9 0%, #4c1d95 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px #7c3aed4d;
}

.cta-button.outline {
  color: #7c3aed;
  background: none;
  border-color: #7c3aed;
}

.cta-button.outline:hover {
  color: #fff;
  background: #7c3aed;
  transform: translateY(-1px);
}

.pricing-footer {
  backdrop-filter: blur(10px);
  background: #fff9;
  border: 1px solid #8b5cf61a;
  border-radius: 16px;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  display: flex;
}

.money-back-guarantee {
  align-items: center;
  gap: 16px;
  display: flex;
}

.guarantee-icon {
  font-size: 2rem;
}

.guarantee-text strong {
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 1rem;
  display: block;
}

.guarantee-text p {
  color: #6b7280;
  margin: 0;
  font-size: .875rem;
  line-height: 1.4;
}

.contact-sales-btn {
  color: #7c3aed;
  cursor: pointer;
  background: none;
  border: 2px solid #7c3aed;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.contact-sales-btn:hover {
  color: #fff;
  background: #7c3aed;
  transform: translateY(-1px);
}

@media (width <= 1200px) {
  .pricing-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .pricing-card.popular {
    transform: none;
  }

  .pricing-footer {
    text-align: center;
    flex-direction: column;
    gap: 20px;
  }
}

@media (width <= 768px) {
  .pricing-section {
    padding: 80px 20px;
  }

  .pricing-title {
    font-size: 2.5rem;
  }

  .pricing-subtitle {
    font-size: 1rem;
  }

  .billing-toggle {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .toggle-label {
    font-size: .875rem;
  }

  .discount-badge {
    font-size: .7rem;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pricing-card {
    padding: 24px 20px;
  }

  .plan-title {
    font-size: 1.25rem;
  }

  .price {
    font-size: 2rem;
  }

  .money-back-guarantee {
    text-align: center;
    flex-direction: column;
    gap: 12px;
  }
}

@media (width <= 480px) {
  .pricing-section {
    padding: 60px 15px;
  }

  .pricing-title {
    font-size: 2rem;
  }

  .pricing-header {
    margin-bottom: 40px;
  }

  .billing-toggle {
    width: 100%;
    max-width: 300px;
  }

  .pricing-card {
    padding: 20px 16px;
  }

  .price {
    font-size: 1.75rem;
  }

  .feature-item {
    font-size: .8rem;
  }

  .pricing-footer {
    padding: 20px;
  }

  .guarantee-text strong {
    font-size: .875rem;
  }

  .guarantee-text p {
    font-size: .8rem;
  }
}

/*# sourceMappingURL=src_Style_PricingSection_css_f9ee138c._.single.css.map*/