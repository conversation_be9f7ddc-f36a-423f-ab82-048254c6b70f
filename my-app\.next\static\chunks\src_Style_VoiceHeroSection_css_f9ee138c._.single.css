/* [project]/src/Style/VoiceHeroSection.css [app-client] (css) */
.voice-hero-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 80px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.voice-hero-container {
  text-align: center;
  width: 100%;
  max-width: 1200px;
  position: relative;
}

.curved-text-container {
  z-index: 1;
  width: 200px;
  height: 200px;
  position: absolute;
  top: -100px;
  right: 10%;
}

.curved-svg {
  width: 100%;
  height: 100%;
}

.curved-text-path {
  fill: #a3a3a3;
  font-family: Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.voice-indicator-container {
  z-index: 2;
  align-items: center;
  gap: 12px;
  display: flex;
  position: absolute;
  top: 20%;
  left: 15%;
}

.voice-indicator {
  background: #1f2937;
  border-radius: 20px;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  display: flex;
  box-shadow: 0 4px 20px #00000026;
}

.voice-wave {
  background: #3b82f6;
  border-radius: 2px;
  width: 3px;
  height: 20px;
  animation: 1s ease-in-out infinite wave;
}

.voice-wave:first-child {
  animation-delay: 0s;
}

.voice-wave:nth-child(2) {
  animation-delay: .1s;
}

.voice-wave:nth-child(3) {
  animation-delay: .2s;
}

.voice-wave:nth-child(4) {
  animation-delay: .3s;
}

.voice-wave:nth-child(5) {
  animation-delay: .4s;
}

.voice-wave:nth-child(6) {
  animation-delay: .5s;
}

.voice-wave:nth-child(7) {
  animation-delay: .6s;
}

.voice-wave:nth-child(8) {
  animation-delay: .7s;
}

@keyframes wave {
  0%, 100% {
    opacity: .7;
    transform: scaleY(.3);
  }

  50% {
    opacity: 1;
    transform: scaleY(1);
  }
}

.voice-label {
  color: #1f2937;
  background: #fff;
  border-radius: 12px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  box-shadow: 0 2px 10px #0000001a;
}

.voice-label:before {
  content: "";
  border-top: 6px solid #0000;
  border-bottom: 6px solid #0000;
  border-right: 6px solid #fff;
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: -6px;
  transform: translateY(-50%);
}

.main-content {
  z-index: 3;
  margin-top: 60px;
  position: relative;
}

.hero-title {
  color: #1f2937;
  margin: 0 0 24px;
  font-family: Georgia, serif;
  font-size: 4.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.highlight {
  color: #1f2937;
  font-weight: 600;
  position: relative;
}

.highlight:after {
  content: "";
  z-index: -1;
  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 4px;
  height: 8px;
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
}

.hero-subtitle {
  color: #4b5563;
  max-width: 600px;
  margin: 0 auto 40px;
  font-size: 1.25rem;
  line-height: 1.6;
}

.cta-buttons {
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
  display: flex;
}

.primary-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 15px #3b82f64d;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #3b82f666;
}

.secondary-btn {
  color: #1f2937;
  cursor: pointer;
  background: none;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
}

.secondary-btn:hover {
  color: #3b82f6;
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.availability {
  color: #6b7280;
  margin: 0;
  font-size: .875rem;
}

@media (width <= 1024px) {
  .curved-text-container {
    width: 160px;
    height: 160px;
    top: -80px;
    right: 5%;
  }

  .voice-indicator-container {
    top: 25%;
    left: 10%;
  }

  .hero-title {
    font-size: 3.5rem;
  }
}

@media (width <= 768px) {
  .voice-hero-section {
    padding: 60px 20px;
  }

  .curved-text-container {
    display: none;
  }

  .voice-indicator-container {
    justify-content: center;
    margin-bottom: 40px;
    position: static;
  }

  .main-content {
    margin-top: 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .primary-btn, .secondary-btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (width <= 480px) {
  .voice-hero-section {
    padding: 40px 15px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .voice-indicator {
    padding: 10px 14px;
  }

  .voice-wave {
    width: 2px;
    height: 16px;
  }

  .voice-label {
    padding: 6px 10px;
    font-size: 12px;
  }
}

/*# sourceMappingURL=src_Style_VoiceHeroSection_css_f9ee138c._.single.css.map*/