/* Features Showcase Styles */
.features-showcase {
  padding: 80px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-header {
  text-align: center;
  margin-bottom: 60px;
}

.features-title {
  font-size: 3rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.features-subtitle {
  font-size: 1.2rem;
  color: #718096;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  align-items: start;
}

.feature-card {
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-large {
  grid-column: span 2;
  min-height: 400px;
}

.feature-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 20px;
}

.feature-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.phone-mockup {
  width: 200px;
  height: 300px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 25px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

/* AI Interface */
.ai-interface {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.ai-suggestions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.suggestion-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-controls {
  display: flex;
  justify-content: center;
}

.ai-btn {
  background: #4facfe;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ai-btn:hover {
  background: #3d8bfe;
  transform: scale(1.05);
}

/* Dictionary Interface */
.dictionary-interface {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.dict-header {
  color: #4facfe;
  font-weight: 600;
  text-align: center;
  font-size: 14px;
}

.dict-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dict-item {
  background: rgba(79, 172, 254, 0.2);
  color: white;
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 11px;
  text-align: center;
  border: 1px solid rgba(79, 172, 254, 0.3);
}

/* Tones Interface */
.tones-interface {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;
}

.app-selector {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.app-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.app-icon.active {
  background: rgba(79, 172, 254, 0.3);
  border: 2px solid #4facfe;
}

.tone-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tone-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 11px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tone-btn.active {
  background: rgba(79, 172, 254, 0.3);
  border: 1px solid #4facfe;
}

/* Languages Interface */
.languages-interface {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.lang-wheel {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.lang-item {
  position: absolute;
  width: 30px;
  height: 30px;
  background: rgba(67, 233, 123, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  color: white;
  border: 1px solid rgba(67, 233, 123, 0.5);
}

.lang-item:nth-child(1) { top: -15px; left: 50%; transform: translateX(-50%); }
.lang-item:nth-child(2) { top: 15px; right: -15px; }
.lang-item:nth-child(3) { bottom: 15px; right: -15px; }
.lang-item:nth-child(4) { bottom: -15px; left: 50%; transform: translateX(-50%); }
.lang-item:nth-child(5) { bottom: 15px; left: -15px; }
.lang-item:nth-child(6) { top: 15px; left: -15px; }

.lang-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(67, 233, 123, 0.5);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  font-size: 12px;
  border: 2px solid rgba(67, 233, 123, 0.7);
}

.feature-text {
  text-align: center;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-description {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .features-showcase {
    padding: 60px 15px;
  }
  
  .features-title {
    font-size: 2.5rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .feature-large {
    grid-column: span 1;
  }
  
  .feature-card {
    padding: 20px;
  }
  
  .phone-mockup {
    width: 150px;
    height: 220px;
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .features-title {
    font-size: 2rem;
  }
  
  .features-subtitle {
    font-size: 1rem;
  }
  
  .phone-mockup {
    width: 120px;
    height: 180px;
    padding: 10px;
  }
}
