"use client"
import React, { useState } from 'react';
import { ChevronDown, Menu, X } from 'lucide-react';

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <nav className="w-full px-6 py-4">
      <div  style={{ backgroundColor: '#034F46' }} className="max-w-3xl mx-auto flex items-center justify-center gap-20 border-2 border-[#E4E4D0]  p-2 rounded-xl " >
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <div className="w-6 h-6 bg-gray-800 rounded-sm flex items-center justify-center">
              <div className="grid grid-cols-2 gap-0.5">
                <div className="w-1.5 h-1.5 bg-white rounded-xs"></div>
                <div className="w-1.5 h-1.5 bg-white rounded-xs"></div>
                <div className="w-1.5 h-1.5 bg-white rounded-xs"></div>
                <div className="w-1.5 h-1.5 bg-white rounded-xs"></div>
              </div>
            </div>
            <span className="text-xl font-semibold text-gray-900">Flow</span>
          </div>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <div className="flex items-center space-x-1 text-white hover:text-white cursor-pointer">
            <span>Product</span>
            <ChevronDown className="w-4 h-4" />
          </div>
          <a href="#" className="text-white hover:text-white">
            Pricing
          </a>
          <div className="flex items-center space-x-1 text-white hover:text-white cursor-pointer">
            <span>About</span>
            <ChevronDown className="w-4 h-4" />
          </div>
        </div>

        {/* Desktop CTA Button */}
        <div className="hidden md:block">
          <button className="bg-[#F0D7FF] hover:bg-[#F0D7FF]  px-6 py-2 border-2 border-black rounded-lg font-medium transition-colors duration-200">
            Download for free
          </button>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden p-2"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? (
            <X className="w-6 h-6 text-gray-700" />
          ) : (
            <Menu className="w-6 h-6 text-gray-700" />
          )}
        </button>
      </div>

          {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden mt-4 pb-4 border-t border-gray-200">
          <div className="flex flex-col space-y-4 pt-4">
            <div className="flex items-center justify-between text-gray-700">
              <span>Product</span>
              <ChevronDown className="w-4 h-4" />
            </div>
            <a href="#" className="text-gray-700">
              Pricing
            </a>
            <div className="flex items-center justify-between text-gray-700">
              <span>About</span>
              <ChevronDown className="w-4 h-4" />
            </div>
            <button className="bg-[#F0D7FF]  hover:bg-[#F0D7FF] px-6 py-2  rounded-lg font-medium transition-colors duration-200 mt-4 ">
              Download for free
            </button>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;