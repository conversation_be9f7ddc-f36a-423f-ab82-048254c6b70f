"use client";
import React, { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/AIAutoEditsSection.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const AIAutoEditsSection = () => {
    const sectionRef = useRef(null);
    const phoneRef = useRef(null);
    const contentRef = useRef(null);
    const [currentSuggestion, setCurrentSuggestion] = useState(0);

    const suggestions = [
        "✨ Enhance clarity and flow",
        "🎯 Improve tone and style", 
        "📝 Fix grammar and spelling",
        "🔄 Restructure for impact"
    ];

    const originalText = "Let's brainstorm a few ideas for our next campaign. We need something that will resonate with our target audience and drive engagement.";
    const improvedText = "Let's collaborate on innovative campaign concepts that will deeply resonate with our target demographic and significantly boost engagement metrics.";

    useEffect(() => {
        const section = sectionRef.current;
        const phone = phoneRef.current;
        const content = contentRef.current;

        if (!section) return;

        // Initial setup
        gsap.set([phone, content], { opacity: 0, y: 50 });

        // Create timeline for entrance animations
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: section,
                start: "top 70%",
                toggleActions: "play none none reverse"
            }
        });

        tl.to(phone, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        })
        .to(content, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power3.out"
        }, "-=0.4");

        // Floating animation for phone
        gsap.to(phone, {
            y: -8,
            duration: 3,
            ease: "power2.inOut",
            yoyo: true,
            repeat: -1
        });

        // Cycle through suggestions
        const suggestionInterval = setInterval(() => {
            setCurrentSuggestion(prev => (prev + 1) % suggestions.length);
        }, 3000);

        return () => {
            clearInterval(suggestionInterval);
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    return (
        <section className="ai-auto-edits-section" ref={sectionRef}>
            <div className="ai-auto-edits-container">
                <div className="phone-mockup-container" ref={phoneRef}>
                    <div className="phone-device">
                        <div className="phone-screen">
                            <div className="screen-header">
                                <div className="status-bar">
                                    <span className="time">9:41</span>
                                    <div className="status-icons">
                                        <span className="signal">📶</span>
                                        <span className="wifi">📶</span>
                                        <span className="battery">🔋</span>
                                    </div>
                                </div>
                                <div className="app-header">
                                    <h3>Flow - AI Writing Assistant</h3>
                                </div>
                            </div>
                            
                            <div className="screen-content">
                                <div className="text-editor">
                                    <div className="original-text">
                                        <p>{originalText}</p>
                                    </div>
                                    
                                    <div className="ai-suggestions">
                                        <div className="suggestion-header">
                                            <span className="ai-icon">🤖</span>
                                            <span>AI Suggestions</span>
                                        </div>
                                        
                                        <div className="suggestion-items">
                                            {suggestions.map((suggestion, index) => (
                                                <div 
                                                    key={index}
                                                    className={`suggestion-item ${index === currentSuggestion ? 'active' : ''}`}
                                                >
                                                    {suggestion}
                                                </div>
                                            ))}
                                        </div>
                                        
                                        <div className="action-buttons">
                                            <button className="apply-btn">Apply All</button>
                                            <button className="preview-btn">Preview</button>
                                        </div>
                                    </div>
                                    
                                    <div className="improved-text">
                                        <div className="improved-header">
                                            <span className="check-icon">✅</span>
                                            <span>Improved Version</span>
                                        </div>
                                        <p className="typing-text">{improvedText}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="content-section" ref={contentRef}>
                    <div className="content-badge">
                        <span className="sparkle">✨</span>
                        AI-Powered
                    </div>
                    
                    <h2 className="section-title">AI Auto Edits</h2>
                    
                    <p className="section-description">
                        Capture naturally and have interactive voice assistants 
                        that work seamlessly. Enhance thoughts flowing into 
                        structured, polished text—quickly, reliably, naturally.
                    </p>
                    
                    <div className="features-list">
                        <div className="feature-item">
                            <span className="feature-icon">🎯</span>
                            <span>Smart tone adjustment</span>
                        </div>
                        <div className="feature-item">
                            <span className="feature-icon">📝</span>
                            <span>Grammar & style fixes</span>
                        </div>
                        <div className="feature-item">
                            <span className="feature-icon">🔄</span>
                            <span>Content restructuring</span>
                        </div>
                        <div className="feature-item">
                            <span className="feature-icon">✨</span>
                            <span>Clarity enhancement</span>
                        </div>
                    </div>
                    
                    <button className="cta-button">
                        Try AI Auto Edits
                        <span className="arrow">→</span>
                    </button>
                </div>
            </div>
        </section>
    );
};

export default AIAutoEditsSection;
