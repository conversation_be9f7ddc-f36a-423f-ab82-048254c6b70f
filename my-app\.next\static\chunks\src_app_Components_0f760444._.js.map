{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/VoiceHeroSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport \"@/Style/VoiceHeroSection.css\";\n\nconst VoiceHeroSection = () => {\n    const sectionRef = useRef(null);\n    const curvedTextRef = useRef(null);\n    const voiceIndicatorRef = useRef(null);\n    const mainTextRef = useRef(null);\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const curvedText = curvedTextRef.current;\n        const voiceIndicator = voiceIndicatorRef.current;\n        const mainText = mainTextRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([curvedText, voiceIndicator, mainText], { opacity: 0, y: 30 });\n\n        // Create entrance timeline\n        const tl = gsap.timeline({ delay: 0.5 });\n\n        tl.to(curvedText, {\n            opacity: 1,\n            y: 0,\n            duration: 1,\n            ease: \"power3.out\"\n        })\n        .to(voiceIndicator, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.5\")\n        .to(mainText, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Animate curved text continuously\n        gsap.to(curvedText, {\n            rotation: 360,\n            duration: 20,\n            ease: \"none\",\n            repeat: -1\n        });\n\n        // Voice indicator animation\n        const waves = voiceIndicator?.querySelectorAll('.voice-wave');\n        if (waves) {\n            waves.forEach((wave, index) => {\n                gsap.to(wave, {\n                    scaleY: Math.random() * 0.5 + 0.5,\n                    duration: 0.5 + Math.random() * 0.5,\n                    ease: \"power2.inOut\",\n                    yoyo: true,\n                    repeat: -1,\n                    delay: index * 0.1\n                });\n            });\n        }\n\n        return () => {\n            gsap.killTweensOf([curvedText, voiceIndicator, mainText]);\n        };\n    }, []);\n\n    return (\n        <section className=\"voice-hero-section\" ref={sectionRef}>\n            <div className=\"voice-hero-container\">\n                <div className=\"curved-text-container\">\n                    <div className=\"curved-text\" ref={curvedTextRef}>\n                        <svg viewBox=\"0 0 200 200\" className=\"curved-svg\">\n                            <defs>\n                                <path\n                                    id=\"circle\"\n                                    d=\"M 100, 100 m -75, 0 a 75,75 0 1,1 150,0 a 75,75 0 1,1 -150,0\"\n                                />\n                            </defs>\n                            <text className=\"curved-text-path\">\n                                <textPath href=\"#circle\" startOffset=\"0%\">\n                                    their meeting were sent out, or mentioned it but didn't capture it •\n                                </textPath>\n                            </text>\n                        </svg>\n                    </div>\n                </div>\n\n                <div className=\"voice-indicator-container\" ref={voiceIndicatorRef}>\n                    <div className=\"voice-indicator\">\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                        <div className=\"voice-wave\"></div>\n                    </div>\n                    <div className=\"voice-label\">meeting were sent out, or</div>\n                </div>\n\n                <div className=\"main-content\" ref={mainTextRef}>\n                    <h1 className=\"hero-title\">\n                        Don't type, <span className=\"highlight\">just speak</span>\n                    </h1>\n                    \n                    <p className=\"hero-subtitle\">\n                        Effortless voice dictation in every application:<br />\n                        4x faster than typing, AI commands and auto-edits.\n                    </p>\n\n                    <div className=\"cta-buttons\">\n                        <button className=\"primary-btn\">\n                            🎤 Try Flow\n                        </button>\n                        <button className=\"secondary-btn\">\n                            Download\n                        </button>\n                    </div>\n\n                    <p className=\"availability\">\n                        Available on Mac, Windows and iPhone\n                    </p>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default VoiceHeroSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;;AAKA,MAAM,mBAAmB;;IACrB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,aAAa,cAAc,OAAO;YACxC,MAAM,iBAAiB,kBAAkB,OAAO;YAChD,MAAM,WAAW,YAAY,OAAO;YAEpC,IAAI,CAAC,SAAS;YAEd,gBAAgB;YAChB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;gBAAC;gBAAY;gBAAgB;aAAS,EAAE;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAErE,2BAA2B;YAC3B,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBAAE,OAAO;YAAI;YAEtC,GAAG,EAAE,CAAC,YAAY;gBACd,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GACC,EAAE,CAAC,gBAAgB;gBAChB,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,UAAU;gBACV,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG;YAEH,mCAAmC;YACnC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,YAAY;gBAChB,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,QAAQ,CAAC;YACb;YAEA,4BAA4B;YAC5B,MAAM,QAAQ,gBAAgB,iBAAiB;YAC/C,IAAI,OAAO;gBACP,MAAM,OAAO;kDAAC,CAAC,MAAM;wBACjB,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;4BACV,QAAQ,KAAK,MAAM,KAAK,MAAM;4BAC9B,UAAU,MAAM,KAAK,MAAM,KAAK;4BAChC,MAAM;4BACN,MAAM;4BACN,QAAQ,CAAC;4BACT,OAAO,QAAQ;wBACnB;oBACJ;;YACJ;YAEA;8CAAO;oBACH,gJAAA,CAAA,OAAI,CAAC,YAAY,CAAC;wBAAC;wBAAY;wBAAgB;qBAAS;gBAC5D;;QACJ;qCAAG,EAAE;IAEL,qBACI,6LAAC;QAAQ,WAAU;QAAqB,KAAK;kBACzC,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;wBAAc,KAAK;kCAC9B,cAAA,6LAAC;4BAAI,SAAQ;4BAAc,WAAU;;8CACjC,6LAAC;8CACG,cAAA,6LAAC;wCACG,IAAG;wCACH,GAAE;;;;;;;;;;;8CAGV,6LAAC;oCAAK,WAAU;8CACZ,cAAA,6LAAC;wCAAS,MAAK;wCAAU,aAAY;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1D,6LAAC;oBAAI,WAAU;oBAA4B,KAAK;;sCAC5C,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEnB,6LAAC;4BAAI,WAAU;sCAAc;;;;;;;;;;;;8BAGjC,6LAAC;oBAAI,WAAU;oBAAe,KAAK;;sCAC/B,6LAAC;4BAAG,WAAU;;gCAAa;8CACX,6LAAC;oCAAK,WAAU;8CAAY;;;;;;;;;;;;sCAG5C,6LAAC;4BAAE,WAAU;;gCAAgB;8CACuB,6LAAC;;;;;gCAAK;;;;;;;sCAI1D,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,6LAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;sCAKtC,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;;;;;;;;;;;;AAOhD;GAhIM;KAAA;uCAkIS", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Marquee.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport \"@/Style/Marquee.css\";\r\n\r\nconst Marquee = ({\r\n    items = [\"Creative\", \"Design\", \"Development\", \"Innovation\", \"Excellence\", \"Quality\", \"Performance\", \"Success\"],\r\n    speed = 1, // speed multiplier\r\n    colors = [\"#ff6b6b\", \"#4ecdc4\", \"#45b7d1\", \"#96ceb4\", \"#feca57\", \"#ff9ff3\", \"#54a0ff\", \"#5f27cd\"]\r\n}) => {\r\n    const wrapperRef = useRef(null);\r\n    const animationRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const wrapper = wrapperRef.current;\r\n        if (!wrapper) return;\r\n\r\n        const marqueeItems = wrapper.querySelectorAll(\".marquee-item\");\r\n\r\n        // Apply background colors\r\n        gsap.set(marqueeItems, {\r\n            backgroundColor: gsap.utils.wrap(colors),\r\n        });\r\n\r\n        // Create infinite scroll animation\r\n        const createAnimation = () => {\r\n            if (marqueeItems.length === 0) return;\r\n\r\n            // Calculate the total width of one set of items\r\n            let totalWidth = 0;\r\n            const itemCount = items.length; // Original items count\r\n\r\n            for (let i = 0; i < itemCount; i++) {\r\n                if (marqueeItems[i]) {\r\n                    totalWidth += marqueeItems[i].offsetWidth + 20; // 20px margin\r\n                }\r\n            }\r\n\r\n            // Animate the wrapper to move left infinitely\r\n            animationRef.current = gsap.to(wrapper, {\r\n                x: -totalWidth,\r\n                duration: totalWidth / (50 * speed), // Adjust duration based on speed\r\n                ease: \"none\",\r\n                repeat: -1,\r\n            });\r\n        };\r\n\r\n        // Wait for layout to be ready\r\n        const timer = setTimeout(createAnimation, 200);\r\n\r\n        return () => {\r\n            clearTimeout(timer);\r\n            if (animationRef.current) {\r\n                animationRef.current.kill();\r\n            }\r\n        };\r\n    }, [items, speed, colors]);\r\n    return (\r\n        <div className=\"marquee-container\">\r\n            <div className=\"marquee-wrapper\" ref={wrapperRef}>\r\n                {/* Duplicate items for seamless infinite loop */}\r\n                {[...items, ...items].map((item, index) => (\r\n                    <div key={index} className=\"marquee-item\">\r\n                        <div className=\"marquee-content\">\r\n                            {item}\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Marquee;"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;;AAKA,MAAM,UAAU,CAAC,EACb,QAAQ;IAAC;IAAY;IAAU;IAAe;IAAc;IAAc;IAAW;IAAe;CAAU,EAC9G,QAAQ,CAAC,EACT,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU,EACpG;;IACG,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,CAAC,SAAS;YAEd,MAAM,eAAe,QAAQ,gBAAgB,CAAC;YAE9C,0BAA0B;YAC1B,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,cAAc;gBACnB,iBAAiB,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACrC;YAEA,mCAAmC;YACnC,MAAM;qDAAkB;oBACpB,IAAI,aAAa,MAAM,KAAK,GAAG;oBAE/B,gDAAgD;oBAChD,IAAI,aAAa;oBACjB,MAAM,YAAY,MAAM,MAAM,EAAE,uBAAuB;oBAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;wBAChC,IAAI,YAAY,CAAC,EAAE,EAAE;4BACjB,cAAc,YAAY,CAAC,EAAE,CAAC,WAAW,GAAG,IAAI,cAAc;wBAClE;oBACJ;oBAEA,8CAA8C;oBAC9C,aAAa,OAAO,GAAG,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;wBACpC,GAAG,CAAC;wBACJ,UAAU,aAAa,CAAC,KAAK,KAAK;wBAClC,MAAM;wBACN,QAAQ,CAAC;oBACb;gBACJ;;YAEA,8BAA8B;YAC9B,MAAM,QAAQ,WAAW,iBAAiB;YAE1C;qCAAO;oBACH,aAAa;oBACb,IAAI,aAAa,OAAO,EAAE;wBACtB,aAAa,OAAO,CAAC,IAAI;oBAC7B;gBACJ;;QACJ;4BAAG;QAAC;QAAO;QAAO;KAAO;IACzB,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;YAAkB,KAAK;sBAEjC;mBAAI;mBAAU;aAAM,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;oBAAgB,WAAU;8BACvB,cAAA,6LAAC;wBAAI,WAAU;kCACV;;;;;;mBAFC;;;;;;;;;;;;;;;AAS9B;GAlEM;KAAA;uCAoES", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/FeaturesShowcase.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/FeaturesShowcase.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst FeaturesShowcase = () => {\n    const containerRef = useRef(null);\n    const cardsRef = useRef([]);\n\n    useEffect(() => {\n        const cards = cardsRef.current;\n        \n        // Initial animation for cards\n        gsap.fromTo(cards, \n            {\n                opacity: 0,\n                y: 50,\n                scale: 0.9\n            },\n            {\n                opacity: 1,\n                y: 0,\n                scale: 1,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: \"power3.out\",\n                scrollTrigger: {\n                    trigger: containerRef.current,\n                    start: \"top 80%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            }\n        );\n\n        // Hover animations\n        cards.forEach((card, index) => {\n            if (card) {\n                card.addEventListener('mouseenter', () => {\n                    gsap.to(card, {\n                        scale: 1.05,\n                        y: -10,\n                        duration: 0.3,\n                        ease: \"power2.out\"\n                    });\n                });\n\n                card.addEventListener('mouseleave', () => {\n                    gsap.to(card, {\n                        scale: 1,\n                        y: 0,\n                        duration: 0.3,\n                        ease: \"power2.out\"\n                    });\n                });\n            }\n        });\n\n        return () => {\n            cards.forEach(card => {\n                if (card) {\n                    card.removeEventListener('mouseenter', () => {});\n                    card.removeEventListener('mouseleave', () => {});\n                }\n            });\n        };\n    }, []);\n\n    const features = [\n        {\n            id: 1,\n            title: \"AI Auto Edits\",\n            description: \"Capture naturally and have interactive voice assistants that work seamlessly. Enhance your content with AI-powered editing tools.\",\n            image: \"/api/placeholder/300/400\",\n            bgColor: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 2,\n            title: \"Personal dictionary\",\n            description: \"Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 3,\n            title: \"Different tones for each app\",\n            description: \"Adapt your communication style with different tones optimized for each application and context.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            textColor: \"#ffffff\"\n        },\n        {\n            id: 4,\n            title: \"100+ languages\",\n            description: \"Communicate globally with support for over 100 languages and seamless translation capabilities.\",\n            image: \"/api/placeholder/300/300\",\n            bgColor: \"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)\",\n            textColor: \"#ffffff\"\n        }\n    ];\n\n    return (\n        <section className=\"features-showcase\" ref={containerRef}>\n            <div className=\"features-container\">\n                <div className=\"features-header\">\n                    <h2 className=\"features-title\">Powerful Features</h2>\n                    <p className=\"features-subtitle\">\n                        Discover the advanced capabilities that make our platform unique\n                    </p>\n                </div>\n                \n                <div className=\"features-grid\">\n                    {features.map((feature, index) => (\n                        <div\n                            key={feature.id}\n                            className={`feature-card ${index === 0 ? 'feature-large' : ''}`}\n                            ref={el => cardsRef.current[index] = el}\n                            style={{ \n                                background: feature.bgColor,\n                                color: feature.textColor \n                            }}\n                        >\n                            <div className=\"feature-content\">\n                                <div className=\"feature-image\">\n                                    <div className=\"phone-mockup\">\n                                        {index === 0 && (\n                                            <div className=\"ai-interface\">\n                                                <div className=\"ai-suggestions\">\n                                                    <div className=\"suggestion-item\">✨ Enhance clarity</div>\n                                                    <div className=\"suggestion-item\">🎯 Improve tone</div>\n                                                    <div className=\"suggestion-item\">📝 Fix grammar</div>\n                                                </div>\n                                                <div className=\"ai-controls\">\n                                                    <button className=\"ai-btn\">Apply</button>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 1 && (\n                                            <div className=\"dictionary-interface\">\n                                                <div className=\"dict-header\">Your Dictionary</div>\n                                                <div className=\"dict-items\">\n                                                    <div className=\"dict-item\">Technical terms</div>\n                                                    <div className=\"dict-item\">Brand names</div>\n                                                    <div className=\"dict-item\">Custom phrases</div>\n                                                    <div className=\"dict-item\">Abbreviations</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 2 && (\n                                            <div className=\"tones-interface\">\n                                                <div className=\"app-selector\">\n                                                    <div className=\"app-icon\">📧</div>\n                                                    <div className=\"app-icon active\">💬</div>\n                                                    <div className=\"app-icon\">📱</div>\n                                                </div>\n                                                <div className=\"tone-options\">\n                                                    <div className=\"tone-btn\">Professional</div>\n                                                    <div className=\"tone-btn active\">Casual</div>\n                                                    <div className=\"tone-btn\">Friendly</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                        {index === 3 && (\n                                            <div className=\"languages-interface\">\n                                                <div className=\"lang-wheel\">\n                                                    <div className=\"lang-item\">EN</div>\n                                                    <div className=\"lang-item\">ES</div>\n                                                    <div className=\"lang-item\">FR</div>\n                                                    <div className=\"lang-item\">DE</div>\n                                                    <div className=\"lang-item\">ZH</div>\n                                                    <div className=\"lang-item\">JA</div>\n                                                    <div className=\"lang-center\">100+</div>\n                                                </div>\n                                            </div>\n                                        )}\n                                    </div>\n                                </div>\n                                \n                                <div className=\"feature-text\">\n                                    <h3 className=\"feature-title\">{feature.title}</h3>\n                                    <p className=\"feature-description\">{feature.description}</p>\n                                </div>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default FeaturesShowcase;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,gCAAgC;AAChC,wCAAmC;IAC/B,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACrC;AAEA,MAAM,mBAAmB;;IACrB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,MAAM,QAAQ,SAAS,OAAO;YAE9B,8BAA8B;YAC9B,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACR;gBACI,SAAS;gBACT,GAAG;gBACH,OAAO;YACX,GACA;gBACI,SAAS;gBACT,GAAG;gBACH,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACX,SAAS,aAAa,OAAO;oBAC7B,OAAO;oBACP,eAAe;gBACnB;YACJ;YAGJ,mBAAmB;YACnB,MAAM,OAAO;8CAAC,CAAC,MAAM;oBACjB,IAAI,MAAM;wBACN,KAAK,gBAAgB,CAAC;0DAAc;gCAChC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oCACV,OAAO;oCACP,GAAG,CAAC;oCACJ,UAAU;oCACV,MAAM;gCACV;4BACJ;;wBAEA,KAAK,gBAAgB,CAAC;0DAAc;gCAChC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oCACV,OAAO;oCACP,GAAG;oCACH,UAAU;oCACV,MAAM;gCACV;4BACJ;;oBACJ;gBACJ;;YAEA;8CAAO;oBACH,MAAM,OAAO;sDAAC,CAAA;4BACV,IAAI,MAAM;gCACN,KAAK,mBAAmB,CAAC;kEAAc,KAAO;;gCAC9C,KAAK,mBAAmB,CAAC;kEAAc,KAAO;;4BAClD;wBACJ;;gBACJ;;QACJ;qCAAG,EAAE;IAEL,MAAM,WAAW;QACb;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;YACT,WAAW;QACf;KACH;IAED,qBACI,6LAAC;QAAQ,WAAU;QAAoB,KAAK;kBACxC,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAAiB;;;;;;sCAC/B,6LAAC;4BAAE,WAAU;sCAAoB;;;;;;;;;;;;8BAKrC,6LAAC;oBAAI,WAAU;8BACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,6LAAC;4BAEG,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,kBAAkB,IAAI;4BAC/D,KAAK,CAAA,KAAM,SAAS,OAAO,CAAC,MAAM,GAAG;4BACrC,OAAO;gCACH,YAAY,QAAQ,OAAO;gCAC3B,OAAO,QAAQ,SAAS;4BAC5B;sCAEA,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;gDACV,UAAU,mBACP,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;;;;;;;sEAErC,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAO,WAAU;0EAAS;;;;;;;;;;;;;;;;;gDAItC,UAAU,mBACP,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,6LAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,6LAAC;oEAAI,WAAU;8EAAY;;;;;;8EAC3B,6LAAC;oEAAI,WAAU;8EAAY;;;;;;;;;;;;;;;;;;gDAItC,UAAU,mBACP,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAW;;;;;;8EAC1B,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAW;;;;;;;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAW;;;;;;8EAC1B,6LAAC;oEAAI,WAAU;8EAAkB;;;;;;8EACjC,6LAAC;oEAAI,WAAU;8EAAW;;;;;;;;;;;;;;;;;;gDAIrC,UAAU,mBACP,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAY;;;;;;0EAC3B,6LAAC;gEAAI,WAAU;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOjD,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAAiB,QAAQ,KAAK;;;;;;0DAC5C,6LAAC;gDAAE,WAAU;0DAAuB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2BAlE1D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AA2E3C;GAzLM;KAAA;uCA2LS", "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/FlowSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/FlowSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst FlowSection = () => {\n    const sectionRef = useRef(null);\n    const titleRef = useRef(null);\n    const tagsRef = useRef([]);\n    const contentRef = useRef(null);\n    const illustrationRef = useRef(null);\n\n    const professionTags = [\n        \"Accessibility\", \"Consultants\", \"Creators\", \"Customer Support\",\n        \"Designers\", \"HR\", \"Managers\", \"Publishers\", \"Ergonomics\",\n        \"Freelancers\", \"Government\", \"Healthcare\", \"Individuals\",\n        \"Journalists\", \"Lawyers\", \"Multilingual\", \"Product\", \"Sales\",\n        \"Slower Typists\", \"Students\", \"Teams\", \"Writers\"\n    ];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const title = titleRef.current;\n        const tags = tagsRef.current;\n        const content = contentRef.current;\n        const illustration = illustrationRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([title, content, illustration], { opacity: 0, y: 50 });\n        gsap.set(tags, { opacity: 0, scale: 0.8 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 80%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(title, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(tags, {\n            opacity: 1,\n            scale: 1,\n            duration: 0.6,\n            stagger: 0.05,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.4\")\n        .to([content, illustration], {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            stagger: 0.2,\n            ease: \"power3.out\"\n        }, \"-=0.3\");\n\n        // Floating animation for illustration\n        gsap.to(illustration, {\n            y: -10,\n            duration: 2,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"flow-section\" ref={sectionRef}>\n            <div className=\"flow-container\">\n                <div className=\"flow-content\">\n                    <div className=\"flow-left\">\n                        <h1 className=\"flow-title\" ref={titleRef}>\n                            Flow is made<br />for you\n                        </h1>\n                        \n                        <div className=\"profession-tags\">\n                            {professionTags.map((tag, index) => (\n                                <span \n                                    key={index}\n                                    className=\"profession-tag\"\n                                    ref={el => tagsRef.current[index] = el}\n                                >\n                                    {tag}\n                                </span>\n                            ))}\n                        </div>\n                    </div>\n\n                    <div className=\"flow-right\">\n                        <div className=\"flow-accessibility\" ref={contentRef}>\n                            <h2 className=\"accessibility-title\">Flow for Accessibility</h2>\n                            <p className=\"accessibility-description\">\n                                Your voice deserves a shortcut. Flow supports anyone who \n                                feels slowed down by a keyboard by turning speech into \n                                structured, polished text—quickly, reliably, naturally.\n                            </p>\n                            <button className=\"get-started-btn\">Get started</button>\n                        </div>\n\n                        <div className=\"flow-illustration\" ref={illustrationRef}>\n                            <div className=\"character-container\">\n                                <div className=\"character\">\n                                    <div className=\"character-head\">\n                                        <div className=\"character-face\">\n                                            <div className=\"eye left-eye\"></div>\n                                            <div className=\"eye right-eye\"></div>\n                                            <div className=\"mouth\"></div>\n                                        </div>\n                                        <div className=\"character-ears\">\n                                            <div className=\"ear left-ear\"></div>\n                                            <div className=\"ear right-ear\"></div>\n                                        </div>\n                                    </div>\n                                    <div className=\"character-body\">\n                                        <div className=\"character-arms\">\n                                            <div className=\"arm left-arm\"></div>\n                                            <div className=\"arm right-arm\"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                                <div className=\"speech-bubble\">\n                                    <div className=\"bubble-content\">\n                                        <div className=\"text-lines\">\n                                            <div className=\"text-line\"></div>\n                                            <div className=\"text-line\"></div>\n                                            <div className=\"text-line short\"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default FlowSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,gCAAgC;AAChC,wCAAmC;IAC/B,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACrC;AAEA,MAAM,cAAc;;IAChB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,iBAAiB;QACnB;QAAiB;QAAe;QAAY;QAC5C;QAAa;QAAM;QAAY;QAAc;QAC7C;QAAe;QAAc;QAAc;QAC3C;QAAe;QAAW;QAAgB;QAAW;QACrD;QAAkB;QAAY;QAAS;KAC1C;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,QAAQ,SAAS,OAAO;YAC9B,MAAM,OAAO,QAAQ,OAAO;YAC5B,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,eAAe,gBAAgB,OAAO;YAE5C,IAAI,CAAC,SAAS;YAEd,gBAAgB;YAChB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;gBAAC;gBAAO;gBAAS;aAAa,EAAE;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7D,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAExC,0CAA0C;YAC1C,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBACrB,eAAe;oBACX,SAAS;oBACT,OAAO;oBACP,eAAe;gBACnB;YACJ;YAEA,GAAG,EAAE,CAAC,OAAO;gBACT,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GACC,EAAE,CAAC,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,MAAM;YACV,GAAG,SACF,EAAE,CAAC;gBAAC;gBAAS;aAAa,EAAE;gBACzB,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,SAAS;gBACT,MAAM;YACV,GAAG;YAEH,sCAAsC;YACtC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,cAAc;gBAClB,GAAG,CAAC;gBACJ,UAAU;gBACV,MAAM;gBACN,MAAM;gBACN,QAAQ,CAAC;YACb;YAEA;yCAAO;oBACH,wIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO;iDAAC,CAAA,UAAW,QAAQ,IAAI;;gBAC1D;;QACJ;gCAAG,EAAE;IAEL,qBACI,6LAAC;QAAQ,WAAU;QAAe,KAAK;kBACnC,cAAA,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;gCAAa,KAAK;;oCAAU;kDAC1B,6LAAC;;;;;oCAAK;;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;0CACV,eAAe,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;wCAEG,WAAU;wCACV,KAAK,CAAA,KAAM,QAAQ,OAAO,CAAC,MAAM,GAAG;kDAEnC;uCAJI;;;;;;;;;;;;;;;;kCAUrB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;gCAAqB,KAAK;;kDACrC,6LAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;kDAKzC,6LAAC;wCAAO,WAAU;kDAAkB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;gCAAoB,KAAK;0CACpC,cAAA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEnB,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI3B,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D;GA9IM;KAAA;uCAgJS", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/ContentImageSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/ContentImageSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst ContentImageSection = () => {\n    const sectionRef = useRef(null);\n    const contentRef = useRef(null);\n    const imageRef = useRef(null);\n    const featuresRef = useRef([]);\n\n    const features = [\n        {\n            icon: \"🎯\",\n            title: \"Precision & Accuracy\",\n            description: \"Advanced AI algorithms ensure your voice is converted to text with remarkable precision and context awareness.\"\n        },\n        {\n            icon: \"⚡\",\n            title: \"Lightning Fast\",\n            description: \"Real-time processing means your thoughts become text instantly, keeping up with your natural speaking pace.\"\n        },\n        {\n            icon: \"🌐\",\n            title: \"Multi-Language Support\",\n            description: \"Communicate in over 100 languages with seamless translation and localization capabilities.\"\n        },\n        {\n            icon: \"🔒\",\n            title: \"Privacy First\",\n            description: \"Your data stays secure with end-to-end encryption and local processing options for sensitive content.\"\n        }\n    ];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const content = contentRef.current;\n        const image = imageRef.current;\n        const featureElements = featuresRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([content, image], { opacity: 0, y: 50 });\n        gsap.set(featureElements, { opacity: 0, x: -30 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(image, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(featureElements, {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            stagger: 0.1,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Parallax effect for image\n        gsap.to(image, {\n            y: -20,\n            scrollTrigger: {\n                trigger: section,\n                start: \"top bottom\",\n                end: \"bottom top\",\n                scrub: 1\n            }\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"content-image-section\" ref={sectionRef}>\n            <div className=\"content-image-container\">\n                <div className=\"content-side\" ref={contentRef}>\n                    <div className=\"content-header\">\n                        <span className=\"content-badge\">Why Choose Flow</span>\n                        <h2 className=\"content-title\">\n                            Transform Your Voice Into \n                            <span className=\"highlight\"> Powerful Content</span>\n                        </h2>\n                        <p className=\"content-description\">\n                            Experience the future of content creation with our advanced voice-to-text \n                            technology. Whether you're writing emails, creating documents, or brainstorming \n                            ideas, Flow makes it effortless and natural.\n                        </p>\n                    </div>\n\n                    <div className=\"features-grid\">\n                        {features.map((feature, index) => (\n                            <div \n                                key={index}\n                                className=\"feature-item\"\n                                ref={el => featuresRef.current[index] = el}\n                            >\n                                <div className=\"feature-icon\">{feature.icon}</div>\n                                <div className=\"feature-content\">\n                                    <h3 className=\"feature-title\">{feature.title}</h3>\n                                    <p className=\"feature-description\">{feature.description}</p>\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n\n                    <div className=\"content-actions\">\n                        <button className=\"primary-btn\">Start Free Trial</button>\n                        <button className=\"secondary-btn\">Watch Demo</button>\n                    </div>\n                </div>\n\n                <div className=\"image-side\" ref={imageRef}>\n                    <div className=\"image-container\">\n                        <div className=\"main-device\">\n                            <div className=\"device-screen\">\n                                <div className=\"screen-header\">\n                                    <div className=\"screen-dots\">\n                                        <span className=\"dot red\"></span>\n                                        <span className=\"dot yellow\"></span>\n                                        <span className=\"dot green\"></span>\n                                    </div>\n                                    <div className=\"screen-title\">Flow - Voice to Text</div>\n                                </div>\n                                <div className=\"screen-content\">\n                                    <div className=\"voice-wave\">\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                        <div className=\"wave-bar\"></div>\n                                    </div>\n                                    <div className=\"text-output\">\n                                        <div className=\"typing-text\">\n                                            <span>Hello, this is a demonstration of Flow's</span>\n                                            <span className=\"cursor\">|</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                        \n                        <div className=\"floating-elements\">\n                            <div className=\"floating-card card-1\">\n                                <div className=\"card-icon\">🎤</div>\n                                <div className=\"card-text\">Voice Input</div>\n                            </div>\n                            <div className=\"floating-card card-2\">\n                                <div className=\"card-icon\">📝</div>\n                                <div className=\"card-text\">Text Output</div>\n                            </div>\n                            <div className=\"floating-card card-3\">\n                                <div className=\"card-icon\">⚡</div>\n                                <div className=\"card-text\">Real-time</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default ContentImageSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,gCAAgC;AAChC,wCAAmC;IAC/B,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACrC;AAEA,MAAM,sBAAsB;;IACxB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE7B,MAAM,WAAW;QACb;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;QACA;YACI,MAAM;YACN,OAAO;YACP,aAAa;QACjB;KACH;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACN,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,QAAQ,SAAS,OAAO;YAC9B,MAAM,kBAAkB,YAAY,OAAO;YAE3C,IAAI,CAAC,SAAS;YAEd,gBAAgB;YAChB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;gBAAC;gBAAS;aAAM,EAAE;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC/C,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,iBAAiB;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAE/C,0CAA0C;YAC1C,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBACrB,eAAe;oBACX,SAAS;oBACT,OAAO;oBACP,eAAe;gBACnB;YACJ;YAEA,GAAG,EAAE,CAAC,SAAS;gBACX,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GACC,EAAE,CAAC,OAAO;gBACP,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,iBAAiB;gBACjB,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,SAAS;gBACT,MAAM;YACV,GAAG;YAEH,4BAA4B;YAC5B,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;gBACX,GAAG,CAAC;gBACJ,eAAe;oBACX,SAAS;oBACT,OAAO;oBACP,KAAK;oBACL,OAAO;gBACX;YACJ;YAEA;iDAAO;oBACH,wIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO;yDAAC,CAAA,UAAW,QAAQ,IAAI;;gBAC1D;;QACJ;wCAAG,EAAE;IAEL,qBACI,6LAAC;QAAQ,WAAU;QAAwB,KAAK;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;oBAAe,KAAK;;sCAC/B,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAG,WAAU;;wCAAgB;sDAE1B,6LAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;8CAEhC,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAOvC,6LAAC;4BAAI,WAAU;sCACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,6LAAC;oCAEG,WAAU;oCACV,KAAK,CAAA,KAAM,YAAY,OAAO,CAAC,MAAM,GAAG;;sDAExC,6LAAC;4CAAI,WAAU;sDAAgB,QAAQ,IAAI;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAG,WAAU;8DAAiB,QAAQ,KAAK;;;;;;8DAC5C,6LAAC;oDAAE,WAAU;8DAAuB,QAAQ,WAAW;;;;;;;;;;;;;mCAPtD;;;;;;;;;;sCAajB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAO,WAAU;8CAAc;;;;;;8CAChC,6LAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAI1C,6LAAC;oBAAI,WAAU;oBAAa,KAAK;8BAC7B,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;;;;;;;;;;;;8DAEpB,6LAAC;oDAAI,WAAU;8DAAe;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEnB,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;0EAAK;;;;;;0EACN,6LAAC;gEAAK,WAAU;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GAhLM;KAAA;uCAkLS", "debugId": null}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/AIAutoEditsSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/AIAutoEditsSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst AIAutoEditsSection = () => {\n    const sectionRef = useRef(null);\n    const phoneRef = useRef(null);\n    const contentRef = useRef(null);\n    const [currentSuggestion, setCurrentSuggestion] = useState(0);\n\n    const suggestions = [\n        \"✨ Enhance clarity and flow\",\n        \"🎯 Improve tone and style\", \n        \"📝 Fix grammar and spelling\",\n        \"🔄 Restructure for impact\"\n    ];\n\n    const originalText = \"Let's brainstorm a few ideas for our next campaign. We need something that will resonate with our target audience and drive engagement.\";\n    const improvedText = \"Let's collaborate on innovative campaign concepts that will deeply resonate with our target demographic and significantly boost engagement metrics.\";\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const phone = phoneRef.current;\n        const content = contentRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set([phone, content], { opacity: 0, y: 50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        tl.to(phone, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        })\n        .to(content, {\n            opacity: 1,\n            y: 0,\n            duration: 0.8,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animation for phone\n        gsap.to(phone, {\n            y: -8,\n            duration: 3,\n            ease: \"power2.inOut\",\n            yoyo: true,\n            repeat: -1\n        });\n\n        // Cycle through suggestions\n        const suggestionInterval = setInterval(() => {\n            setCurrentSuggestion(prev => (prev + 1) % suggestions.length);\n        }, 3000);\n\n        return () => {\n            clearInterval(suggestionInterval);\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"ai-auto-edits-section\" ref={sectionRef}>\n            <div className=\"ai-auto-edits-container\">\n                <div className=\"phone-mockup-container\" ref={phoneRef}>\n                    <div className=\"phone-device\">\n                        <div className=\"phone-screen\">\n                            <div className=\"screen-header\">\n                                <div className=\"status-bar\">\n                                    <span className=\"time\">9:41</span>\n                                    <div className=\"status-icons\">\n                                        <span className=\"signal\">📶</span>\n                                        <span className=\"wifi\">📶</span>\n                                        <span className=\"battery\">🔋</span>\n                                    </div>\n                                </div>\n                                <div className=\"app-header\">\n                                    <h3>Flow - AI Writing Assistant</h3>\n                                </div>\n                            </div>\n                            \n                            <div className=\"screen-content\">\n                                <div className=\"text-editor\">\n                                    <div className=\"original-text\">\n                                        <p>{originalText}</p>\n                                    </div>\n                                    \n                                    <div className=\"ai-suggestions\">\n                                        <div className=\"suggestion-header\">\n                                            <span className=\"ai-icon\">🤖</span>\n                                            <span>AI Suggestions</span>\n                                        </div>\n                                        \n                                        <div className=\"suggestion-items\">\n                                            {suggestions.map((suggestion, index) => (\n                                                <div \n                                                    key={index}\n                                                    className={`suggestion-item ${index === currentSuggestion ? 'active' : ''}`}\n                                                >\n                                                    {suggestion}\n                                                </div>\n                                            ))}\n                                        </div>\n                                        \n                                        <div className=\"action-buttons\">\n                                            <button className=\"apply-btn\">Apply All</button>\n                                            <button className=\"preview-btn\">Preview</button>\n                                        </div>\n                                    </div>\n                                    \n                                    <div className=\"improved-text\">\n                                        <div className=\"improved-header\">\n                                            <span className=\"check-icon\">✅</span>\n                                            <span>Improved Version</span>\n                                        </div>\n                                        <p className=\"typing-text\">{improvedText}</p>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"content-section\" ref={contentRef}>\n                    <div className=\"content-badge\">\n                        <span className=\"sparkle\">✨</span>\n                        AI-Powered\n                    </div>\n                    \n                    <h2 className=\"section-title\">AI Auto Edits</h2>\n                    \n                    <p className=\"section-description\">\n                        Capture naturally and have interactive voice assistants \n                        that work seamlessly. Enhance thoughts flowing into \n                        structured, polished text—quickly, reliably, naturally.\n                    </p>\n                    \n                    <div className=\"features-list\">\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">🎯</span>\n                            <span>Smart tone adjustment</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">📝</span>\n                            <span>Grammar & style fixes</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">🔄</span>\n                            <span>Content restructuring</span>\n                        </div>\n                        <div className=\"feature-item\">\n                            <span className=\"feature-icon\">✨</span>\n                            <span>Clarity enhancement</span>\n                        </div>\n                    </div>\n                    \n                    <button className=\"cta-button\">\n                        Try AI Auto Edits\n                        <span className=\"arrow\">→</span>\n                    </button>\n                </div>\n            </div>\n        </section>\n    );\n};\n\nexport default AIAutoEditsSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,gCAAgC;AAChC,wCAAmC;IAC/B,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACrC;AAEA,MAAM,qBAAqB;;IACvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,cAAc;QAChB;QACA;QACA;QACA;KACH;IAED,MAAM,eAAe;IACrB,MAAM,eAAe;IAErB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACN,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,QAAQ,SAAS,OAAO;YAC9B,MAAM,UAAU,WAAW,OAAO;YAElC,IAAI,CAAC,SAAS;YAEd,gBAAgB;YAChB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC;gBAAC;gBAAO;aAAQ,EAAE;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAE/C,0CAA0C;YAC1C,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBACrB,eAAe;oBACX,SAAS;oBACT,OAAO;oBACP,eAAe;gBACnB;YACJ;YAEA,GAAG,EAAE,CAAC,OAAO;gBACT,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GACC,EAAE,CAAC,SAAS;gBACT,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG;YAEH,+BAA+B;YAC/B,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;gBACX,GAAG,CAAC;gBACJ,UAAU;gBACV,MAAM;gBACN,MAAM;gBACN,QAAQ,CAAC;YACb;YAEA,4BAA4B;YAC5B,MAAM,qBAAqB;mEAAY;oBACnC;2EAAqB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,MAAM;;gBAChE;kEAAG;YAEH;gDAAO;oBACH,cAAc;oBACd,wIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO;wDAAC,CAAA,UAAW,QAAQ,IAAI;;gBAC1D;;QACJ;uCAAG,EAAE;IAEL,qBACI,6LAAC;QAAQ,WAAU;QAAwB,KAAK;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;oBAAyB,KAAK;8BACzC,cAAA,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAS;;;;;;sEACzB,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAGlC,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;0DAAG;;;;;;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC;8DAAG;;;;;;;;;;;0DAGR,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,6LAAC;0EAAK;;;;;;;;;;;;kEAGV,6LAAC;wDAAI,WAAU;kEACV,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC1B,6LAAC;gEAEG,WAAW,CAAC,gBAAgB,EAAE,UAAU,oBAAoB,WAAW,IAAI;0EAE1E;+DAHI;;;;;;;;;;kEAQjB,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAY;;;;;;0EAC9B,6LAAC;gEAAO,WAAU;0EAAc;;;;;;;;;;;;;;;;;;0DAIxC,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAK,WAAU;0EAAa;;;;;;0EAC7B,6LAAC;0EAAK;;;;;;;;;;;;kEAEV,6LAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpD,6LAAC;oBAAI,WAAU;oBAAkB,KAAK;;sCAClC,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAAU;;;;;;gCAAQ;;;;;;;sCAItC,6LAAC;4BAAG,WAAU;sCAAgB;;;;;;sCAE9B,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;sCAMnC,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,6LAAC;sDAAK;;;;;;;;;;;;8CAEV,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,6LAAC;sDAAK;;;;;;;;;;;;8CAEV,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,6LAAC;sDAAK;;;;;;;;;;;;8CAEV,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAId,6LAAC;4BAAO,WAAU;;gCAAa;8CAE3B,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD;GA3KM;KAAA;uCA6KS", "debugId": null}}, {"offset": {"line": 2444, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/MultiDeviceSection.jsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport \"@/Style/MultiDeviceSection.css\";\n\n// Register ScrollTrigger plugin\nif (typeof window !== \"undefined\") {\n    gsap.registerPlugin(ScrollTrigger);\n}\n\nconst MultiDeviceSection = () => {\n    const sectionRef = useRef(null);\n    const devicesRef = useRef([]);\n    const contentRef = useRef([]);\n\n    const languages = [\"EN\", \"ES\", \"FR\", \"DE\", \"ZH\", \"JA\", \"KO\", \"AR\", \"HI\", \"PT\", \"RU\", \"IT\"];\n\n    useEffect(() => {\n        const section = sectionRef.current;\n        const devices = devicesRef.current;\n        const contents = contentRef.current;\n\n        if (!section) return;\n\n        // Initial setup\n        gsap.set(devices, { opacity: 0, y: 100, rotation: 5 });\n        gsap.set(contents, { opacity: 0, x: -50 });\n\n        // Create timeline for entrance animations\n        const tl = gsap.timeline({\n            scrollTrigger: {\n                trigger: section,\n                start: \"top 70%\",\n                toggleActions: \"play none none reverse\"\n            }\n        });\n\n        // Animate devices in sequence\n        tl.to(devices[0], { // Dictionary device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        })\n        .to(contents[0], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[1], { // Tones device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[1], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[2], { // Languages device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[2], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\")\n        .to(devices[3], { // Desktop device\n            opacity: 1,\n            y: 0,\n            rotation: 0,\n            duration: 0.8,\n            ease: \"back.out(1.7)\"\n        }, \"-=0.2\")\n        .to(contents[3], {\n            opacity: 1,\n            x: 0,\n            duration: 0.6,\n            ease: \"power3.out\"\n        }, \"-=0.4\");\n\n        // Floating animations for devices\n        devices.forEach((device, index) => {\n            if (device) {\n                gsap.to(device, {\n                    y: -10,\n                    duration: 2 + index * 0.5,\n                    ease: \"power2.inOut\",\n                    yoyo: true,\n                    repeat: -1,\n                    delay: index * 0.3\n                });\n            }\n        });\n\n        return () => {\n            ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n        };\n    }, []);\n\n    return (\n        <section className=\"multi-device-section\" ref={sectionRef}>\n            <div className=\"multi-device-container\">\n                \n                {/* Dictionary Section */}\n                <div className=\"device-row\">\n                    <div className=\"device-mockup dictionary-device\" ref={el => devicesRef.current[0] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"screen-header\">\n                                <h3>Your Dictionary</h3>\n                                <span className=\"close-btn\">×</span>\n                            </div>\n                            <div className=\"dictionary-content\">\n                                <div className=\"dict-item\">Technical terms</div>\n                                <div className=\"dict-item\">Brand names</div>\n                                <div className=\"dict-item\">Custom phrases</div>\n                                <div className=\"dict-item\">Abbreviations</div>\n                                <div className=\"dict-item\">Industry jargon</div>\n                                <div className=\"dict-item\">Personal vocabulary</div>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"device-content\" ref={el => contentRef.current[0] = el}>\n                        <h3>Personal Dictionary</h3>\n                        <p>Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage across all your content.</p>\n                    </div>\n                </div>\n\n                {/* Tones Section */}\n                <div className=\"device-row reverse\">\n                    <div className=\"device-content\" ref={el => contentRef.current[1] = el}>\n                        <h3>Different tones for each app</h3>\n                        <p>Adapt your communication style with different tones optimized for each application and context, ensuring perfect messaging every time.</p>\n                    </div>\n                    <div className=\"device-mockup tones-device\" ref={el => devicesRef.current[1] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"app-selector\">\n                                <div className=\"app-icon email\">📧</div>\n                                <div className=\"app-icon chat active\">💬</div>\n                                <div className=\"app-icon social\">📱</div>\n                            </div>\n                            <div className=\"tones-content\">\n                                <div className=\"tone-option\">Professional</div>\n                                <div className=\"tone-option active\">Casual</div>\n                                <div className=\"tone-option\">Friendly</div>\n                                <div className=\"tone-option\">Formal</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Languages Section */}\n                <div className=\"device-row\">\n                    <div className=\"device-mockup languages-device\" ref={el => devicesRef.current[2] = el}>\n                        <div className=\"device-screen\">\n                            <div className=\"language-wheel\">\n                                {languages.map((lang, index) => (\n                                    <div \n                                        key={lang}\n                                        className=\"language-item\"\n                                        style={{\n                                            transform: `rotate(${index * 30}deg) translateY(-80px) rotate(-${index * 30}deg)`\n                                        }}\n                                    >\n                                        {lang}\n                                    </div>\n                                ))}\n                                <div className=\"wheel-center\">\n                                    <span className=\"lang-count\">100+</span>\n                                    <span className=\"lang-text\">Languages</span>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"device-content\" ref={el => contentRef.current[2] = el}>\n                        <h3>100+ languages</h3>\n                        <p>Communicate globally with support for over 100 languages and seamless translation capabilities, breaking down language barriers effortlessly.</p>\n                    </div>\n                </div>\n\n                {/* Desktop Section */}\n                <div className=\"device-row reverse\">\n                    <div className=\"device-content\" ref={el => contentRef.current[3] = el}>\n                        <h3>On-the-go or at your desk</h3>\n                        <p>Work from anywhere with seamless synchronization across all your devices. Whether mobile or desktop, your workflow stays uninterrupted and efficient.</p>\n                        <button className=\"cta-button\">Get started</button>\n                    </div>\n                    <div className=\"device-mockup desktop-device\" ref={el => devicesRef.current[3] = el}>\n                        <div className=\"desktop-screen\">\n                            <div className=\"desktop-header\">\n                                <div className=\"window-controls\">\n                                    <span className=\"control red\"></span>\n                                    <span className=\"control yellow\"></span>\n                                    <span className=\"control green\"></span>\n                                </div>\n                                <div className=\"window-title\">Flow Desktop</div>\n                            </div>\n                            <div className=\"desktop-content\">\n                                <div className=\"sidebar\">\n                                    <div className=\"sidebar-item active\">📝 Documents</div>\n                                    <div className=\"sidebar-item\">🎤 Voice Notes</div>\n                                    <div className=\"sidebar-item\">📊 Analytics</div>\n                                    <div className=\"sidebar-item\">⚙️ Settings</div>\n                                </div>\n                                <div className=\"main-content\">\n                                    <div className=\"document-header\">\n                                        <h4>📋 Crazy product ideas</h4>\n                                        <span className=\"doc-status\">Synced</span>\n                                    </div>\n                                    <div className=\"document-body\">\n                                        <div className=\"text-line\"></div>\n                                        <div className=\"text-line short\"></div>\n                                        <div className=\"text-line\"></div>\n                                        <div className=\"text-line medium\"></div>\n                                    </div>\n                                </div>\n                            </div>\n                            <div className=\"mobile-preview\">\n                                <div className=\"mobile-screen\">\n                                    <div className=\"mobile-header\">Flow Mobile</div>\n                                    <div className=\"voice-indicator\">\n                                        <div className=\"voice-wave\"></div>\n                                        <div className=\"voice-wave\"></div>\n                                        <div className=\"voice-wave\"></div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n            </div>\n        </section>\n    );\n};\n\nexport default MultiDeviceSection;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;;AAMA,gCAAgC;AAChC,wCAAmC;IAC/B,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACrC;AAEA,MAAM,qBAAqB;;IACvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC5B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE5B,MAAM,YAAY;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE1F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACN,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,WAAW,WAAW,OAAO;YAEnC,IAAI,CAAC,SAAS;YAEd,gBAAgB;YAChB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAK,UAAU;YAAE;YACpD,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAExC,0CAA0C;YAC1C,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBACrB,eAAe;oBACX,SAAS;oBACT,OAAO;oBACP,eAAe;gBACnB;YACJ;YAEA,8BAA8B;YAC9B,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;gBACd,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,UAAU;gBACV,MAAM;YACV,GACC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACb,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;gBACZ,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACb,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;gBACZ,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACb,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;gBACZ,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,UAAU;gBACV,MAAM;YACV,GAAG,SACF,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;gBACb,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;YACV,GAAG;YAEH,kCAAkC;YAClC,QAAQ,OAAO;gDAAC,CAAC,QAAQ;oBACrB,IAAI,QAAQ;wBACR,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,QAAQ;4BACZ,GAAG,CAAC;4BACJ,UAAU,IAAI,QAAQ;4BACtB,MAAM;4BACN,MAAM;4BACN,QAAQ,CAAC;4BACT,OAAO,QAAQ;wBACnB;oBACJ;gBACJ;;YAEA;gDAAO;oBACH,wIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO;wDAAC,CAAA,UAAW,QAAQ,IAAI;;gBAC1D;;QACJ;uCAAG,EAAE;IAEL,qBACI,6LAAC;QAAQ,WAAU;QAAuB,KAAK;kBAC3C,cAAA,6LAAC;YAAI,WAAU;;8BAGX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;4BAAkC,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAChF,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;0DAC3B,6LAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAKX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAE;;;;;;;;;;;;sCAEP,6LAAC;4BAAI,WAAU;4BAA6B,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC3E,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DAAiB;;;;;;0DAChC,6LAAC;gDAAI,WAAU;0DAAuB;;;;;;0DACtC,6LAAC;gDAAI,WAAU;0DAAkB;;;;;;;;;;;;kDAErC,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,6LAAC;gDAAI,WAAU;0DAAqB;;;;;;0DACpC,6LAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,6LAAC;gDAAI,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;4BAAiC,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC/E,cAAA,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;;wCACV,UAAU,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;gDAEG,WAAU;gDACV,OAAO;oDACH,WAAW,CAAC,OAAO,EAAE,QAAQ,GAAG,+BAA+B,EAAE,QAAQ,GAAG,IAAI,CAAC;gDACrF;0DAEC;+CANI;;;;;sDASb,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAK,WAAU;8DAAa;;;;;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAKX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;4BAAiB,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;;8CAC/D,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAO,WAAU;8CAAa;;;;;;;;;;;;sCAEnC,6LAAC;4BAAI,WAAU;4BAA+B,KAAK,CAAA,KAAM,WAAW,OAAO,CAAC,EAAE,GAAG;sCAC7E,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;;;;;;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;0DAAe;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,6LAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,6LAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,6LAAC;wDAAI,WAAU;kEAAe;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;gEAAK,WAAU;0EAAa;;;;;;;;;;;;kEAEjC,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAI3B,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD;GA1OM;KAAA;uCA4OS", "debugId": null}}]}