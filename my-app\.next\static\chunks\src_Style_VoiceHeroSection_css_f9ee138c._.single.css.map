{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/Style/VoiceHeroSection.css"], "sourcesContent": ["/* Voice Hero Section */\n.voice-hero-section {\n  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 30%, #fed7aa 70%, #fdba74 100%);\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 80px 20px;\n  position: relative;\n  overflow: hidden;\n  box-shadow: inset 0 0 100px rgba(245, 158, 11, 0.1);\n}\n\n.voice-hero-container {\n  max-width: 1200px;\n  width: 100%;\n  text-align: center;\n  position: relative;\n}\n\n.curved-text-container {\n  position: absolute;\n  top: -100px;\n  right: 10%;\n  width: 200px;\n  height: 200px;\n  z-index: 1;\n}\n\n.curved-svg {\n  width: 100%;\n  height: 100%;\n}\n\n.curved-text-path {\n  font-size: 12px;\n  font-weight: 400;\n  fill: #a3a3a3;\n  font-family: 'Arial', sans-serif;\n}\n\n.voice-indicator-container {\n  position: absolute;\n  top: 20%;\n  left: 15%;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  z-index: 2;\n}\n\n.voice-indicator {\n  background: #1f2937;\n  border-radius: 20px;\n  padding: 12px 16px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n}\n\n.voice-wave {\n  width: 3px;\n  height: 20px;\n  background: #3b82f6;\n  border-radius: 2px;\n  animation: wave 1s ease-in-out infinite;\n}\n\n.voice-wave:nth-child(1) { animation-delay: 0s; }\n.voice-wave:nth-child(2) { animation-delay: 0.1s; }\n.voice-wave:nth-child(3) { animation-delay: 0.2s; }\n.voice-wave:nth-child(4) { animation-delay: 0.3s; }\n.voice-wave:nth-child(5) { animation-delay: 0.4s; }\n.voice-wave:nth-child(6) { animation-delay: 0.5s; }\n.voice-wave:nth-child(7) { animation-delay: 0.6s; }\n.voice-wave:nth-child(8) { animation-delay: 0.7s; }\n\n@keyframes wave {\n  0%, 100% { \n    transform: scaleY(0.3);\n    opacity: 0.7;\n  }\n  50% { \n    transform: scaleY(1);\n    opacity: 1;\n  }\n}\n\n.voice-label {\n  background: white;\n  color: #1f2937;\n  padding: 8px 12px;\n  border-radius: 12px;\n  font-size: 14px;\n  font-weight: 500;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.voice-label::before {\n  content: '';\n  position: absolute;\n  left: -6px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 0;\n  height: 0;\n  border-top: 6px solid transparent;\n  border-bottom: 6px solid transparent;\n  border-right: 6px solid white;\n}\n\n.main-content {\n  z-index: 3;\n  position: relative;\n  margin-top: 60px;\n}\n\n.hero-title {\n  font-size: 4.5rem;\n  font-weight: 400;\n  color: #1f2937;\n  margin: 0 0 24px 0;\n  line-height: 1.1;\n  font-family: 'Georgia', serif;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  letter-spacing: -0.02em;\n}\n\n.highlight {\n  color: #1f2937;\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 500;\n  font-weight: 600;\n  position: relative;\n}\n\n.highlight::after {\n  content: '';\n  position: absolute;\n  bottom: 8px;\n  left: 0;\n  right: 0;\n  height: 8px;\n  background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);\n  border-radius: 4px;\n  z-index: -1;\n}\n\n.hero-subtitle {\n  font-size: 1.25rem;\n  color: #374151;\n  margin: 0 0 40px 0;\n  line-height: 1.6;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  font-weight: 400;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.cta-buttons {\n  display: flex;\n  justify-content: center;\n  gap: 16px;\n  margin-bottom: 24px;\n}\n\n.primary-btn {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n  border: none;\n  padding: 16px 32px;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);\n  position: relative;\n  overflow: hidden;\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.primary-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\n}\n\n.secondary-btn {\n  background: transparent;\n  color: #1f2937;\n  border: 2px solid #d1d5db;\n  padding: 16px 32px;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.secondary-btn:hover {\n  border-color: #3b82f6;\n  color: #3b82f6;\n  transform: translateY(-2px);\n}\n\n.availability {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .curved-text-container {\n    top: -80px;\n    right: 5%;\n    width: 160px;\n    height: 160px;\n  }\n  \n  .voice-indicator-container {\n    top: 25%;\n    left: 10%;\n  }\n  \n  .hero-title {\n    font-size: 3.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .voice-hero-section {\n    padding: 60px 20px;\n  }\n  \n  .curved-text-container {\n    display: none;\n  }\n  \n  .voice-indicator-container {\n    position: static;\n    justify-content: center;\n    margin-bottom: 40px;\n  }\n  \n  .main-content {\n    margin-top: 0;\n  }\n  \n  .hero-title {\n    font-size: 2.5rem;\n  }\n  \n  .hero-subtitle {\n    font-size: 1.125rem;\n  }\n  \n  .cta-buttons {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .primary-btn,\n  .secondary-btn {\n    width: 100%;\n    max-width: 280px;\n  }\n}\n\n@media (max-width: 480px) {\n  .voice-hero-section {\n    padding: 40px 15px;\n  }\n  \n  .hero-title {\n    font-size: 2rem;\n  }\n  \n  .hero-subtitle {\n    font-size: 1rem;\n  }\n  \n  .voice-indicator {\n    padding: 10px 14px;\n  }\n  \n  .voice-wave {\n    width: 2px;\n    height: 16px;\n  }\n  \n  .voice-label {\n    font-size: 12px;\n    padding: 6px 10px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;;;;;AAYA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AAmBA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAOA;EACE;;;;;;;EAOA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA"}}]}