/* [project]/src/Style/Marquee.css [app-client] (css) */
.marquee-container {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  align-items: center;
  width: 100%;
  min-height: 100px;
  padding: 20px 0;
  font-family: Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  position: relative;
  overflow: hidden;
}

.marquee-container:before {
  content: "";
  pointer-events: none;
  z-index: 2;
  background: linear-gradient(90deg, #00000026 0%, #0000 15% 85%, #00000026 100%);
  position: absolute;
  inset: 0;
}

.marquee-wrapper {
  white-space: nowrap;
  will-change: transform;
  align-items: center;
  display: flex;
}

.marquee-item {
  backdrop-filter: blur(10px);
  border: 1px solid #fff3;
  border-radius: 12px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  min-width: fit-content;
  margin-right: 20px;
  display: inline-flex;
  box-shadow: 0 4px 15px #0000001a;
}

.marquee-content {
  color: #fff;
  text-shadow: 0 2px 4px #0000004d;
  white-space: nowrap;
  letter-spacing: .5px;
  user-select: none;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
}

@media (width <= 1024px) {
  .marquee-content {
    padding: 11px 18px;
    font-size: 15px;
  }

  .marquee-item {
    margin-right: 18px;
  }
}

@media (width <= 768px) {
  .marquee-container {
    min-height: 80px;
    padding: 15px 0;
  }

  .marquee-content {
    padding: 10px 16px;
    font-size: 14px;
  }

  .marquee-item {
    margin-right: 15px;
  }
}

@media (width <= 480px) {
  .marquee-container {
    min-height: 70px;
    padding: 12px 0;
  }

  .marquee-content {
    letter-spacing: .3px;
    padding: 8px 14px;
    font-size: 13px;
  }

  .marquee-item {
    margin-right: 12px;
  }
}

@media (width <= 320px) {
  .marquee-container {
    min-height: 60px;
    padding: 10px 0;
  }

  .marquee-content {
    padding: 6px 12px;
    font-size: 12px;
  }

  .marquee-item {
    margin-right: 10px;
  }
}

.MarqueeBox {
  color: #fff;
  text-align: center;
  background: #111;
  justify-content: center;
  align-items: center;
  height: 20vh;
  font-family: system-ui;
  display: flex;
}

.carousel {
  background: #00f;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
}

.box {
  color: #000;
  cursor: pointer;
  background: green;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  font-size: 121px;
  display: flex;
  position: relative;
}

.test {
  padding: 20px;
}

.test-2 {
  padding: 20px 10px;
}


/* [project]/src/Style/FeaturesShowcase.css [app-client] (css) */
.features-showcase {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 80px 20px;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-header {
  text-align: center;
  margin-bottom: 60px;
}

.features-title {
  color: #2d3748;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-bottom: 16px;
  font-size: 3rem;
  font-weight: 700;
}

.features-subtitle {
  color: #718096;
  max-width: 600px;
  margin: 0 auto;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  align-items: start;
  gap: 30px;
  display: grid;
}

.feature-card {
  backdrop-filter: blur(10px);
  border: 1px solid #fff3;
  border-radius: 20px;
  padding: 30px;
  transition: all .3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px #0000001a;
}

.feature-large {
  grid-column: span 2;
  min-height: 400px;
}

.feature-content {
  flex-direction: column;
  gap: 20px;
  height: 100%;
  display: flex;
}

.feature-image {
  flex: 1;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  display: flex;
}

.phone-mockup {
  background: #000c;
  border: 3px solid #ffffff4d;
  border-radius: 25px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 300px;
  padding: 20px;
  display: flex;
  position: relative;
}

.ai-interface {
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  display: flex;
}

.ai-suggestions {
  flex-direction: column;
  gap: 10px;
  display: flex;
}

.suggestion-item {
  color: #fff;
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
}

.ai-controls {
  justify-content: center;
  display: flex;
}

.ai-btn {
  color: #fff;
  cursor: pointer;
  background: #4facfe;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-weight: 600;
  transition: all .3s;
}

.ai-btn:hover {
  background: #3d8bfe;
  transform: scale(1.05);
}

.dictionary-interface {
  flex-direction: column;
  gap: 15px;
  width: 100%;
  height: 100%;
  display: flex;
}

.dict-header {
  color: #4facfe;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
}

.dict-items {
  flex-direction: column;
  gap: 8px;
  display: flex;
}

.dict-item {
  color: #fff;
  text-align: center;
  background: #4facfe33;
  border: 1px solid #4facfe4d;
  border-radius: 15px;
  padding: 8px 12px;
  font-size: 11px;
}

.tones-interface {
  flex-direction: column;
  justify-content: center;
  gap: 20px;
  width: 100%;
  height: 100%;
  display: flex;
}

.app-selector {
  justify-content: center;
  gap: 15px;
  display: flex;
}

.app-icon {
  cursor: pointer;
  background: #ffffff1a;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 18px;
  transition: all .3s;
  display: flex;
}

.app-icon.active {
  background: #4facfe4d;
  border: 2px solid #4facfe;
}

.tone-options {
  flex-direction: column;
  gap: 8px;
  display: flex;
}

.tone-btn {
  color: #fff;
  text-align: center;
  cursor: pointer;
  background: #ffffff1a;
  border-radius: 12px;
  padding: 8px 12px;
  font-size: 11px;
  transition: all .3s;
}

.tone-btn.active {
  background: #4facfe4d;
  border: 1px solid #4facfe;
}

.languages-interface {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.lang-wheel {
  border: 2px solid #ffffff4d;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  position: relative;
}

.lang-item {
  color: #fff;
  background: #43e97b4d;
  border: 1px solid #43e97b80;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  position: absolute;
}

.lang-item:first-child {
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
}

.lang-item:nth-child(2) {
  top: 15px;
  right: -15px;
}

.lang-item:nth-child(3) {
  bottom: 15px;
  right: -15px;
}

.lang-item:nth-child(4) {
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
}

.lang-item:nth-child(5) {
  bottom: 15px;
  left: -15px;
}

.lang-item:nth-child(6) {
  top: 15px;
  left: -15px;
}

.lang-center {
  color: #fff;
  background: #43e97b80;
  border: 2px solid #43e97bb3;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.feature-text {
  text-align: center;
}

.feature-title {
  text-shadow: 0 2px 4px #0000001a;
  margin-bottom: 12px;
  font-size: 1.5rem;
  font-weight: 700;
}

.feature-description {
  opacity: .9;
  text-shadow: 0 1px 2px #0000001a;
  font-size: 1rem;
  line-height: 1.6;
}

@media (width <= 768px) {
  .features-showcase {
    padding: 60px 15px;
  }

  .features-title {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-large {
    grid-column: span 1;
  }

  .feature-card {
    padding: 20px;
  }

  .phone-mockup {
    width: 150px;
    height: 220px;
    padding: 15px;
  }
}

@media (width <= 480px) {
  .features-title {
    font-size: 2rem;
  }

  .features-subtitle {
    font-size: 1rem;
  }

  .phone-mockup {
    width: 120px;
    height: 180px;
    padding: 10px;
  }
}


/* [project]/src/Style/FlowSection.css [app-client] (css) */
.flow-section {
  color: #fff;
  background: #2a2a2a;
  align-items: center;
  min-height: 100vh;
  padding: 100px 20px;
  display: flex;
}

.flow-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.flow-content {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 60px;
  max-width: 100%;
  display: grid;
}

.flow-left {
  flex-direction: column;
  gap: 40px;
  display: flex;
}

.flow-title {
  word-wrap: break-word;
  max-width: 100%;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.profession-tags {
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  max-width: 100%;
  display: flex;
}

.profession-tag {
  color: #fff;
  cursor: pointer;
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 25px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.profession-tag:hover {
  background: #fff3;
  border-color: #fff6;
  transform: translateY(-2px);
}

.flow-right {
  flex-direction: column;
  align-items: flex-end;
  gap: 40px;
  display: flex;
}

.flow-accessibility {
  text-align: left;
  max-width: 400px;
}

.accessibility-title {
  color: #fff;
  margin-bottom: 16px;
  font-size: 2rem;
  font-weight: 600;
}

.accessibility-description {
  color: #fffc;
  margin-bottom: 24px;
  font-size: 1.1rem;
  line-height: 1.6;
}

.get-started-btn {
  color: #fff;
  cursor: pointer;
  background: #6366f1;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.get-started-btn:hover {
  background: #5855eb;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #6366f14d;
}

.flow-illustration {
  width: 300px;
  height: 300px;
  position: relative;
}

.character-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.character {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.character-head {
  background: #ff9ff3;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  margin: 0 auto 10px;
  position: relative;
}

.character-face {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.eye {
  background: #2a2a2a;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  position: absolute;
}

.left-eye {
  top: -2px;
  left: -12px;
}

.right-eye {
  top: -2px;
  right: -12px;
}

.mouth {
  border: 2px solid #2a2a2a;
  border-top: none;
  border-radius: 0 0 12px 12px;
  width: 12px;
  height: 6px;
  position: absolute;
  top: 8px;
  left: -6px;
}

.character-ears {
  width: 100%;
  position: absolute;
  top: 10px;
}

.ear {
  background: #ff9ff3;
  border-radius: 50%;
  width: 20px;
  height: 25px;
  position: absolute;
}

.left-ear {
  left: -10px;
  transform: rotate(-20deg);
}

.right-ear {
  right: -10px;
  transform: rotate(20deg);
}

.character-body {
  background: #ff9ff3;
  border-radius: 30px;
  width: 60px;
  height: 80px;
  margin: 0 auto;
  position: relative;
}

.character-arms {
  width: 100%;
  position: absolute;
  top: 20px;
}

.arm {
  background: #ff9ff3;
  border-radius: 4px;
  width: 30px;
  height: 8px;
  position: absolute;
}

.left-arm {
  left: -25px;
  transform: rotate(-30deg);
}

.right-arm {
  right: -25px;
  transform: rotate(30deg);
}

.speech-bubble {
  background: #fff;
  border-radius: 15px;
  width: 120px;
  height: 80px;
  padding: 15px;
  position: absolute;
  top: -20px;
  right: -80px;
  box-shadow: 0 4px 20px #0000001a;
}

.speech-bubble:before {
  content: "";
  border: 10px solid #0000;
  border-left-width: 0;
  border-right-color: #fff;
  width: 0;
  height: 0;
  position: absolute;
  top: 30px;
  left: -10px;
}

.bubble-content {
  flex-direction: column;
  justify-content: center;
  height: 100%;
  display: flex;
}

.text-lines {
  flex-direction: column;
  gap: 6px;
  display: flex;
}

.text-line {
  background: #ff9500;
  border-radius: 2px;
  height: 3px;
}

.text-line.short {
  width: 60%;
}

@media (width <= 1024px) {
  .flow-content {
    gap: 60px;
  }

  .flow-title {
    font-size: 3.5rem;
  }

  .flow-illustration {
    width: 250px;
    height: 250px;
  }
}

@media (width <= 768px) {
  .flow-section {
    padding: 80px 20px;
  }

  .flow-content {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .flow-title {
    font-size: 3rem;
  }

  .flow-right {
    align-items: center;
  }

  .flow-accessibility {
    text-align: center;
  }

  .profession-tags {
    justify-content: center;
  }

  .flow-illustration {
    width: 200px;
    height: 200px;
  }

  .speech-bubble {
    width: 100px;
    height: 60px;
    padding: 10px;
    right: -60px;
  }
}

@media (width <= 480px) {
  .flow-section {
    padding: 60px 15px;
  }

  .flow-title {
    font-size: 2.5rem;
  }

  .accessibility-title {
    font-size: 1.5rem;
  }

  .accessibility-description {
    font-size: 1rem;
  }

  .profession-tag {
    padding: 6px 12px;
    font-size: 12px;
  }

  .flow-illustration {
    width: 180px;
    height: 180px;
  }

  .character-head {
    width: 60px;
    height: 60px;
  }

  .character-body {
    width: 45px;
    height: 60px;
  }
}


/* [project]/src/Style/ContentImageSection.css [app-client] (css) */
.content-image-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
}

.content-image-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
}

.content-side {
  flex-direction: column;
  gap: 40px;
  display: flex;
}

.content-header {
  flex-direction: column;
  gap: 20px;
  display: flex;
}

.content-badge {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .5px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  width: fit-content;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
}

.content-title {
  color: #1a202c;
  margin: 0;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.content-description {
  color: #4a5568;
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-grid {
  grid-template-columns: 1fr;
  gap: 24px;
  display: grid;
}

.feature-item {
  background: #fff;
  border-radius: 12px;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 6px #0000000d;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px #0000001a;
}

.feature-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 2rem;
  display: flex;
}

.feature-content {
  flex: 1;
}

.feature-title {
  color: #1a202c;
  margin: 0 0 8px;
  font-size: 1.2rem;
  font-weight: 600;
}

.feature-description {
  color: #4a5568;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.content-actions {
  flex-wrap: wrap;
  gap: 16px;
  display: flex;
}

.primary-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 14px 28px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 4px 15px #667eea4d;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #667eea66;
}

.secondary-btn {
  color: #667eea;
  cursor: pointer;
  background: none;
  border: 2px solid #667eea;
  border-radius: 8px;
  padding: 12px 26px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.secondary-btn:hover {
  color: #fff;
  background: #667eea;
  transform: translateY(-2px);
}

.image-side {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.image-container {
  width: 100%;
  max-width: 500px;
  position: relative;
}

.main-device {
  z-index: 2;
  background: #1a202c;
  border-radius: 20px;
  padding: 20px;
  position: relative;
  box-shadow: 0 20px 40px #0003;
}

.device-screen {
  background: #2d3748;
  border-radius: 12px;
  overflow: hidden;
}

.screen-header {
  background: #4a5568;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  display: flex;
}

.screen-dots {
  gap: 6px;
  display: flex;
}

.dot {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.dot.red {
  background: #f56565;
}

.dot.yellow {
  background: #ed8936;
}

.dot.green {
  background: #48bb78;
}

.screen-title {
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.screen-content {
  flex-direction: column;
  align-items: center;
  gap: 30px;
  padding: 30px 20px;
  display: flex;
}

.voice-wave {
  justify-content: center;
  align-items: center;
  gap: 4px;
  display: flex;
}

.wave-bar {
  background: #667eea;
  border-radius: 2px;
  width: 4px;
  animation: 1.5s ease-in-out infinite wave;
}

.wave-bar:first-child {
  height: 20px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 35px;
  animation-delay: .1s;
}

.wave-bar:nth-child(3) {
  height: 25px;
  animation-delay: .2s;
}

.wave-bar:nth-child(4) {
  height: 40px;
  animation-delay: .3s;
}

.wave-bar:nth-child(5) {
  height: 30px;
  animation-delay: .4s;
}

.wave-bar:nth-child(6) {
  height: 35px;
  animation-delay: .5s;
}

.wave-bar:nth-child(7) {
  height: 20px;
  animation-delay: .6s;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(.3);
  }
}

.text-output {
  background: #4a5568;
  border-radius: 8px;
  align-items: center;
  width: 100%;
  min-height: 60px;
  padding: 16px;
  display: flex;
}

.typing-text {
  color: #e2e8f0;
  font-family: Courier New, monospace;
  font-size: 14px;
}

.cursor {
  color: #667eea;
  animation: 1s infinite blink;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }

  51%, 100% {
    opacity: 0;
  }
}

.floating-elements {
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.floating-card {
  color: #1a202c;
  background: #fff;
  border-radius: 12px;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  animation: 3s ease-in-out infinite float;
  display: flex;
  position: absolute;
  box-shadow: 0 8px 25px #00000026;
}

.card-1 {
  animation-delay: 0s;
  top: 20%;
  left: -10%;
}

.card-2 {
  animation-delay: 1s;
  top: 60%;
  right: -15%;
}

.card-3 {
  animation-delay: 2s;
  bottom: 20%;
  left: -5%;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

.card-icon {
  font-size: 18px;
}

@media (width <= 1024px) {
  .content-image-container {
    gap: 60px;
  }

  .content-title {
    font-size: 3rem;
  }
}

@media (width <= 768px) {
  .content-image-section {
    padding: 80px 20px;
  }

  .content-image-container {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .content-title {
    text-align: center;
    font-size: 2.5rem;
  }

  .content-header {
    text-align: center;
  }

  .content-badge {
    align-self: center;
  }

  .content-actions {
    justify-content: center;
  }

  .floating-card {
    display: none;
  }
}

@media (width <= 480px) {
  .content-image-section {
    padding: 60px 15px;
  }

  .content-title {
    font-size: 2rem;
  }

  .content-description {
    font-size: 1rem;
  }

  .feature-item {
    padding: 16px;
  }

  .feature-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .main-device {
    padding: 15px;
  }

  .screen-content {
    padding: 20px 15px;
  }
}


/* [project]/src/Style/AIAutoEditsSection.css [app-client] (css) */
.ai-auto-edits-section {
  background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
}

.ai-auto-edits-container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
}

.phone-mockup-container {
  justify-content: center;
  align-items: center;
  display: flex;
}

.phone-device {
  background: linear-gradient(145deg, #1f2937, #374151);
  border-radius: 30px;
  width: 320px;
  height: 640px;
  padding: 8px;
  position: relative;
  box-shadow: 0 25px 50px #0000004d;
}

.phone-device:before {
  content: "";
  background: #6b7280;
  border-radius: 2px;
  width: 60px;
  height: 4px;
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
}

.phone-screen {
  background: #111827;
  border-radius: 22px;
  flex-direction: column;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.screen-header {
  background: #1f2937;
  padding: 12px 16px 8px;
}

.status-bar {
  color: #fff;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.status-icons {
  gap: 4px;
  display: flex;
}

.app-header h3 {
  color: #fff;
  text-align: center;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.screen-content {
  flex: 1;
  padding: 20px 16px;
  overflow-y: auto;
}

.text-editor {
  flex-direction: column;
  gap: 20px;
  height: 100%;
  display: flex;
}

.original-text {
  background: #374151;
  border-left: 4px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
}

.original-text p {
  color: #e5e7eb;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.ai-suggestions {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 12px;
  padding: 16px;
}

.suggestion-header {
  color: #60a5fa;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.suggestion-items {
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  display: flex;
}

.suggestion-item {
  color: #d1d5db;
  opacity: .6;
  background: #374151;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 13px;
  transition: all .3s;
}

.suggestion-item.active {
  color: #fff;
  opacity: 1;
  background: #3b82f6;
  transform: scale(1.02);
}

.action-buttons {
  gap: 8px;
  display: flex;
}

.apply-btn, .preview-btn {
  cursor: pointer;
  border: none;
  border-radius: 6px;
  flex: 1;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  transition: all .3s;
}

.apply-btn {
  color: #fff;
  background: #10b981;
}

.apply-btn:hover {
  background: #059669;
}

.preview-btn {
  color: #d1d5db;
  background: #374151;
}

.preview-btn:hover {
  background: #4b5563;
}

.improved-text {
  background: #065f46;
  border-left: 4px solid #10b981;
  border-radius: 12px;
  padding: 16px;
}

.improved-header {
  color: #6ee7b7;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.typing-text {
  color: #d1fae5;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  animation: 4s steps(40, end) infinite typewriter;
}

@keyframes typewriter {
  0% {
    width: 0;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 100%;
  }
}

.content-section {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.content-badge {
  color: #92400e;
  background: #fbbf2433;
  border: 1px solid #fbbf244d;
  border-radius: 20px;
  align-items: center;
  gap: 8px;
  width: fit-content;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.sparkle {
  animation: 2s ease-in-out infinite sparkle;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1)rotate(0);
  }

  50% {
    transform: scale(1.2)rotate(180deg);
  }
}

.section-title {
  color: #92400e;
  margin: 0;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.section-description {
  color: #78350f;
  max-width: 500px;
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.features-list {
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin: 20px 0;
  display: grid;
}

.feature-item {
  color: #92400e;
  backdrop-filter: blur(10px);
  background: #ffffff80;
  border: 1px solid #ffffff4d;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.feature-icon {
  font-size: 18px;
}

.cta-button {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  width: fit-content;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
  box-shadow: 0 8px 25px #f59e0b4d;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px #f59e0b66;
}

.arrow {
  transition: transform .3s;
}

.cta-button:hover .arrow {
  transform: translateX(4px);
}

@media (width <= 1024px) {
  .ai-auto-edits-container {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .phone-device {
    width: 280px;
    height: 560px;
  }
}

@media (width <= 768px) {
  .ai-auto-edits-section {
    padding: 80px 20px;
  }

  .ai-auto-edits-container {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .phone-device {
    width: 260px;
    height: 520px;
  }
}

@media (width <= 480px) {
  .ai-auto-edits-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .phone-device {
    width: 240px;
    height: 480px;
  }

  .screen-content {
    padding: 16px 12px;
  }
}


/* [project]/src/Style/MultiDeviceSection.css [app-client] (css) */
.multi-device-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  min-height: 200vh;
  padding: 120px 20px;
}

.multi-device-container {
  flex-direction: column;
  gap: 120px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
}

.device-row {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  display: grid;
}

.device-row.reverse {
  direction: rtl;
}

.device-row.reverse > * {
  direction: ltr;
}

.device-mockup {
  background: #1f2937;
  border-radius: 20px;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  box-shadow: 0 25px 50px #0000004d;
}

.device-screen {
  background: #111827;
  border-radius: 12px;
  min-height: 300px;
  overflow: hidden;
}

.screen-header {
  color: #fff;
  background: #374151;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  display: flex;
}

.screen-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  cursor: pointer;
  opacity: .7;
  font-size: 20px;
}

.device-content {
  flex-direction: column;
  gap: 20px;
  max-width: 500px;
  display: flex;
}

.device-content h3 {
  color: #92400e;
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.device-content p {
  color: #78350f;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.dictionary-content {
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  display: flex;
}

.dict-item {
  color: #60a5fa;
  background: #374151;
  border-left: 3px solid #3b82f6;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.dict-item:hover {
  background: #4b5563;
  transform: translateX(4px);
}

.app-selector {
  background: #374151;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  display: flex;
}

.app-icon {
  cursor: pointer;
  background: #4b5563;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  font-size: 20px;
  transition: all .3s;
  display: flex;
}

.app-icon.active {
  background: #3b82f6;
  transform: scale(1.1);
}

.tones-content {
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  display: flex;
}

.tone-option {
  color: #d1d5db;
  text-align: center;
  cursor: pointer;
  background: #374151;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.tone-option.active {
  color: #fff;
  background: #10b981;
  transform: scale(1.02);
}

.language-wheel {
  width: 200px;
  height: 200px;
  margin: 40px auto;
  position: relative;
}

.language-item {
  color: #fff;
  transform-origin: center;
  background: #3b82f6;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 12px;
  font-weight: 600;
  animation: 20s linear infinite rotate;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
}

@keyframes rotate {
  from {
    transform: rotate(0)translateY(-80px)rotate(0);
  }

  to {
    transform: rotate(360deg)translateY(-80px)rotate(-360deg);
  }
}

.wheel-center {
  background: #1f2937;
  border: 3px solid #3b82f6;
  border-radius: 50%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.lang-count {
  color: #60a5fa;
  font-size: 18px;
  font-weight: 700;
}

.lang-text {
  color: #9ca3af;
  font-size: 10px;
  font-weight: 500;
}

.desktop-device {
  max-width: 500px;
}

.desktop-screen {
  background: #f3f4f6;
  border-radius: 12px;
  min-height: 400px;
  position: relative;
  overflow: hidden;
}

.desktop-header {
  background: #e5e7eb;
  border-bottom: 1px solid #d1d5db;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  display: flex;
}

.window-controls {
  gap: 6px;
  display: flex;
}

.control {
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.control.red {
  background: #ef4444;
}

.control.yellow {
  background: #f59e0b;
}

.control.green {
  background: #10b981;
}

.window-title {
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

.desktop-content {
  height: 300px;
  display: flex;
}

.sidebar {
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  flex-direction: column;
  gap: 8px;
  width: 120px;
  padding: 16px 8px;
  display: flex;
}

.sidebar-item {
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  transition: all .3s;
}

.sidebar-item.active {
  color: #fff;
  background: #3b82f6;
}

.main-content {
  background: #fff;
  flex: 1;
  padding: 20px;
}

.document-header {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  display: flex;
}

.document-header h4 {
  color: #1f2937;
  margin: 0;
  font-size: 16px;
}

.doc-status {
  color: #10b981;
  background: #dcfce7;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
}

.document-body {
  flex-direction: column;
  gap: 12px;
  display: flex;
}

.text-line {
  background: #d1d5db;
  border-radius: 2px;
  height: 4px;
}

.text-line.short {
  width: 60%;
}

.text-line.medium {
  width: 80%;
}

.mobile-preview {
  background: #1f2937;
  border-radius: 12px;
  width: 80px;
  height: 120px;
  padding: 8px;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.mobile-screen {
  background: #111827;
  border-radius: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  height: 100%;
  display: flex;
}

.mobile-header {
  color: #fff;
  font-size: 8px;
  font-weight: 600;
}

.voice-indicator {
  align-items: center;
  gap: 2px;
  display: flex;
}

.voice-wave {
  background: #3b82f6;
  border-radius: 1px;
  width: 2px;
  animation: 1s ease-in-out infinite wave;
}

.voice-wave:first-child {
  height: 8px;
  animation-delay: 0s;
}

.voice-wave:nth-child(2) {
  height: 12px;
  animation-delay: .1s;
}

.voice-wave:nth-child(3) {
  height: 6px;
  animation-delay: .2s;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(.3);
  }
}

.cta-button {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 8px;
  width: fit-content;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 4px 15px #f59e0b4d;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #f59e0b66;
}

@media (width <= 1024px) {
  .multi-device-container {
    gap: 100px;
  }

  .device-row {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 2rem;
  }

  .device-mockup {
    max-width: 350px;
  }
}

@media (width <= 768px) {
  .multi-device-section {
    padding: 80px 20px;
  }

  .multi-device-container {
    gap: 80px;
  }

  .device-row {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .device-row.reverse {
    direction: ltr;
  }

  .device-content {
    align-items: center;
    max-width: none;
  }

  .device-content h3 {
    font-size: 1.8rem;
  }

  .device-mockup {
    max-width: 320px;
  }

  .language-wheel {
    width: 160px;
    height: 160px;
  }

  .language-item {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }

  .wheel-center {
    width: 60px;
    height: 60px;
  }

  .lang-count {
    font-size: 14px;
  }

  .lang-text {
    font-size: 8px;
  }
}

@media (width <= 480px) {
  .multi-device-section {
    padding: 60px 15px;
  }

  .multi-device-container {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 1.5rem;
  }

  .device-content p {
    font-size: 1rem;
  }

  .device-mockup {
    max-width: 280px;
    padding: 15px;
  }

  .device-screen {
    min-height: 250px;
  }

  .desktop-content {
    height: 250px;
  }

  .sidebar {
    width: 100px;
    padding: 12px 6px;
  }

  .sidebar-item {
    padding: 6px 8px;
    font-size: 10px;
  }

  .main-content {
    padding: 15px;
  }

  .mobile-preview {
    width: 60px;
    height: 90px;
    bottom: 15px;
    right: 15px;
  }

  .language-wheel {
    width: 140px;
    height: 140px;
  }

  .language-item {
    width: 28px;
    height: 28px;
    font-size: 9px;
  }
}


/*# sourceMappingURL=src_Style_812068de._.css.map*/