/* [project]/src/Style/SavingsCalculator.css [app-client] (css) */
.savings-calculator-section {
  color: #fff;
  background: linear-gradient(135deg, #064e3b 0%, #047857 100%);
  min-height: 100vh;
  padding: 120px 20px;
}

.savings-container {
  flex-direction: column;
  gap: 60px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
}

.content-section {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  display: grid;
}

.text-content {
  flex-direction: column;
  gap: 32px;
  display: flex;
}

.section-title {
  color: #fff;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.scenario-text {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.scenario-main {
  color: #d1fae5;
  margin: 0;
  font-size: 1.5rem;
  line-height: 1.4;
}

.scenario-main strong {
  color: #6ee7b7;
  font-weight: 700;
}

.scenario-detail {
  color: #a7f3d0;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.value-statement {
  color: #d1fae5;
  margin: 0;
  font-size: 1.25rem;
  line-height: 1.4;
}

.value-statement strong {
  color: #6ee7b7;
  font-weight: 700;
}

.input-controls {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.input-group {
  flex-direction: column;
  gap: 12px;
  display: flex;
}

.input-group label {
  color: #a7f3d0;
  font-size: 1rem;
  font-weight: 600;
}

.slider {
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  background: #065f46;
  border-radius: 4px;
  outline: none;
  width: 100%;
  height: 8px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  transition: all .3s;
  box-shadow: 0 2px 8px #10b9814d;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px #10b98166;
}

.slider::-moz-range-thumb {
  cursor: pointer;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  box-shadow: 0 2px 8px #10b9814d;
}

.slider-value {
  color: #6ee7b7;
  text-align: center;
  background: #10b98133;
  border-radius: 20px;
  align-self: center;
  width: fit-content;
  padding: 8px 16px;
  font-size: 1.125rem;
  font-weight: 700;
}

.illustration-section {
  justify-content: center;
  align-items: center;
  display: flex;
}

.character-illustration {
  justify-content: center;
  align-items: center;
  width: 300px;
  height: 300px;
  display: flex;
  position: relative;
}

.character {
  z-index: 2;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  display: flex;
}

.character-head {
  flex-direction: column;
  align-items: center;
  gap: 8px;
  display: flex;
  position: relative;
}

.face {
  font-size: 4rem;
  animation: 2s ease-in-out infinite bounce;
}

.hat {
  font-size: 2rem;
  animation: 3s ease-in-out infinite tilt;
  position: absolute;
  top: -20px;
}

.character-body {
  justify-content: center;
  display: flex;
}

.arms {
  gap: 40px;
  display: flex;
}

.arm {
  font-size: 2rem;
  animation: 1.5s ease-in-out infinite juggle;
}

.arm.left {
  animation-delay: 0s;
}

.arm.right {
  animation-delay: .75s;
}

.money-animation {
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
}

.money-icon {
  font-size: 2rem;
  animation: 3s ease-in-out infinite float;
  position: absolute;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes tilt {
  0%, 100% {
    transform: rotate(-5deg);
  }

  50% {
    transform: rotate(5deg);
  }
}

@keyframes juggle {
  0%, 100% {
    transform: translateY(0)rotate(0);
  }

  50% {
    transform: translateY(-15px)rotate(10deg);
  }
}

@keyframes float {
  0%, 100% {
    opacity: .8;
    transform: translateY(0)rotate(0);
  }

  50% {
    opacity: 1;
    transform: translateY(-20px)rotate(180deg);
  }
}

.calculator-section {
  justify-content: center;
  display: flex;
}

.calculator-card {
  backdrop-filter: blur(20px);
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 24px;
  width: 100%;
  max-width: 500px;
  padding: 40px;
  box-shadow: 0 8px 32px #0000004d;
}

.calculator-header {
  text-align: center;
  margin-bottom: 32px;
}

.calculator-header h3 {
  color: #a7f3d0;
  margin: 0 0 16px;
  font-size: 1.5rem;
  font-weight: 600;
}

.savings-amount {
  color: #6ee7b7;
  justify-content: center;
  align-items: baseline;
  gap: 8px;
  font-size: 3rem;
  font-weight: 700;
  display: flex;
}

.period {
  color: #a7f3d0;
  font-size: 1.5rem;
  font-weight: 500;
}

.breakdown {
  border-top: 1px solid #fff3;
  border-bottom: 1px solid #fff3;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px 0;
  display: flex;
}

.breakdown-item {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.breakdown-item .label {
  color: #d1fae5;
  font-size: 1rem;
}

.breakdown-item .value {
  color: #6ee7b7;
  font-size: 1rem;
  font-weight: 600;
}

.breakdown-item .value.negative {
  color: #fca5a5;
}

.calculator-footer {
  text-align: center;
}

.get-started-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  margin: 0 auto;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 15px #10b9814d;
}

.get-started-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #10b98166;
}

.arrow {
  transition: transform .3s;
}

.get-started-btn:hover .arrow {
  transform: translateX(4px);
}

@media (width <= 1024px) {
  .content-section {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .character-illustration {
    width: 250px;
    height: 250px;
  }

  .face {
    font-size: 3rem;
  }

  .calculator-card {
    padding: 32px;
  }
}

@media (width <= 768px) {
  .savings-calculator-section {
    padding: 80px 20px;
  }

  .content-section {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .scenario-main {
    font-size: 1.25rem;
  }

  .value-statement {
    font-size: 1.125rem;
  }

  .character-illustration {
    width: 200px;
    height: 200px;
  }

  .face {
    font-size: 2.5rem;
  }

  .hat, .arm, .money-icon {
    font-size: 1.5rem;
  }

  .calculator-card {
    margin: 0 20px;
    padding: 24px;
  }

  .savings-amount {
    font-size: 2.5rem;
  }
}

@media (width <= 480px) {
  .savings-calculator-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .scenario-main {
    font-size: 1.125rem;
  }

  .scenario-detail {
    font-size: .875rem;
  }

  .value-statement {
    font-size: 1rem;
  }

  .character-illustration {
    width: 150px;
    height: 150px;
  }

  .face {
    font-size: 2rem;
  }

  .calculator-card {
    margin: 0 10px;
    padding: 20px;
  }

  .savings-amount {
    font-size: 2rem;
  }

  .calculator-header h3 {
    font-size: 1.25rem;
  }

  .breakdown-item .label, .breakdown-item .value {
    font-size: .875rem;
  }

  .get-started-btn {
    padding: 14px 24px;
    font-size: 1rem;
  }
}

/*# sourceMappingURL=src_Style_SavingsCalculator_css_f9ee138c._.single.css.map*/