"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { <PERSON>a<PERSON><PERSON>le, FaAndroid, FaGithub, FaSlack, FaGoogle, FaSnapchat } from "react-icons/fa";

const icons = [
  FaApple,
  FaAndroid,
  FaGithub,
  FaSlack,
  FaGoogle,
  FaSnapchat,
];

const SnakeBelt = () => {
  const beltRef = useRef();

  useEffect(() => {
    const belt = beltRef.current;

    const animateBelt = () => {
      gsap.set(belt, { x: "-50%" }); // Start halfway off screen to the left
      gsap.to(belt, {
        x: "0%",
        ease: "linear",
        duration: 20,
        repeat: -1,
      });
    };

    animateBelt();

    // Vertical snake effect on each icon
    const icons = belt.querySelectorAll(".icon-item");
    icons.forEach((icon, i) => {
      gsap.to(icon, {
        y: 20,
        duration: 2,
        ease: "sine.inOut",
        yoyo: true,
        repeat: -1,
        delay: i * 0.2,
      });
    });
  }, []);

  // Duplicate icons for infinite scrolling illusion
  const duplicatedIcons = [...icons, ...icons];

  return (
    <div className="bg-black py-10 overflow-hidden">
      <div className="text-center text-white mb-6">
        <h2 className="text-3xl font-bold">Infinite Snake Scroll (←→)</h2>
        <p className="text-gray-400">Seamless loop with GSAP</p>
      </div>

      <div className="relative w-full h-[120px] overflow-hidden">
        <div
          ref={beltRef}
          className="flex w-max absolute gap-8"
          style={{ willChange: "transform" }}
        >
          {duplicatedIcons.map((Icon, i) => (
            <div key={i} className="icon-item text-4xl text-white">
              <Icon />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SnakeBelt;
