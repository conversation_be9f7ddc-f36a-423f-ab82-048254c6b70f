/* Multi Device Section */
.multi-device-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  padding: 120px 20px;
  min-height: 200vh;
}

.multi-device-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 120px;
}

.device-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.device-row.reverse {
  grid-template-columns: 1fr 1fr;
}

.device-row.reverse .device-mockup {
  order: 2;
}

.device-row.reverse .device-content {
  order: 1;
}

.device-mockup {
  background: #1f2937;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  margin: 0 auto;
}

.device-screen {
  background: #111827;
  border-radius: 12px;
  overflow: hidden;
  min-height: 300px;
}

.screen-header {
  background: #374151;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.screen-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  font-size: 20px;
  cursor: pointer;
  opacity: 0.7;
}

.device-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 500px;
}

.device-content h3 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #92400e;
  margin: 0;
  line-height: 1.2;
}

.device-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #78350f;
  margin: 0;
}

/* Dictionary Device */
.dictionary-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dict-item {
  background: #374151;
  color: #60a5fa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border-left: 3px solid #3b82f6;
  transition: all 0.3s ease;
}

.dict-item:hover {
  background: #4b5563;
  transform: translateX(4px);
}

/* Tones Device */
.app-selector {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  background: #374151;
}

.app-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: #4b5563;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.app-icon.active {
  background: #3b82f6;
  transform: scale(1.1);
}

.tones-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tone-option {
  background: #374151;
  color: #d1d5db;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tone-option.active {
  background: #10b981;
  color: white;
  transform: scale(1.02);
}

/* Languages Device */
.language-wheel {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 40px auto;
}

.language-item {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  transform-origin: center;
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg) translateY(-80px) rotate(0deg); }
  to { transform: rotate(360deg) translateY(-80px) rotate(-360deg); }
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: #1f2937;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 3px solid #3b82f6;
}

.lang-count {
  color: #60a5fa;
  font-size: 18px;
  font-weight: 700;
}

.lang-text {
  color: #9ca3af;
  font-size: 10px;
  font-weight: 500;
}

/* Desktop Device */
.desktop-device {
  max-width: 500px;
}

.desktop-screen {
  background: #f3f4f6;
  border-radius: 12px;
  overflow: hidden;
  min-height: 400px;
  position: relative;
}

.desktop-header {
  background: #e5e7eb;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #d1d5db;
}

.window-controls {
  display: flex;
  gap: 6px;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.window-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.desktop-content {
  display: flex;
  height: 300px;
}

.sidebar {
  width: 120px;
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sidebar-item {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-item.active {
  background: #3b82f6;
  color: white;
}

.main-content {
  flex: 1;
  padding: 20px;
  background: white;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.document-header h4 {
  margin: 0;
  font-size: 16px;
  color: #1f2937;
}

.doc-status {
  font-size: 12px;
  color: #10b981;
  background: #dcfce7;
  padding: 4px 8px;
  border-radius: 4px;
}

.document-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-line {
  height: 4px;
  background: #d1d5db;
  border-radius: 2px;
}

.text-line.short { width: 60%; }
.text-line.medium { width: 80%; }

.mobile-preview {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 80px;
  height: 120px;
  background: #1f2937;
  border-radius: 12px;
  padding: 8px;
}

.mobile-screen {
  background: #111827;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.mobile-header {
  color: white;
  font-size: 8px;
  font-weight: 600;
}

.voice-indicator {
  display: flex;
  gap: 2px;
  align-items: center;
}

.voice-wave {
  width: 2px;
  background: #3b82f6;
  border-radius: 1px;
  animation: wave 1s ease-in-out infinite;
}

.voice-wave:nth-child(1) { height: 8px; animation-delay: 0s; }
.voice-wave:nth-child(2) { height: 12px; animation-delay: 0.1s; }
.voice-wave:nth-child(3) { height: 6px; animation-delay: 0.2s; }

@keyframes wave {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(0.3); }
}

.cta-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .multi-device-container {
    gap: 100px;
  }

  .device-row {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 2rem;
  }

  .device-mockup {
    max-width: 350px;
  }
}

@media (max-width: 768px) {
  .multi-device-section {
    padding: 80px 20px;
  }

  .multi-device-container {
    gap: 80px;
  }

  .device-row {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .device-row.reverse .device-mockup,
  .device-row.reverse .device-content {
    order: unset;
  }

  .device-content {
    max-width: none;
    align-items: center;
  }

  .device-content h3 {
    font-size: 1.8rem;
  }

  .device-mockup {
    max-width: 320px;
  }

  .language-wheel {
    width: 160px;
    height: 160px;
  }

  .language-item {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }

  .wheel-center {
    width: 60px;
    height: 60px;
  }

  .lang-count {
    font-size: 14px;
  }

  .lang-text {
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .multi-device-section {
    padding: 60px 15px;
  }

  .multi-device-container {
    gap: 60px;
  }

  .device-content h3 {
    font-size: 1.5rem;
  }

  .device-content p {
    font-size: 1rem;
  }

  .device-mockup {
    max-width: 280px;
    padding: 15px;
  }

  .device-screen {
    min-height: 250px;
  }

  .desktop-content {
    height: 250px;
  }

  .sidebar {
    width: 100px;
    padding: 12px 6px;
  }

  .sidebar-item {
    font-size: 10px;
    padding: 6px 8px;
  }

  .main-content {
    padding: 15px;
  }

  .mobile-preview {
    width: 60px;
    height: 90px;
    bottom: 15px;
    right: 15px;
  }

  .language-wheel {
    width: 140px;
    height: 140px;
  }

  .language-item {
    width: 28px;
    height: 28px;
    font-size: 9px;
  }
}
