/* [project]/src/Style/HomeSection.css [app-client] (css) */
.home-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 50%, #fed7aa 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.home-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.content-grid {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  display: grid;
}

.text-content {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.cta-section {
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  margin-top: 32px;
  display: flex;
}

.primary-cta-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px #f59e0b4d;
}

.primary-cta-btn:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.primary-cta-btn:hover:before {
  left: 100%;
}

.primary-cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px #f59e0b66;
}

.cta-note {
  color: #6b7280;
  margin: 0;
  font-size: .875rem;
  font-style: italic;
}

.section-title {
  color: #000;
  text-shadow: 0 2px 4px #0000001a;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 500;
}

.section-description {
  color: #4b5563;
  text-shadow: 0 1px 2px #0000001a;
  max-width: 500px;
  margin: 0;
  font-size: 1.125rem;
  line-height: 1.6;
}

.feature-list {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.feature-item {
  color: #374151;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  display: flex;
}

.feature-icon {
  background: #fff;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-size: 1.25rem;
  display: flex;
  box-shadow: 0 2px 8px #0000001a;
}

.visual-content {
  flex-direction: column;
  gap: 32px;
  display: flex;
}

.stats-grid {
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  display: grid;
}

.stat-card {
  text-align: center;
  background: #fff;
  border: 1px solid #f59e0b1a;
  border-radius: 16px;
  padding: 24px;
  transition: all .3s;
  box-shadow: 0 4px 20px #0000001a;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px #00000026;
}

.stat-number {
  color: #1f2937;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-bottom: 8px;
  font-size: 2rem;
  font-weight: 700;
}

.stat-label {
  color: #6b7280;
  font-size: .875rem;
  font-weight: 500;
}

.demo-card {
  background: #fff;
  border: 1px solid #f59e0b1a;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 30px #0000001a;
}

.demo-header {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  display: flex;
}

.demo-title {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.demo-status {
  color: #10b981;
  align-items: center;
  gap: 8px;
  font-size: .875rem;
  font-weight: 500;
  display: flex;
}

.status-dot {
  background: #10b981;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  animation: 2s ease-in-out infinite pulse;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: .7;
    transform: scale(1.1);
  }
}

.demo-content {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.waveform {
  justify-content: center;
  align-items: center;
  gap: 4px;
  height: 60px;
  display: flex;
}

.wave-bar {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 2px;
  width: 4px;
  animation: 1.5s ease-in-out infinite wave;
}

.wave-bar:first-child {
  height: 20px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 35px;
  animation-delay: .1s;
}

.wave-bar:nth-child(3) {
  height: 50px;
  animation-delay: .2s;
}

.wave-bar:nth-child(4) {
  height: 30px;
  animation-delay: .3s;
}

.wave-bar:nth-child(5) {
  height: 45px;
  animation-delay: .4s;
}

.wave-bar:nth-child(6) {
  height: 25px;
  animation-delay: .5s;
}

.wave-bar:nth-child(7) {
  height: 40px;
  animation-delay: .6s;
}

.wave-bar:nth-child(8) {
  height: 20px;
  animation-delay: .7s;
}

@keyframes wave {
  0%, 100% {
    opacity: .7;
    transform: scaleY(.3);
  }

  50% {
    opacity: 1;
    transform: scaleY(1);
  }
}

.demo-text {
  color: #6b7280;
  text-align: center;
  background: #f9fafb;
  border-left: 4px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
  font-size: .95rem;
  font-style: italic;
}

@media (width <= 1024px) {
  .home-section {
    padding: 100px 20px;
  }

  .content-grid {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }
}

@media (width <= 768px) {
  .home-section {
    min-height: auto;
    padding: 80px 20px;
  }

  .content-grid {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-description {
    max-width: none;
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .feature-list {
    align-items: center;
  }

  .feature-item {
    justify-content: center;
  }
}

@media (width <= 480px) {
  .home-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: .95rem;
  }

  .demo-card, .stat-card {
    padding: 20px;
  }

  .stat-number {
    font-size: 1.75rem;
  }

  .waveform {
    height: 50px;
  }

  .wave-bar {
    width: 3px;
  }
}

/*# sourceMappingURL=src_Style_HomeSection_css_f9ee138c._.single.css.map*/