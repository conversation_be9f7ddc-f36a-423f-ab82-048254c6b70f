/* AI Auto Edits Section */
.ai-auto-edits-section {
  background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 50%, #fdba74 100%);
  padding: 120px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.ai-auto-edits-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 30% 70%, rgba(251, 191, 36, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.ai-auto-edits-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

/* Phone Mockup */
.phone-mockup-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-device {
  width: 320px;
  height: 640px;
  background: linear-gradient(145deg, #1f2937, #374151);
  border-radius: 30px;
  padding: 8px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  position: relative;
}

.phone-device::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #6b7280;
  border-radius: 2px;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #111827;
  border-radius: 22px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.screen-header {
  background: #1f2937;
  padding: 12px 16px 8px;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-icons {
  display: flex;
  gap: 4px;
}

.app-header h3 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  text-align: center;
}

.screen-content {
  flex: 1;
  padding: 20px 16px;
  overflow-y: auto;
}

.text-editor {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.original-text {
  background: #374151;
  border-radius: 12px;
  padding: 16px;
  border-left: 4px solid #f59e0b;
}

.original-text p {
  color: #e5e7eb;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.ai-suggestions {
  background: #1f2937;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #374151;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #60a5fa;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}

.suggestion-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.suggestion-item {
  background: #374151;
  color: #d1d5db;
  padding: 10px 12px;
  border-radius: 8px;
  font-size: 13px;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.suggestion-item.active {
  background: #3b82f6;
  color: white;
  opacity: 1;
  transform: scale(1.02);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.apply-btn, .preview-btn {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn {
  background: #10b981;
  color: white;
}

.apply-btn:hover {
  background: #059669;
}

.preview-btn {
  background: #374151;
  color: #d1d5db;
}

.preview-btn:hover {
  background: #4b5563;
}

.improved-text {
  background: #065f46;
  border-radius: 12px;
  padding: 16px;
  border-left: 4px solid #10b981;
}

.improved-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6ee7b7;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}

.typing-text {
  color: #d1fae5;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  animation: typewriter 4s steps(40) infinite;
}

@keyframes typewriter {
  0% { width: 0; }
  50% { width: 100%; }
  100% { width: 100%; }
}

/* Content Section */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.content-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(251, 191, 36, 0.2);
  color: #92400e;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  width: fit-content;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.2) rotate(180deg); }
}

.section-title {
  font-size: 3.5rem;
  font-weight: 400;
  color: #92400e;
  margin: 0;
  line-height: 1.1;
  font-family: 'Georgia', serif;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #78350f;
  margin: 0;
  max-width: 500px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.features-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin: 20px 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.5);
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.feature-icon {
  font-size: 18px;
}

.cta-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  width: fit-content;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);
}

.arrow {
  transition: transform 0.3s ease;
}

.cta-button:hover .arrow {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ai-auto-edits-container {
    gap: 60px;
  }
  
  .section-title {
    font-size: 3rem;
  }
  
  .phone-device {
    width: 280px;
    height: 560px;
  }
}

@media (max-width: 768px) {
  .ai-auto-edits-section {
    padding: 80px 20px;
  }
  
  .ai-auto-edits-container {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .features-list {
    grid-template-columns: 1fr;
  }
  
  .phone-device {
    width: 260px;
    height: 520px;
  }
}

@media (max-width: 480px) {
  .ai-auto-edits-section {
    padding: 60px 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .section-description {
    font-size: 1rem;
  }
  
  .phone-device {
    width: 240px;
    height: 480px;
  }
  
  .screen-content {
    padding: 16px 12px;
  }
}
