"use client";
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "@/Style/FeaturesShowcase.css";

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const FeaturesShowcase = () => {
    const containerRef = useRef(null);
    const cardsRef = useRef([]);

    useEffect(() => {
        const cards = cardsRef.current;
        
        // Initial animation for cards
        gsap.fromTo(cards, 
            {
                opacity: 0,
                y: 50,
                scale: 0.9
            },
            {
                opacity: 1,
                y: 0,
                scale: 1,
                duration: 0.8,
                stagger: 0.2,
                ease: "power3.out",
                scrollTrigger: {
                    trigger: containerRef.current,
                    start: "top 80%",
                    toggleActions: "play none none reverse"
                }
            }
        );

        // Hover animations
        cards.forEach((card, index) => {
            if (card) {
                card.addEventListener('mouseenter', () => {
                    gsap.to(card, {
                        scale: 1.05,
                        y: -10,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });

                card.addEventListener('mouseleave', () => {
                    gsap.to(card, {
                        scale: 1,
                        y: 0,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
            }
        });

        return () => {
            cards.forEach(card => {
                if (card) {
                    card.removeEventListener('mouseenter', () => {});
                    card.removeEventListener('mouseleave', () => {});
                }
            });
        };
    }, []);

    const features = [
        {
            id: 1,
            title: "AI Auto Edits",
            description: "Capture naturally and have interactive voice assistants that work seamlessly. Enhance your content with AI-powered editing tools.",
            image: "/api/placeholder/300/400",
            bgColor: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            textColor: "#ffffff"
        },
        {
            id: 2,
            title: "Personal dictionary",
            description: "Keep automatically and have your unique words and terms stored in your personal dictionary for consistent usage.",
            image: "/api/placeholder/300/300",
            bgColor: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
            textColor: "#ffffff"
        },
        {
            id: 3,
            title: "Different tones for each app",
            description: "Adapt your communication style with different tones optimized for each application and context.",
            image: "/api/placeholder/300/300",
            bgColor: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
            textColor: "#ffffff"
        },
        {
            id: 4,
            title: "100+ languages",
            description: "Communicate globally with support for over 100 languages and seamless translation capabilities.",
            image: "/api/placeholder/300/300",
            bgColor: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
            textColor: "#ffffff"
        }
    ];

    return (
        <section className="features-showcase" ref={containerRef}>
            <div className="features-container">
                <div className="features-header">
                    <h2 className="features-title">Powerful Features</h2>
                    <p className="features-subtitle">
                        Discover the advanced capabilities that make our platform unique
                    </p>
                </div>
                
                <div className="features-grid">
                    {features.map((feature, index) => (
                        <div
                            key={feature.id}
                            className={`feature-card ${index === 0 ? 'feature-large' : ''}`}
                            ref={el => cardsRef.current[index] = el}
                            style={{ 
                                background: feature.bgColor,
                                color: feature.textColor 
                            }}
                        >
                            <div className="feature-content">
                                <div className="feature-image">
                                    <div className="phone-mockup">
                                        {index === 0 && (
                                            <div className="ai-interface">
                                                <div className="ai-suggestions">
                                                    <div className="suggestion-item">✨ Enhance clarity</div>
                                                    <div className="suggestion-item">🎯 Improve tone</div>
                                                    <div className="suggestion-item">📝 Fix grammar</div>
                                                </div>
                                                <div className="ai-controls">
                                                    <button className="ai-btn">Apply</button>
                                                </div>
                                            </div>
                                        )}
                                        {index === 1 && (
                                            <div className="dictionary-interface">
                                                <div className="dict-header">Your Dictionary</div>
                                                <div className="dict-items">
                                                    <div className="dict-item">Technical terms</div>
                                                    <div className="dict-item">Brand names</div>
                                                    <div className="dict-item">Custom phrases</div>
                                                    <div className="dict-item">Abbreviations</div>
                                                </div>
                                            </div>
                                        )}
                                        {index === 2 && (
                                            <div className="tones-interface">
                                                <div className="app-selector">
                                                    <div className="app-icon">📧</div>
                                                    <div className="app-icon active">💬</div>
                                                    <div className="app-icon">📱</div>
                                                </div>
                                                <div className="tone-options">
                                                    <div className="tone-btn">Professional</div>
                                                    <div className="tone-btn active">Casual</div>
                                                    <div className="tone-btn">Friendly</div>
                                                </div>
                                            </div>
                                        )}
                                        {index === 3 && (
                                            <div className="languages-interface">
                                                <div className="lang-wheel">
                                                    <div className="lang-item">EN</div>
                                                    <div className="lang-item">ES</div>
                                                    <div className="lang-item">FR</div>
                                                    <div className="lang-item">DE</div>
                                                    <div className="lang-item">ZH</div>
                                                    <div className="lang-item">JA</div>
                                                    <div className="lang-center">100+</div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                
                                <div className="feature-text">
                                    <h3 className="feature-title">{feature.title}</h3>
                                    <p className="feature-description">{feature.description}</p>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default FeaturesShowcase;
