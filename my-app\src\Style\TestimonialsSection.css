/* Testimonials Section */
.testimonials-section {
  background: #1a1a1a;
  padding: 120px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
}

.section-title {
  font-size: 4rem;
  font-weight: 400;
  color: white;
  line-height: 1.1;
  margin: 0;
  font-family: 'Georgia', serif;
}

.decorative-lines {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  pointer-events: none;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, transparent 0%, #10b981 50%, transparent 100%);
  border-radius: 2px;
}

.line-1 {
  width: 60px;
  height: 2px;
  top: 20%;
  left: 10%;
  transform: rotate(25deg);
  animation: glow 3s ease-in-out infinite;
}

.line-2 {
  width: 40px;
  height: 2px;
  top: 30%;
  right: 15%;
  transform: rotate(-15deg);
  animation: glow 3s ease-in-out infinite 0.5s;
}

.line-3 {
  width: 50px;
  height: 2px;
  bottom: 25%;
  left: 20%;
  transform: rotate(45deg);
  animation: glow 3s ease-in-out infinite 1s;
}

.line-4 {
  width: 35px;
  height: 2px;
  bottom: 35%;
  right: 10%;
  transform: rotate(-30deg);
  animation: glow 3s ease-in-out infinite 1.5s;
}

.line-5 {
  width: 45px;
  height: 2px;
  top: 50%;
  left: 5%;
  transform: rotate(60deg);
  animation: glow 3s ease-in-out infinite 2s;
}

.line-6 {
  width: 55px;
  height: 2px;
  top: 60%;
  right: 5%;
  transform: rotate(-45deg);
  animation: glow 3s ease-in-out infinite 2.5s;
}

@keyframes glow {
  0%, 100% { 
    opacity: 0.3;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.1);
  }
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.testimonial-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.testimonial-card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.user-role {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.testimonial-content {
  margin-bottom: 16px;
}

.testimonial-text {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #374151;
  margin: 0;
  font-style: italic;
}

.rating {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 1rem;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.star.filled {
  opacity: 1;
}

/* Masonry-like positioning for visual interest */
.card-1 { grid-row: span 1; }
.card-2 { grid-row: span 1; }
.card-3 { grid-row: span 1; }
.card-4 { grid-row: span 1; }
.card-5 { grid-row: span 1; }
.card-6 { grid-row: span 1; }
.card-7 { grid-row: span 1; }

/* Responsive Design */
@media (max-width: 1024px) {
  .testimonials-section {
    padding: 100px 20px;
  }
  
  .section-title {
    font-size: 3.5rem;
  }
  
  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .decorative-lines {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .testimonials-section {
    padding: 80px 20px;
    min-height: auto;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .section-header {
    margin-bottom: 60px;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .testimonial-card {
    padding: 20px;
  }
  
  .decorative-lines {
    width: 200px;
    height: 200px;
  }
  
  .line-1, .line-2, .line-3, .line-4, .line-5, .line-6 {
    width: 30px;
  }
}

@media (max-width: 480px) {
  .testimonials-section {
    padding: 60px 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .testimonial-card {
    padding: 16px;
  }
  
  .card-header {
    gap: 12px;
  }
  
  .avatar {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .user-name {
    font-size: 0.9rem;
  }
  
  .user-role {
    font-size: 0.8rem;
  }
  
  .testimonial-text {
    font-size: 0.9rem;
  }
  
  .decorative-lines {
    display: none;
  }
}
