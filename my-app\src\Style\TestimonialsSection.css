/* Testimonials Section */
.testimonials-section {
  background: #1a1a1a;
  padding: 120px 20px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
}

.section-title {
  font-size: 4rem;
  font-weight: 400;
  color: white;
  line-height: 1.1;
  margin: 0 0 16px 0;
  font-family: 'Georgia', serif;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.section-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-align: center;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.carousel-container {
  position: relative;
  margin-top: 60px;
}

.carousel-wrapper {
  overflow: hidden;
  border-radius: 20px;
}

.carousel-track {
  display: flex;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 24px;
  will-change: transform;
}

.carousel-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10;
}

.carousel-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: all;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.carousel-btn:hover {
  background: white;
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

.carousel-btn:active {
  transform: scale(0.95);
}

.prev-btn {
  margin-left: -25px;
}

.next-btn {
  margin-right: -25px;
}

.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 40px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: #f59e0b;
  transform: scale(1.2);
}

.dot:hover {
  background: rgba(255, 255, 255, 0.6);
}

.testimonial-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 24px;
  height: auto;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f59e0b 0%, #10b981 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.testimonial-card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.user-role {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.user-company {
  font-size: 0.75rem;
  color: #9ca3af;
  margin: 0;
  font-style: italic;
}

.testimonial-content {
  margin-bottom: 16px;
}

.testimonial-text {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #374151;
  margin: 0;
  font-style: italic;
}

.rating {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 1rem;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.star.filled {
  opacity: 1;
}

/* Masonry-like positioning for visual interest */
.card-1 { grid-row: span 1; }
.card-2 { grid-row: span 1; }
.card-3 { grid-row: span 1; }
.card-4 { grid-row: span 1; }
.card-5 { grid-row: span 1; }
.card-6 { grid-row: span 1; }
.card-7 { grid-row: span 1; }

/* Responsive Design */
@media (max-width: 1024px) {
  .testimonials-section {
    padding: 100px 20px;
  }
  
  .section-title {
    font-size: 3.5rem;
  }
  
  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .decorative-lines {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 1024px) {
  .testimonial-card {
    padding: 28px;
    min-height: 280px;
  }

  .carousel-btn {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }
}

@media (max-width: 768px) {
  .testimonials-section {
    padding: 80px 20px;
    min-height: auto;
  }

  .testimonials-container {
    padding: 0 20px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .testimonial-card {
    padding: 24px;
    min-height: 250px;
    margin-right: 16px;
  }

  .carousel-controls {
    display: none;
  }

  .carousel-dots {
    margin-top: 24px;
  }

  .carousel-track {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .testimonial-card {
    padding: 20px;
    min-height: 220px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .testimonials-section {
    padding: 60px 15px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .testimonial-card {
    padding: 16px;
  }
  
  .card-header {
    gap: 12px;
  }
  
  .avatar {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .user-name {
    font-size: 0.9rem;
  }
  
  .user-role {
    font-size: 0.8rem;
  }
  
  .testimonial-text {
    font-size: 0.9rem;
  }
  
  .decorative-lines {
    display: none;
  }
}
