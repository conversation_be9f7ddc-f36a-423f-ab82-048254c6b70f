/* Footer Section */
.footer-section {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: white;
  padding: 80px 0 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.footer-content {
  display: flex;
  flex-direction: column;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  margin-bottom: 60px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.brand-logo {
  margin-bottom: 16px;
}

.brand-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-tagline {
  font-size: 1rem;
  color: #9ca3af;
  margin: 0;
  font-weight: 500;
}

.brand-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #d1d5db;
  margin: 0 0 32px 0;
  max-width: 400px;
}

.newsletter-signup {
  margin-bottom: 32px;
}

.newsletter-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
}

.newsletter-description {
  font-size: 0.875rem;
  color: #9ca3af;
  margin: 0 0 16px 0;
}

.newsletter-form {
  display: flex;
  gap: 12px;
  max-width: 400px;
}

.newsletter-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.875rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.newsletter-input:focus {
  outline: none;
  border-color: #f59e0b;
  background: rgba(255, 255, 255, 0.15);
}

.newsletter-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.social-links {
  display: flex;
  gap: 16px;
}

.social-link {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d1d5db;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #10b981 100%);
  color: white;
  transform: translateY(-2px);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.link-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.link-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
}

.link-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-link {
  color: #9ca3af;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
  position: relative;
}

.footer-link:hover {
  color: #f59e0b;
}

.footer-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: #f59e0b;
  transition: width 0.3s ease;
}

.footer-link:hover::after {
  width: 100%;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 32px 0;
}

.footer-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(245, 158, 11, 0.3) 50%, transparent 100%);
  margin-bottom: 32px;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  font-size: 0.875rem;
  color: #9ca3af;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 24px;
}

.footer-bottom-link {
  font-size: 0.875rem;
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom-link:hover {
  color: #f59e0b;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .footer-section {
    padding: 60px 0 0;
  }
  
  .footer-container {
    padding: 0 20px;
  }
  
  .footer-main {
    gap: 40px;
    margin-bottom: 40px;
  }
  
  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }
  
  .brand-name {
    font-size: 2rem;
  }
  
  .brand-description {
    font-size: 0.95rem;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .footer-bottom-links {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .footer-section {
    padding: 40px 0 0;
  }
  
  .footer-container {
    padding: 0 15px;
  }
  
  .footer-main {
    gap: 32px;
    margin-bottom: 32px;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .brand-name {
    font-size: 1.75rem;
  }
  
  .brand-description {
    font-size: 0.9rem;
  }
  
  .social-links {
    gap: 12px;
  }
  
  .social-link {
    width: 40px;
    height: 40px;
  }
  
  .link-title {
    font-size: 1rem;
  }
  
  .footer-link {
    font-size: 0.9rem;
  }
  
  .footer-bottom {
    padding: 24px 0;
  }
  
  .copyright,
  .footer-bottom-link {
    font-size: 0.8rem;
  }
  
  .footer-bottom-links {
    gap: 12px;
  }
}
