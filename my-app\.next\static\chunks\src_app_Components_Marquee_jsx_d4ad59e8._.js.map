{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Santhosh/Flow/my-app/src/app/Components/Marquee.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport \"@/Style/Marquee.css\";\r\n\r\n\r\nconst Marquee = () => {\r\n    const wrapperRef = useRef(null);\r\n    const loopRef = useRef(null);\r\n\r\n    const colors = [\"#f38630\", \"#6fb936\", \"#ccc\", \"#6fb936\"];\r\n\r\n    useEffect(() => {\r\n        const boxes = gsap.utils.toArray(\".box\");\r\n\r\n        // Apply background color\r\n        gsap.set(boxes, {\r\n            backgroundColor: gsap.utils.wrap(colors),\r\n        });\r\n\r\n        // Call horizontalLoop with the boxes\r\n        loopRef.current = horizontalLoop(boxes, {\r\n            paused: false,\r\n            repeat: -1,\r\n            speed: 1,\r\n        });\r\n\r\n        return () => {\r\n            loopRef.current?.kill();\r\n        };\r\n    }, []);\r\n    function horizontalLoop(items, config) {\r\n        items = gsap.utils.toArray(items);\r\n        config = config || {};\r\n        let tl = gsap.timeline({\r\n            repeat: config.repeat,\r\n            paused: config.paused,\r\n            defaults: { ease: \"none\" },\r\n            onReverseComplete: () => tl.totalTime(tl.rawTime() + tl.duration() * 100),\r\n        }),\r\n            length = items.length,\r\n            startX = items[0].offsetLeft,\r\n            times = [],\r\n            widths = [],\r\n            xPercents = [],\r\n            curIndex = 0,\r\n            pixelsPerSecond = (config.speed || 1) * 100,\r\n            snap = config.snap === false\r\n                ? (v) => v\r\n                : gsap.utils.snap(config.snap || 1),\r\n            totalWidth,\r\n            curX,\r\n            distanceToStart,\r\n            distanceToLoop,\r\n            item,\r\n            i;\r\n\r\n        gsap.set(items, {\r\n            xPercent: (i, el) => {\r\n                let w = (widths[i] = parseFloat(gsap.getProperty(el, \"width\", \"px\")));\r\n                xPercents[i] = snap(\r\n                    (parseFloat(gsap.getProperty(el, \"x\", \"px\")) / w) * 100 +\r\n                    gsap.getProperty(el, \"xPercent\")\r\n                );\r\n                return xPercents[i];\r\n            },\r\n        });\r\n        gsap.set(items, { x: 0 });\r\n        totalWidth =\r\n            items[length - 1].offsetLeft +\r\n            (xPercents[length - 1] / 100) * widths[length - 1] -\r\n            startX +\r\n            items[length - 1].offsetWidth * gsap.getProperty(items[length - 1], \"scaleX\") +\r\n            (parseFloat(config.paddingRight) || 0);\r\n\r\n        for (i = 0; i < length; i++) {\r\n            item = items[i];\r\n            curX = (xPercents[i] / 100) * widths[i];\r\n            distanceToStart = item.offsetLeft + curX - startX;\r\n            distanceToLoop = distanceToStart + widths[i] * gsap.getProperty(item, \"scaleX\");\r\n\r\n            tl.to(\r\n                item,\r\n                {\r\n                    xPercent: snap(((curX - distanceToLoop) / widths[i]) * 100),\r\n                    duration: distanceToLoop / pixelsPerSecond,\r\n                },\r\n                0\r\n            )\r\n                .fromTo(\r\n                    item,\r\n                    {\r\n                        xPercent: snap(\r\n                            ((curX - distanceToLoop + totalWidth) / widths[i]) * 100\r\n                        ),\r\n                    },\r\n                    {\r\n                        xPercent: xPercents[i],\r\n                        duration:\r\n                            (curX - distanceToLoop + totalWidth - curX) / pixelsPerSecond,\r\n                        immediateRender: false,\r\n                    },\r\n                    distanceToLoop / pixelsPerSecond\r\n                )\r\n                .add(\"label\" + i, distanceToStart / pixelsPerSecond);\r\n\r\n            times[i] = distanceToStart / pixelsPerSecond;\r\n        }\r\n\r\n        function toIndex(index, vars) {\r\n            vars = vars || {};\r\n            if (Math.abs(index - curIndex) > length / 2) {\r\n                index += index > curIndex ? -length : length;\r\n            }\r\n            let newIndex = gsap.utils.wrap(0, length, index),\r\n                time = times[newIndex];\r\n            if ((time > tl.time()) !== index > curIndex) {\r\n                vars.modifiers = { time: gsap.utils.wrap(0, tl.duration()) };\r\n                time += tl.duration() * (index > curIndex ? 1 : -1);\r\n            }\r\n            curIndex = newIndex;\r\n            vars.overwrite = true;\r\n            return tl.tweenTo(time, vars);\r\n        }\r\n\r\n        tl.next = (vars) => toIndex(curIndex + 1, vars);\r\n        tl.previous = (vars) => toIndex(curIndex - 1, vars);\r\n        tl.current = () => curIndex;\r\n        tl.toIndex = (index, vars) => toIndex(index, vars);\r\n        tl.times = times;\r\n        tl.progress(1, true).progress(0, true);\r\n        if (config.reversed) {\r\n            tl.vars.onReverseComplete();\r\n            tl.reverse();\r\n        }\r\n        return tl;\r\n    }\r\n\r\n\r\n\r\n    return (\r\n        <div >\r\n            <div className=\"wrapper\" ref={wrapperRef}>\r\n                {[...Array(11).keys()].map((i) => (\r\n                    <div key={i} className=\"box\" >\r\n                        <div\r\n                            className={i + 1 === 8 ? \"test-2\" : \"test\"}                        >\r\n                            {i + 1}\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Marquee;"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;;AAMA,MAAM,UAAU;;IACZ,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,MAAM,SAAS;QAAC;QAAW;QAAW;QAAQ;KAAU;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,MAAM,QAAQ,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YAEjC,yBAAyB;YACzB,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;gBACZ,iBAAiB,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACrC;YAEA,qCAAqC;YACrC,QAAQ,OAAO,GAAG,eAAe,OAAO;gBACpC,QAAQ;gBACR,QAAQ,CAAC;gBACT,OAAO;YACX;YAEA;qCAAO;oBACH,QAAQ,OAAO,EAAE;gBACrB;;QACJ;4BAAG,EAAE;IACL,SAAS,eAAe,KAAK,EAAE,MAAM;QACjC,QAAQ,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QAC3B,SAAS,UAAU,CAAC;QACpB,IAAI,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;YACnB,QAAQ,OAAO,MAAM;YACrB,QAAQ,OAAO,MAAM;YACrB,UAAU;gBAAE,MAAM;YAAO;YACzB,mBAAmB,IAAM,GAAG,SAAS,CAAC,GAAG,OAAO,KAAK,GAAG,QAAQ,KAAK;QACzE,IACI,SAAS,MAAM,MAAM,EACrB,SAAS,KAAK,CAAC,EAAE,CAAC,UAAU,EAC5B,QAAQ,EAAE,EACV,SAAS,EAAE,EACX,YAAY,EAAE,EACd,WAAW,GACX,kBAAkB,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,KACxC,OAAO,OAAO,IAAI,KAAK,QACjB,CAAC,IAAM,IACP,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,IACrC,YACA,MACA,iBACA,gBACA,MACA;QAEJ,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;YACZ,UAAU,CAAC,GAAG;gBACV,IAAI,IAAK,MAAM,CAAC,EAAE,GAAG,WAAW,gJAAA,CAAA,OAAI,CAAC,WAAW,CAAC,IAAI,SAAS;gBAC9D,SAAS,CAAC,EAAE,GAAG,KACX,AAAC,WAAW,gJAAA,CAAA,OAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,IAAK,MACpD,gJAAA,CAAA,OAAI,CAAC,WAAW,CAAC,IAAI;gBAEzB,OAAO,SAAS,CAAC,EAAE;YACvB;QACJ;QACA,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;YAAE,GAAG;QAAE;QACvB,aACI,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,GAC5B,AAAC,SAAS,CAAC,SAAS,EAAE,GAAG,MAAO,MAAM,CAAC,SAAS,EAAE,GAClD,SACA,KAAK,CAAC,SAAS,EAAE,CAAC,WAAW,GAAG,gJAAA,CAAA,OAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,YACpE,CAAC,WAAW,OAAO,YAAY,KAAK,CAAC;QAEzC,IAAK,IAAI,GAAG,IAAI,QAAQ,IAAK;YACzB,OAAO,KAAK,CAAC,EAAE;YACf,OAAO,AAAC,SAAS,CAAC,EAAE,GAAG,MAAO,MAAM,CAAC,EAAE;YACvC,kBAAkB,KAAK,UAAU,GAAG,OAAO;YAC3C,iBAAiB,kBAAkB,MAAM,CAAC,EAAE,GAAG,gJAAA,CAAA,OAAI,CAAC,WAAW,CAAC,MAAM;YAEtE,GAAG,EAAE,CACD,MACA;gBACI,UAAU,KAAK,AAAC,CAAC,OAAO,cAAc,IAAI,MAAM,CAAC,EAAE,GAAI;gBACvD,UAAU,iBAAiB;YAC/B,GACA,GAEC,MAAM,CACH,MACA;gBACI,UAAU,KACN,AAAC,CAAC,OAAO,iBAAiB,UAAU,IAAI,MAAM,CAAC,EAAE,GAAI;YAE7D,GACA;gBACI,UAAU,SAAS,CAAC,EAAE;gBACtB,UACI,CAAC,OAAO,iBAAiB,aAAa,IAAI,IAAI;gBAClD,iBAAiB;YACrB,GACA,iBAAiB,iBAEpB,GAAG,CAAC,UAAU,GAAG,kBAAkB;YAExC,KAAK,CAAC,EAAE,GAAG,kBAAkB;QACjC;QAEA,SAAS,QAAQ,KAAK,EAAE,IAAI;YACxB,OAAO,QAAQ,CAAC;YAChB,IAAI,KAAK,GAAG,CAAC,QAAQ,YAAY,SAAS,GAAG;gBACzC,SAAS,QAAQ,WAAW,CAAC,SAAS;YAC1C;YACA,IAAI,WAAW,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,QACtC,OAAO,KAAK,CAAC,SAAS;YAC1B,IAAI,AAAC,OAAO,GAAG,IAAI,OAAQ,QAAQ,UAAU;gBACzC,KAAK,SAAS,GAAG;oBAAE,MAAM,gJAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ;gBAAI;gBAC3D,QAAQ,GAAG,QAAQ,KAAK,CAAC,QAAQ,WAAW,IAAI,CAAC,CAAC;YACtD;YACA,WAAW;YACX,KAAK,SAAS,GAAG;YACjB,OAAO,GAAG,OAAO,CAAC,MAAM;QAC5B;QAEA,GAAG,IAAI,GAAG,CAAC,OAAS,QAAQ,WAAW,GAAG;QAC1C,GAAG,QAAQ,GAAG,CAAC,OAAS,QAAQ,WAAW,GAAG;QAC9C,GAAG,OAAO,GAAG,IAAM;QACnB,GAAG,OAAO,GAAG,CAAC,OAAO,OAAS,QAAQ,OAAO;QAC7C,GAAG,KAAK,GAAG;QACX,GAAG,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,GAAG;QACjC,IAAI,OAAO,QAAQ,EAAE;YACjB,GAAG,IAAI,CAAC,iBAAiB;YACzB,GAAG,OAAO;QACd;QACA,OAAO;IACX;IAIA,qBACI,6LAAC;kBACG,cAAA,6LAAC;YAAI,WAAU;YAAU,KAAK;sBACzB;mBAAI,MAAM,IAAI,IAAI;aAAG,CAAC,GAAG,CAAC,CAAC,kBACxB,6LAAC;oBAAY,WAAU;8BACnB,cAAA,6LAAC;wBACG,WAAW,IAAI,MAAM,IAAI,WAAW;kCACnC,IAAI;;;;;;mBAHH;;;;;;;;;;;;;;;AAU9B;GApJM;KAAA;uCAsJS", "debugId": null}}]}