/* Pricing Section */
.pricing-section {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  padding: 120px 20px;
  min-height: 100vh;
}

.pricing-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.pricing-header {
  text-align: center;
  margin-bottom: 60px;
}

.pricing-title {
  font-size: 4rem;
  font-weight: 700;
  color: #581c87;
  margin: 0 0 16px 0;
  font-family: 'Georgia', serif;
}

.pricing-subtitle {
  font-size: 1.2rem;
  color: #7c3aed;
  margin: 0 0 40px 0;
  line-height: 1.6;
}

.billing-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px;
  border-radius: 50px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.2);
  width: fit-content;
  margin: 0 auto;
}

.toggle-label {
  font-size: 1rem;
  font-weight: 600;
  color: #6b7280;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-label.active {
  color: #7c3aed;
}

.discount-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.toggle-switch {
  position: relative;
  width: 60px;
  height: 32px;
  background: #e5e7eb;
  border-radius: 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.toggle-switch:hover {
  background: #d1d5db;
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

.toggle-slider.yearly {
  transform: translateX(28px);
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 60px;
}

.pricing-card {
  background: white;
  border-radius: 20px;
  padding: 32px 24px;
  position: relative;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.pricing-card.popular {
  border-color: #7c3aed;
  transform: scale(1.05);
  box-shadow: 0 8px 30px rgba(124, 58, 237, 0.2);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  color: white;
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.plan-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price {
  font-size: 2.5rem;
  font-weight: 700;
  color: #7c3aed;
}

.period {
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
}

.price-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.original-price {
  font-size: 0.875rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.savings {
  font-size: 0.75rem;
  background: #dcfce7;
  color: #166534;
  padding: 2px 8px;
  border-radius: 8px;
  font-weight: 600;
}

.card-features {
  margin-bottom: 32px;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

.check-icon {
  color: #10b981;
  font-weight: 700;
  font-size: 1rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.card-footer {
  text-align: center;
}

.cta-button {
  width: 100%;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.cta-button.primary {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  color: white;
  border-color: #7c3aed;
}

.cta-button.primary:hover {
  background: linear-gradient(135deg, #6d28d9 0%, #4c1d95 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
}

.cta-button.outline {
  background: transparent;
  color: #7c3aed;
  border-color: #7c3aed;
}

.cta-button.outline:hover {
  background: #7c3aed;
  color: white;
  transform: translateY(-1px);
}

.pricing-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.6);
  padding: 24px 32px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.1);
}

.money-back-guarantee {
  display: flex;
  align-items: center;
  gap: 16px;
}

.guarantee-icon {
  font-size: 2rem;
}

.guarantee-text strong {
  color: #1f2937;
  font-size: 1rem;
  display: block;
  margin-bottom: 4px;
}

.guarantee-text p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.4;
}

.contact-sales-btn {
  background: transparent;
  color: #7c3aed;
  border: 2px solid #7c3aed;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.contact-sales-btn:hover {
  background: #7c3aed;
  color: white;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .pricing-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .pricing-card.popular {
    transform: none;
  }

  .pricing-footer {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .pricing-section {
    padding: 80px 20px;
  }

  .pricing-title {
    font-size: 2.5rem;
  }

  .pricing-subtitle {
    font-size: 1rem;
  }

  .billing-toggle {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .toggle-label {
    font-size: 0.875rem;
  }

  .discount-badge {
    font-size: 0.7rem;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pricing-card {
    padding: 24px 20px;
  }

  .plan-title {
    font-size: 1.25rem;
  }

  .price {
    font-size: 2rem;
  }

  .money-back-guarantee {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .pricing-section {
    padding: 60px 15px;
  }

  .pricing-title {
    font-size: 2rem;
  }

  .pricing-header {
    margin-bottom: 40px;
  }

  .billing-toggle {
    width: 100%;
    max-width: 300px;
  }

  .pricing-card {
    padding: 20px 16px;
  }

  .price {
    font-size: 1.75rem;
  }

  .feature-item {
    font-size: 0.8rem;
  }

  .pricing-footer {
    padding: 20px;
  }

  .guarantee-text strong {
    font-size: 0.875rem;
  }

  .guarantee-text p {
    font-size: 0.8rem;
  }
}
