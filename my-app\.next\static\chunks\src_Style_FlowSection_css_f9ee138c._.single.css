/* [project]/src/Style/FlowSection.css [app-client] (css) */
.flow-section {
  color: #fff;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  align-items: center;
  min-height: 100vh;
  padding: 120px 20px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.flow-section:before {
  content: "";
  pointer-events: none;
  background: radial-gradient(circle at 30% 20%, #f59e0b1a 0%, #0000 50%), radial-gradient(circle at 70% 80%, #10b9811a 0%, #0000 50%);
  position: absolute;
  inset: 0;
}

.flow-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.flow-content {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 60px;
  max-width: 100%;
  display: grid;
}

.flow-left {
  flex-direction: column;
  gap: 40px;
  display: flex;
}

.flow-title {
  word-wrap: break-word;
  text-shadow: 0 4px 20px #0000004d;
  z-index: 1;
  max-width: 100%;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 400;
  line-height: 1.1;
  position: relative;
}

.profession-tags {
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  max-width: 100%;
  display: flex;
}

.profession-tag {
  color: #fff;
  cursor: pointer;
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 25px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all .3s;
}

.profession-tag:hover {
  background: #fff3;
  border-color: #fff6;
  transform: translateY(-2px);
}

.flow-right {
  flex-direction: column;
  align-items: flex-end;
  gap: 40px;
  display: flex;
}

.flow-accessibility {
  text-align: left;
  max-width: 400px;
}

.accessibility-title {
  color: #fff;
  margin-bottom: 16px;
  font-size: 2rem;
  font-weight: 600;
}

.accessibility-description {
  color: #fffc;
  margin-bottom: 24px;
  font-size: 1.1rem;
  line-height: 1.6;
}

.get-started-btn {
  color: #fff;
  cursor: pointer;
  background: #6366f1;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.get-started-btn:hover {
  background: #5855eb;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #6366f14d;
}

.flow-illustration {
  width: 300px;
  height: 300px;
  position: relative;
}

.character-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.character {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.character-head {
  background: #ff9ff3;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  margin: 0 auto 10px;
  position: relative;
}

.character-face {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.eye {
  background: #2a2a2a;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  position: absolute;
}

.left-eye {
  top: -2px;
  left: -12px;
}

.right-eye {
  top: -2px;
  right: -12px;
}

.mouth {
  border: 2px solid #2a2a2a;
  border-top: none;
  border-radius: 0 0 12px 12px;
  width: 12px;
  height: 6px;
  position: absolute;
  top: 8px;
  left: -6px;
}

.character-ears {
  width: 100%;
  position: absolute;
  top: 10px;
}

.ear {
  background: #ff9ff3;
  border-radius: 50%;
  width: 20px;
  height: 25px;
  position: absolute;
}

.left-ear {
  left: -10px;
  transform: rotate(-20deg);
}

.right-ear {
  right: -10px;
  transform: rotate(20deg);
}

.character-body {
  background: #ff9ff3;
  border-radius: 30px;
  width: 60px;
  height: 80px;
  margin: 0 auto;
  position: relative;
}

.character-arms {
  width: 100%;
  position: absolute;
  top: 20px;
}

.arm {
  background: #ff9ff3;
  border-radius: 4px;
  width: 30px;
  height: 8px;
  position: absolute;
}

.left-arm {
  left: -25px;
  transform: rotate(-30deg);
}

.right-arm {
  right: -25px;
  transform: rotate(30deg);
}

.speech-bubble {
  background: #fff;
  border-radius: 15px;
  width: 120px;
  height: 80px;
  padding: 15px;
  position: absolute;
  top: -20px;
  right: -80px;
  box-shadow: 0 4px 20px #0000001a;
}

.speech-bubble:before {
  content: "";
  border: 10px solid #0000;
  border-left-width: 0;
  border-right-color: #fff;
  width: 0;
  height: 0;
  position: absolute;
  top: 30px;
  left: -10px;
}

.bubble-content {
  flex-direction: column;
  justify-content: center;
  height: 100%;
  display: flex;
}

.text-lines {
  flex-direction: column;
  gap: 6px;
  display: flex;
}

.text-line {
  background: #ff9500;
  border-radius: 2px;
  height: 3px;
}

.text-line.short {
  width: 60%;
}

@media (width <= 1024px) {
  .flow-content {
    gap: 60px;
  }

  .flow-title {
    font-size: 3.5rem;
  }

  .flow-illustration {
    width: 250px;
    height: 250px;
  }
}

@media (width <= 768px) {
  .flow-section {
    padding: 80px 20px;
  }

  .flow-content {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .flow-title {
    font-size: 3rem;
  }

  .flow-right {
    align-items: center;
  }

  .flow-accessibility {
    text-align: center;
  }

  .profession-tags {
    justify-content: center;
  }

  .flow-illustration {
    width: 200px;
    height: 200px;
  }

  .speech-bubble {
    width: 100px;
    height: 60px;
    padding: 10px;
    right: -60px;
  }
}

@media (width <= 480px) {
  .flow-section {
    padding: 60px 15px;
  }

  .flow-title {
    font-size: 2.5rem;
  }

  .accessibility-title {
    font-size: 1.5rem;
  }

  .accessibility-description {
    font-size: 1rem;
  }

  .profession-tag {
    padding: 6px 12px;
    font-size: 12px;
  }

  .flow-illustration {
    width: 180px;
    height: 180px;
  }

  .character-head {
    width: 60px;
    height: 60px;
  }

  .character-body {
    width: 45px;
    height: 60px;
  }
}

/*# sourceMappingURL=src_Style_FlowSection_css_f9ee138c._.single.css.map*/