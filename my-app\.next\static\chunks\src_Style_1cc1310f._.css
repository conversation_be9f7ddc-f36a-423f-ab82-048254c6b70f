/* [project]/src/Style/PricingSection.css [app-client] (css) */
.pricing-section {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  min-height: 100vh;
  padding: 120px 20px;
}

.pricing-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.pricing-header {
  text-align: center;
  margin-bottom: 60px;
}

.pricing-title {
  color: #581c87;
  margin: 0 0 16px;
  font-family: Georgia, serif;
  font-size: 4rem;
  font-weight: 700;
}

.pricing-subtitle {
  color: #7c3aed;
  margin: 0 0 40px;
  font-size: 1.2rem;
  line-height: 1.6;
}

.billing-toggle {
  backdrop-filter: blur(10px);
  background: #fffc;
  border: 1px solid #8b5cf633;
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  gap: 20px;
  width: fit-content;
  margin: 0 auto;
  padding: 8px;
  display: flex;
}

.toggle-label {
  color: #6b7280;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 600;
  transition: color .3s;
  display: flex;
}

.toggle-label.active {
  color: #7c3aed;
}

.discount-badge {
  color: #fff;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: .75rem;
  font-weight: 600;
}

.toggle-switch {
  cursor: pointer;
  background: #e5e7eb;
  border-radius: 16px;
  width: 60px;
  height: 32px;
  transition: background .3s;
  position: relative;
}

.toggle-switch:hover {
  background: #d1d5db;
}

.toggle-slider {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  transition: transform .3s;
  position: absolute;
  top: 2px;
  left: 2px;
  box-shadow: 0 2px 8px #7c3aed4d;
}

.toggle-slider.yearly {
  transform: translateX(28px);
}

.pricing-cards {
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 60px;
  display: grid;
}

.pricing-card {
  background: #fff;
  border: 2px solid #0000;
  border-radius: 20px;
  padding: 32px 24px;
  transition: all .3s;
  position: relative;
  box-shadow: 0 4px 20px #00000014;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px #0000001f;
}

.pricing-card.popular {
  border-color: #7c3aed;
  transform: scale(1.05);
  box-shadow: 0 8px 30px #7c3aed33;
}

.popular-badge {
  color: #fff;
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border-radius: 20px;
  padding: 6px 20px;
  font-size: .875rem;
  font-weight: 600;
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
}

.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.plan-title {
  color: #1f2937;
  margin: 0 0 16px;
  font-size: 1.5rem;
  font-weight: 700;
}

.price-container {
  flex-direction: column;
  align-items: center;
  gap: 8px;
  display: flex;
}

.price-main {
  align-items: baseline;
  gap: 4px;
  display: flex;
}

.price {
  color: #7c3aed;
  font-size: 2.5rem;
  font-weight: 700;
}

.period {
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
}

.price-details {
  align-items: center;
  gap: 8px;
  display: flex;
}

.original-price {
  color: #9ca3af;
  font-size: .875rem;
  text-decoration: line-through;
}

.savings {
  color: #166534;
  background: #dcfce7;
  border-radius: 8px;
  padding: 2px 8px;
  font-size: .75rem;
  font-weight: 600;
}

.card-features {
  margin-bottom: 32px;
}

.features-list {
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.feature-item {
  color: #4b5563;
  align-items: flex-start;
  gap: 12px;
  font-size: .875rem;
  line-height: 1.5;
  display: flex;
}

.check-icon {
  color: #10b981;
  flex-shrink: 0;
  margin-top: 2px;
  font-size: 1rem;
  font-weight: 700;
}

.card-footer {
  text-align: center;
}

.cta-button {
  cursor: pointer;
  border: 2px solid #0000;
  border-radius: 12px;
  width: 100%;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.cta-button.primary {
  color: #fff;
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border-color: #7c3aed;
}

.cta-button.primary:hover {
  background: linear-gradient(135deg, #6d28d9 0%, #4c1d95 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px #7c3aed4d;
}

.cta-button.outline {
  color: #7c3aed;
  background: none;
  border-color: #7c3aed;
}

.cta-button.outline:hover {
  color: #fff;
  background: #7c3aed;
  transform: translateY(-1px);
}

.pricing-footer {
  backdrop-filter: blur(10px);
  background: #fff9;
  border: 1px solid #8b5cf61a;
  border-radius: 16px;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  display: flex;
}

.money-back-guarantee {
  align-items: center;
  gap: 16px;
  display: flex;
}

.guarantee-icon {
  font-size: 2rem;
}

.guarantee-text strong {
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 1rem;
  display: block;
}

.guarantee-text p {
  color: #6b7280;
  margin: 0;
  font-size: .875rem;
  line-height: 1.4;
}

.contact-sales-btn {
  color: #7c3aed;
  cursor: pointer;
  background: none;
  border: 2px solid #7c3aed;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.contact-sales-btn:hover {
  color: #fff;
  background: #7c3aed;
  transform: translateY(-1px);
}

@media (width <= 1200px) {
  .pricing-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .pricing-card.popular {
    transform: none;
  }

  .pricing-footer {
    text-align: center;
    flex-direction: column;
    gap: 20px;
  }
}

@media (width <= 768px) {
  .pricing-section {
    padding: 80px 20px;
  }

  .pricing-title {
    font-size: 2.5rem;
  }

  .pricing-subtitle {
    font-size: 1rem;
  }

  .billing-toggle {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .toggle-label {
    font-size: .875rem;
  }

  .discount-badge {
    font-size: .7rem;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pricing-card {
    padding: 24px 20px;
  }

  .plan-title {
    font-size: 1.25rem;
  }

  .price {
    font-size: 2rem;
  }

  .money-back-guarantee {
    text-align: center;
    flex-direction: column;
    gap: 12px;
  }
}

@media (width <= 480px) {
  .pricing-section {
    padding: 60px 15px;
  }

  .pricing-title {
    font-size: 2rem;
  }

  .pricing-header {
    margin-bottom: 40px;
  }

  .billing-toggle {
    width: 100%;
    max-width: 300px;
  }

  .pricing-card {
    padding: 20px 16px;
  }

  .price {
    font-size: 1.75rem;
  }

  .feature-item {
    font-size: .8rem;
  }

  .pricing-footer {
    padding: 20px;
  }

  .guarantee-text strong {
    font-size: .875rem;
  }

  .guarantee-text p {
    font-size: .8rem;
  }
}


/* [project]/src/Style/SavingsCalculator.css [app-client] (css) */
.savings-calculator-section {
  color: #fff;
  background: linear-gradient(135deg, #064e3b 0%, #047857 100%);
  min-height: 100vh;
  padding: 120px 20px;
}

.savings-container {
  flex-direction: column;
  gap: 60px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
}

.content-section {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
  display: grid;
}

.text-content {
  flex-direction: column;
  gap: 32px;
  display: flex;
}

.section-title {
  color: #fff;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.scenario-text {
  flex-direction: column;
  gap: 16px;
  display: flex;
}

.scenario-main {
  color: #d1fae5;
  margin: 0;
  font-size: 1.5rem;
  line-height: 1.4;
}

.scenario-main strong {
  color: #6ee7b7;
  font-weight: 700;
}

.scenario-detail {
  color: #a7f3d0;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.value-statement {
  color: #d1fae5;
  margin: 0;
  font-size: 1.25rem;
  line-height: 1.4;
}

.value-statement strong {
  color: #6ee7b7;
  font-weight: 700;
}

.input-controls {
  flex-direction: column;
  gap: 24px;
  display: flex;
}

.input-group {
  flex-direction: column;
  gap: 12px;
  display: flex;
}

.input-group label {
  color: #a7f3d0;
  font-size: 1rem;
  font-weight: 600;
}

.slider {
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  background: #065f46;
  border-radius: 4px;
  outline: none;
  width: 100%;
  height: 8px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  transition: all .3s;
  box-shadow: 0 2px 8px #10b9814d;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px #10b98166;
}

.slider::-moz-range-thumb {
  cursor: pointer;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  box-shadow: 0 2px 8px #10b9814d;
}

.slider-value {
  color: #6ee7b7;
  text-align: center;
  background: #10b98133;
  border-radius: 20px;
  align-self: center;
  width: fit-content;
  padding: 8px 16px;
  font-size: 1.125rem;
  font-weight: 700;
}

.illustration-section {
  justify-content: center;
  align-items: center;
  display: flex;
}

.character-illustration {
  justify-content: center;
  align-items: center;
  width: 300px;
  height: 300px;
  display: flex;
  position: relative;
}

.character {
  z-index: 2;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  display: flex;
}

.character-head {
  flex-direction: column;
  align-items: center;
  gap: 8px;
  display: flex;
  position: relative;
}

.face {
  font-size: 4rem;
  animation: 2s ease-in-out infinite bounce;
}

.hat {
  font-size: 2rem;
  animation: 3s ease-in-out infinite tilt;
  position: absolute;
  top: -20px;
}

.character-body {
  justify-content: center;
  display: flex;
}

.arms {
  gap: 40px;
  display: flex;
}

.arm {
  font-size: 2rem;
  animation: 1.5s ease-in-out infinite juggle;
}

.arm.left {
  animation-delay: 0s;
}

.arm.right {
  animation-delay: .75s;
}

.money-animation {
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
}

.money-icon {
  font-size: 2rem;
  animation: 3s ease-in-out infinite float;
  position: absolute;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes tilt {
  0%, 100% {
    transform: rotate(-5deg);
  }

  50% {
    transform: rotate(5deg);
  }
}

@keyframes juggle {
  0%, 100% {
    transform: translateY(0)rotate(0);
  }

  50% {
    transform: translateY(-15px)rotate(10deg);
  }
}

@keyframes float {
  0%, 100% {
    opacity: .8;
    transform: translateY(0)rotate(0);
  }

  50% {
    opacity: 1;
    transform: translateY(-20px)rotate(180deg);
  }
}

.calculator-section {
  justify-content: center;
  display: flex;
}

.calculator-card {
  backdrop-filter: blur(20px);
  background: #ffffff1a;
  border: 1px solid #fff3;
  border-radius: 24px;
  width: 100%;
  max-width: 500px;
  padding: 40px;
  box-shadow: 0 8px 32px #0000004d;
}

.calculator-header {
  text-align: center;
  margin-bottom: 32px;
}

.calculator-header h3 {
  color: #a7f3d0;
  margin: 0 0 16px;
  font-size: 1.5rem;
  font-weight: 600;
}

.savings-amount {
  color: #6ee7b7;
  justify-content: center;
  align-items: baseline;
  gap: 8px;
  font-size: 3rem;
  font-weight: 700;
  display: flex;
}

.period {
  color: #a7f3d0;
  font-size: 1.5rem;
  font-weight: 500;
}

.breakdown {
  border-top: 1px solid #fff3;
  border-bottom: 1px solid #fff3;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px 0;
  display: flex;
}

.breakdown-item {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.breakdown-item .label {
  color: #d1fae5;
  font-size: 1rem;
}

.breakdown-item .value {
  color: #6ee7b7;
  font-size: 1rem;
  font-weight: 600;
}

.breakdown-item .value.negative {
  color: #fca5a5;
}

.calculator-footer {
  text-align: center;
}

.get-started-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  margin: 0 auto;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 15px #10b9814d;
}

.get-started-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #10b98166;
}

.arrow {
  transition: transform .3s;
}

.get-started-btn:hover .arrow {
  transform: translateX(4px);
}

@media (width <= 1024px) {
  .content-section {
    gap: 60px;
  }

  .section-title {
    font-size: 3rem;
  }

  .character-illustration {
    width: 250px;
    height: 250px;
  }

  .face {
    font-size: 3rem;
  }

  .calculator-card {
    padding: 32px;
  }
}

@media (width <= 768px) {
  .savings-calculator-section {
    padding: 80px 20px;
  }

  .content-section {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .scenario-main {
    font-size: 1.25rem;
  }

  .value-statement {
    font-size: 1.125rem;
  }

  .character-illustration {
    width: 200px;
    height: 200px;
  }

  .face {
    font-size: 2.5rem;
  }

  .hat, .arm, .money-icon {
    font-size: 1.5rem;
  }

  .calculator-card {
    margin: 0 20px;
    padding: 24px;
  }

  .savings-amount {
    font-size: 2.5rem;
  }
}

@media (width <= 480px) {
  .savings-calculator-section {
    padding: 60px 15px;
  }

  .section-title {
    font-size: 2rem;
  }

  .scenario-main {
    font-size: 1.125rem;
  }

  .scenario-detail {
    font-size: .875rem;
  }

  .value-statement {
    font-size: 1rem;
  }

  .character-illustration {
    width: 150px;
    height: 150px;
  }

  .face {
    font-size: 2rem;
  }

  .calculator-card {
    margin: 0 10px;
    padding: 20px;
  }

  .savings-amount {
    font-size: 2rem;
  }

  .calculator-header h3 {
    font-size: 1.25rem;
  }

  .breakdown-item .label, .breakdown-item .value {
    font-size: .875rem;
  }

  .get-started-btn {
    padding: 14px 24px;
    font-size: 1rem;
  }
}


/* [project]/src/Style/FAQSection.css [app-client] (css) */
.faq-section {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  min-height: 100vh;
  padding: 120px 20px;
}

.faq-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.faq-header {
  text-align: center;
  margin-bottom: 60px;
}

.faq-title {
  color: #92400e;
  margin: 0;
  font-family: Georgia, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.faq-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 80px;
  display: grid;
}

.faq-item {
  background: #fff;
  border: 2px solid #0000;
  border-radius: 16px;
  transition: all .3s;
  overflow: hidden;
  box-shadow: 0 4px 20px #00000014;
}

.faq-item:hover {
  border-color: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 8px 30px #0000001f;
}

.faq-item.active {
  border-color: #f59e0b;
  box-shadow: 0 8px 30px #f59e0b33;
}

.faq-question {
  cursor: pointer;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  padding: 24px;
  transition: background-color .3s;
  display: flex;
}

.faq-question:hover {
  background-color: #fef3c7;
}

.faq-item.active .faq-question {
  background-color: #fef3c7;
  border-bottom: 1px solid #fde68a;
}

.faq-question h3 {
  color: #1f2937;
  flex: 1;
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
}

.faq-icon {
  color: #f59e0b;
  background: #f59e0b1a;
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  transition: all .3s;
  display: flex;
}

.faq-answer {
  opacity: 0;
  height: 0;
  transition: all .4s;
  overflow: hidden;
}

.faq-item.active .faq-answer {
  opacity: 1;
  height: auto;
}

.faq-answer p {
  color: #4b5563;
  margin: 0;
  padding: 0 24px 24px;
  font-size: 1rem;
  line-height: 1.6;
}

.faq-footer {
  text-align: center;
  backdrop-filter: blur(10px);
  background: #fff9;
  border: 1px solid #f59e0b33;
  border-radius: 20px;
  padding: 48px 32px;
}

.contact-support h3 {
  color: #92400e;
  margin: 0 0 16px;
  font-size: 2rem;
  font-weight: 700;
}

.contact-support p {
  color: #78350f;
  margin: 0 0 32px;
  font-size: 1.125rem;
  line-height: 1.6;
}

.contact-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  border-radius: 12px;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
  display: inline-flex;
  box-shadow: 0 4px 15px #f59e0b4d;
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #f59e0b66;
}

.arrow {
  transition: transform .3s;
}

.contact-btn:hover .arrow {
  transform: translateX(4px);
}

@media (width <= 1024px) {
  .faq-title {
    font-size: 3rem;
  }

  .faq-grid {
    gap: 20px;
  }

  .contact-support h3 {
    font-size: 1.75rem;
  }
}

@media (width <= 768px) {
  .faq-section {
    padding: 80px 20px;
  }

  .faq-title {
    font-size: 2.5rem;
  }

  .faq-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .faq-question {
    padding: 20px;
  }

  .faq-question h3 {
    font-size: 1rem;
  }

  .faq-answer p {
    padding: 0 20px 20px;
    font-size: .875rem;
  }

  .faq-footer {
    padding: 32px 24px;
  }

  .contact-support h3 {
    font-size: 1.5rem;
  }

  .contact-support p {
    font-size: 1rem;
  }
}

@media (width <= 480px) {
  .faq-section {
    padding: 60px 15px;
  }

  .faq-title {
    font-size: 2rem;
  }

  .faq-question {
    padding: 16px;
  }

  .faq-question h3 {
    font-size: .875rem;
  }

  .faq-icon {
    width: 28px;
    height: 28px;
  }

  .faq-answer p {
    padding: 0 16px 16px;
    font-size: .8rem;
  }

  .faq-footer {
    padding: 24px 20px;
  }

  .contact-support h3 {
    font-size: 1.25rem;
  }

  .contact-support p {
    font-size: .875rem;
  }

  .contact-btn {
    padding: 14px 24px;
    font-size: 1rem;
  }
}


/*# sourceMappingURL=src_Style_1cc1310f._.css.map*/